BEGIN;

ALTER TABLE public.ctn_form_fields ADD field_parent_id varchar(40) NULL;
ALTER TABLE public.ctn_form_fields ADD has_attachment bool NULL;
ALTER TABLE public.ctn_form_fields ADD remark varchar(255) NULL;
ALTER TABLE public.ctn_form_fields ALTER COLUMN form_field_type_code TYPE varchar(100) USING form_field_type_code::varchar(100);

ALTER TABLE public."ctn_FORM_FIELD_TYPES" ALTER COLUMN code TYPE varchar(100) USING code::varchar(100);
ALTER TABLE public."ctn_FORM_FIELD_TYPES" ALTER COLUMN "label" TYPE varchar(100) USING "label"::varchar(100);
ALTER TABLE public."ctn_FORM_FIELD_TYPES" ALTER COLUMN description TYPE varchar(100) USING description::varchar(100);

INSERT INTO "ctn_FORM_FIELD_TYPES" (code, "label", description) VALUES('CONDITIONAL_GRID', 'Conditional Grid', 'Conditional Grid');
INSERT INTO "ctn_FORM_FIELD_TYPES" (code, "label", description) VALUES('CONDITIONAL_GRID_CHILDREN', 'Conditional Grid Children', 'Conditional Grid Childre');

ALTER TABLE public.ctn_form_template_fields ADD field_parent_id varchar(40) NULL;
ALTER TABLE public.ctn_form_template_fields ADD has_attachment bool NULL;
ALTER TABLE public.ctn_form_template_fields ADD remark varchar(255) NULL;
ALTER TABLE public.ctn_form_template_fields ALTER COLUMN form_field_type_code TYPE varchar(100) USING form_field_type_code::varchar(100);

COMMIT;