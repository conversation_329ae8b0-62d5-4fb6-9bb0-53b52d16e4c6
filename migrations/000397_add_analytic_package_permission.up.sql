BEGIN;

ALTER TABLE public."uis_ANALYTICS" ADD show_on_client_package_codes _varchar NULL;
ALTER TABLE public."uis_ANALYTICS" ADD show_on_user_permission_codes _varchar NULL;

UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='TOTAL_INSPECTIONS_BY_INSPECTORS_OVER_TIME';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='TOP_5_TYRE_BRANDS_BY_SIZE_DISTRIBUTION';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='TOP_5_VEHICLE_BRANDS';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='INSPECTIONS_LOCATION';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='ON_HOLD_WORK_ORDERS';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='ASSETS_WITH_OPERATIONAL_ALERTS';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='ASSETS_UNDER_MAINTENANCE';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='ASSET_STATUS';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='WORK_ORDER_STATUS';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='ASSET_BRANDS';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='ASSET_CATEGORIES_&_SUBCATEGORIES';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='ASSETS_WITH_EXPIRED_COMPONENT';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='TOTAL_WORK_ORDERS';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='UNASSIGNED_WORK_ORDERS';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='NOT_IN_PROGRESS_MORE_THAN_48_HOURS';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='OVERDUE_WORK_ORDERS';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='INSTALLED_TYRES_NOT_INSPECTED_THIS_MONTH';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='ASSET_TYRE_WITH_TYRE_ALERTS';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='TOTAL_INSPECTIONS';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='TOTAL_CUSTOMERS_INSPECTED';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='TOTAL_VEHICLES_INSPECTED';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='TOTAL_INSPECTORS';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='TOTAL_INSPECTIONS_BY_TYPE';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='VEHICLES_BASED_ON_INSPECTION_FREQUENCY';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='TOTAL_TYRES_INSPECTED_VS_VEHICLES_INSPECTED';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='TOP_5_MOST_INSPECTED_CUSTOMER';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='TOTAL_TYRES_INSPECTED';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='INSTALLED_TYRES_WITH_CRITICAL_TREAD_DEPTH';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='TOTAL_REGISTERED_CUSTOMERS';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='TOTAL_REGISTERED_VEHICLES';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='TOP_5_MOST_COMMON_VEHICLES_USED_BY_CUSTOMERS';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='AGED_IN_PROGRESS_WORK_ORDERS';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='TOTAL_ASSETS';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes='{TYRE_OPTIMAX}', show_on_user_permission_codes=NULL WHERE code='TOTAL_LINKED_TYRE_INSPECTIONS';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes='{GENERAL_TODO}', show_on_user_permission_codes=NULL WHERE code='COUNT_OVERDUE_TASKS';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes='{GENERAL_TODO}', show_on_user_permission_codes=NULL WHERE code='COUNT_OPEN_TASKS_TODAY';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes='{GENERAL_TODO}', show_on_user_permission_codes=NULL WHERE code='COUNT_OPEN_TASKS';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes='{TYRE_OPTIMAX}', show_on_user_permission_codes=NULL WHERE code='TOTAL_REGISTERED_RUNNING_TYRES_EXC_SPARES';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes='{TYRE_OPTIMAX}', show_on_user_permission_codes=NULL WHERE code='TOTAL_RUNNING_TYRES_CRITICAL_TREAD_DEPTH_EXC_SPARES';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes='{TYRE_OPTIMAX}', show_on_user_permission_codes=NULL WHERE code='VEHICLE_INSPECTIONS_INSPECTED_CUSTOMER_OVER_TIME';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes='{DIGISPECT_INSPECTION_FLOW_SCENARIO_4}', show_on_user_permission_codes=NULL WHERE code='REGISTERED_VS_EXTERNAL_VEHICLE_INSPECTIONS_THIS_MONTH';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes='{DIGISPECT_INSPECTION_FLOW_SCENARIO_4}', show_on_user_permission_codes=NULL WHERE code='NUMBER_OF_VEHICLES_INSPECTED';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes='{DIGISPECT_INSPECTION_FLOW_SCENARIO_4}', show_on_user_permission_codes=NULL WHERE code='VEHICLE_INSPECTIONS_ON_EXTERNAL_VEHICLES_OVER_TIME';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes='{TYRE_OPTIMAX}', show_on_user_permission_codes=NULL WHERE code='NUMBER_OF_REGISTERED_CUSTOMERS_BY_INSPECTION_FREQUENCY';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes='{TYRE_OPTIMAX}', show_on_user_permission_codes=NULL WHERE code='TOP_5_MOST_COMMON_TYRES_USED_BY_CUSTOMERS';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='TYRE_DEALER_TOTAL_REGISTERED_CUSTOMERS';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='TYRE_DEALER_CUSTOMER_LOCATION';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='TYRE_DEALER_TOP_5_CUSTOMERS_BY_NO_OF_VEHICLES';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='TYRE_DEALER_TOP_5_CUSTOMERS_BY_NO_OF_TYRES';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='TYRE_DEALER_TOTAL_REGISTERED_VEHICLES';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='TYRE_DEALER_REGISTERED_VS_EXTERNAL_VEHICLES';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='TYRE_DEALER_TOP_5_REGISTERED_VEHICLE_BRANDS';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='TYRE_DEALER_NUMBER_OF_OPEN_TASKS';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='TYRE_DEALER_OVERDUE_SCHEDULED_TASKS';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='TYRE_DEALER_UNSCHEDULED_TASKS';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='TYRE_DEALER_TODAYS_SCHEDULED_OPEN_TASKS';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='TYRE_DEALER_NUMBER_OF_TASKS_BY_STATUS_THIS_MONTH';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes=NULL, show_on_user_permission_codes=NULL WHERE code='TYRE_DEALER_NUMBER_OF_TASKS_BY_RELATED_ASSETS_THIS_MONTH';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes='{TYRE_OPTIMAX}', show_on_user_permission_codes=NULL WHERE code='TYRE_DEALER_REGISTERED_VEHICLES_WITH_TYRES_INSTALLED_VS_NOT_YET_INSTALLED';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes='{TYRE_OPTIMAX}', show_on_user_permission_codes=NULL WHERE code='TYRE_DEALER_TOTAL_REGISTERED_VEHICLES_BY_AXLE_TYPES';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes='{DIGISPECT_INSPECTION_FLOW_SCENARIO_4}', show_on_user_permission_codes=NULL WHERE code='TYRE_DEALER_TOP_5_EXTERNAL_VEHICLE_BRANDS';
UPDATE "uis_ANALYTICS" SET show_on_client_package_codes='{DIGISPECT_INSPECTION_FLOW_SCENARIO_4}', show_on_user_permission_codes=NULL WHERE code='TYRE_DEALER_TOTAL_EXTERNAL_VEHICLES_BY_AXLE_TYPES';

COMMIT;