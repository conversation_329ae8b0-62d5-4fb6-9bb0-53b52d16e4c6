package assetservice

import (
	approvalPersistence "assetfindr/internal/app/approval/persistence"
	approvalUsecase "assetfindr/internal/app/approval/usecase"
	"assetfindr/internal/app/asset/handler"
	"assetfindr/internal/app/asset/persistence"
	"assetfindr/internal/app/asset/routers"
	"assetfindr/internal/app/asset/usecase"
	"assetfindr/internal/app/asset/utils"
	financePresistence "assetfindr/internal/app/finance/presistence"
	financeUseCase "assetfindr/internal/app/finance/usecase"
	integrationPersistence "assetfindr/internal/app/integration/presistence"
	notificationPersistence "assetfindr/internal/app/notification/presistence"
	notificationUsecase "assetfindr/internal/app/notification/usecase"
	storagePersistence "assetfindr/internal/app/storage/persistence"
	storageUsecase "assetfindr/internal/app/storage/usecase"
	taskPersistence "assetfindr/internal/app/task/persistence"
	uploadPersistence "assetfindr/internal/app/upload/presistence"
	userIdentityPersistence "assetfindr/internal/app/user-identity/persistence"
	userUsecase "assetfindr/internal/app/user-identity/usecase"
	"assetfindr/internal/infrastructure/cloudStorage"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/internal/infrastructure/email"

	contentPersistence "assetfindr/internal/app/content/presistence"
	contentUsecase "assetfindr/internal/app/content/usecase"

	packagePersistence "assetfindr/internal/app/inventory/persistence"

	"github.com/gin-gonic/gin"
)

func InitializeAsset(dbUsecase database.DBUsecase, router *gin.Engine) error {

	// Repositories
	uploadRepo := uploadPersistence.NewUploadRepository()
	approvalRepository := approvalPersistence.NewApprovalRepository()
	userIdentityRepository := userIdentityPersistence.NewUserRepository()
	assetAssignmentRepository := persistence.NewAssetAssignmentRepository()
	assetRepository := persistence.NewAssetRepository()
	assetVehicleRepository := persistence.NewAssetVehicleRepository()
	storageRepository := storagePersistence.NewStorageRepository(cloudStorage.Bucket)
	assetTyreRepository := persistence.NewAssetTyreRepository()
	assetLinkedRepository := persistence.NewAssetLinkedRepository()
	inspectionRepository := persistence.NewAssetInspectionRepository()
	inspectionVehicleRepository := persistence.NewAssetInspectionVehicleRepository()
	inspectionTyreRepository := persistence.NewAssetInspectionTyreRepository()
	attachmentRepository := storagePersistence.NewAttachmentRepository()
	assetLogRepository := persistence.NewAssetLogRepository()
	notificationRepo := notificationPersistence.NewAssetTyreRepository()
	emailRepo := notificationPersistence.NewEmailRepository(email.GetEmailService())
	assetStatusRequestRepo := persistence.NewAssetStatusRequestRepository()
	financeRepo := financePresistence.NewFinanceRepository()
	formRepository := contentPersistence.NewFormRepository()
	materialRepo := contentPersistence.NewMaterialRepository()
	assetTransactionRepo := persistence.NewAssetTransactionRepository()
	partnerRepo := userIdentityPersistence.NewPartnerRepository()
	userRepo := userIdentityPersistence.NewUserRepository()
	brandRepo := persistence.NewBrandRepository()
	assetModelRepo := persistence.NewAssetModelRepository()
	assetVehicleBodyTypeRepo := persistence.NewAssetVehicleBodyTypeRepository()
	bonusPenaltyTargetFormationRepo := persistence.NewBonusPenaltyTargetFormationRepository()
	bonusPenaltyParameterRepo := persistence.NewBonusPenaltyParameterRepository()
	bonusPenaltyRepo := persistence.NewBonusPenaltyRepository()
	assetComponentRepo := persistence.NewAssetComponentRepository()
	packageRepository := packagePersistence.NewPackageRepository()
	ticketRepo := taskPersistence.NewTicketRepository()
	customAssetCategoryRepo := persistence.NewCustomAssetCategoryRepository()
	alertRepo := integrationPersistence.NewAlertRepository()
	integrationRepo := integrationPersistence.NewIntegrationRepository()
	vehicleTargetTyreRemovalRepo := persistence.NewVehicleTargetTyreRemovalRepository()
	assetInspectionFindingRepo := persistence.NewAssetInspectionFindingRepository()
	weightBridgeRepo := persistence.NewWeightBridgeRepository()
	workShiftRepo := userIdentityPersistence.NewWorkShiftRepository()
	haulRepo := persistence.NewHaulRepository()
	permissionRepo := userIdentityPersistence.NewPermissionRepository()

	// Use Cases
	bonusPenaltyParameterUsecase := usecase.NewBonusPenaltyParameterUseCase(dbUsecase, bonusPenaltyParameterRepo, assetTyreRepository)
	bonusPenaltyTargetFormationUsecase := usecase.NewBonusPenaltyTargetFormationUseCase(dbUsecase, bonusPenaltyTargetFormationRepo)

	// Utils
	assetInpectionUtil := utils.NewAssetInspectionUtil()

	attachmentUc := storageUsecase.NewAttachmentUseCase(dbUsecase, attachmentRepository, storageRepository)
	formUseCase := contentUsecase.NewFormUsecase(dbUsecase, formRepository, ticketRepo, attachmentUc)
	financeUsecase := financeUseCase.NewFinanceUsecase(
		dbUsecase,
		financeRepo,
	)
	notificationUc := notificationUsecase.NewNotificationUsecase(
		dbUsecase,
		notificationRepo,
		emailRepo,
		userIdentityRepository,
	)

	assetAssignmentUseCase := usecase.NewAssetAssignmentUseCase(
		dbUsecase,
		assetAssignmentRepository,
		userIdentityRepository,
		assetLinkedRepository,
		notificationUc,
		assetRepository,
		attachmentUc,
		formRepository,
		assetVehicleRepository,
		inspectionRepository,
		inspectionTyreRepository,
		inspectionVehicleRepository,
		assetInpectionUtil,
		assetTyreRepository,
	)
	storageUc := storageUsecase.NewStorageUseCase(storageRepository)
	attachmentUseCase := storageUsecase.NewAttachmentUseCase(dbUsecase, attachmentRepository, storageRepository)
	approvalUsecase := approvalUsecase.NewApprovalUseCase(dbUsecase, approvalRepository, assetRepository, userIdentityRepository, assetStatusRequestRepo, bonusPenaltyRepo, assetLinkedRepository, financeUsecase, packageRepository, assetTyreRepository)
	assetUseCase := usecase.NewAssetUseCase(
		dbUsecase,
		assetRepository,
		userIdentityRepository,
		assetVehicleRepository,
		assetLogRepository,
		assetTyreRepository,
		assetAssignmentRepository,
		attachmentUseCase,
		formUseCase,
		&approvalUsecase,
		alertRepo,
		assetLinkedRepository,
		integrationRepo,
		assetTyreRepository,
		inspectionTyreRepository,
		vehicleTargetTyreRemovalRepo,
	)
	assetUseCase.SetNotifUseCase(notificationUc)
	assetTyreUseCase := usecase.NewAssetTyreUseCase(
		dbUsecase,
		assetTyreRepository,
		assetRepository,
		assetLogRepository,
		assetAssignmentRepository,
		assetLinkedRepository,
		financeUsecase,
		*attachmentUseCase,
		userRepo,
		assetVehicleBodyTypeRepo,
		brandRepo,
		uploadRepo,
		storageUc,
		inspectionTyreRepository,
		partnerRepo,
		approvalRepository,
	)
	bonusPenaltyUsecase := usecase.NewBonusPenaltyUseCase(dbUsecase, bonusPenaltyRepo, userIdentityRepository, attachmentUseCase, approvalUsecase, assetVehicleRepository)
	assetTyreUseCase.SetNotifUseCase(notificationUc)
	assetVehicleUc := usecase.NewAssetVehicleUseCase(
		dbUsecase,
		assetRepository,
		userIdentityRepository,
		assetVehicleRepository,
		assetLogRepository,
		assetAssignmentRepository,
		financeUsecase,
		attachmentUseCase,
		assetLinkedRepository,
		assetTyreRepository,
		assetUseCase,
		storageUc,
		uploadRepo,
		assetVehicleBodyTypeRepo,
		brandRepo,
		integrationRepo,
		persistence.NewDigispectRepository(),
		inspectionVehicleRepository,
		inspectionTyreRepository,
		vehicleTargetTyreRemovalRepo,
	)

	assetStatusRequestUsecase := usecase.NewAssetStatusRequestUseCase(
		dbUsecase,
		assetStatusRequestRepo,
		assetRepository,
		approvalUsecase,
		*attachmentUseCase,
		userIdentityRepository,
		approvalRepository,
	)
	assetLinkedUseCase := usecase.NewAssetLinkedUseCase(
		dbUsecase,
		assetLinkedRepository,
		assetLogRepository,
		assetVehicleRepository,
		assetTyreRepository,
		assetRepository,
		assetAssignmentRepository,
		userRepo,
		assetVehicleUc,
		storageUc,
		uploadRepo,
		ticketRepo,
		assetStatusRequestUsecase,
	)
	inspectionUseCase := usecase.NewAssetInspectionUseCase(
		dbUsecase,
		inspectionRepository,
		assetRepository,
		inspectionVehicleRepository,
		inspectionTyreRepository,
		userIdentityRepository,
		attachmentUseCase,
		assetVehicleRepository,
		assetTyreRepository,
		assetVehicleUc,
		storageRepository,
		ticketRepo,
		assetInpectionUtil,
		notificationUc,
		assetAssignmentRepository,
		assetLinkedUseCase,
		formRepository,
		persistence.NewDigispectRepository(),
		assetLinkedRepository,
		assetInspectionFindingRepo,
	)
	locationUsecase := usecase.NewLocationUseCase(
		dbUsecase,
		persistence.NewLocationRepository(),
		userIdentityRepository,
		attachmentUseCase,
		assetRepository,
		weightBridgeRepo,
	)
	assetTransactionUsecase := usecase.NewAssetTransactionUseCase(
		dbUsecase,
		assetTransactionRepo,
		partnerRepo,
		userRepo,
		financeUsecase,
		attachmentUseCase,
		assetRepository,
		assetTyreRepository,
		&notificationUc,
	)
	brandUsecase := usecase.NewBrandUseCase(dbUsecase, brandRepo, assetRepository, customAssetCategoryRepo)
	assetModelUsecase := usecase.NewAssetModelUseCase(dbUsecase, assetModelRepo, assetRepository)
	assetVehicleBodyTypeUsecase := usecase.NewAssetVehicleBodyTypeUseCase(dbUsecase, assetVehicleBodyTypeRepo)
	assetComponentUsecase := usecase.NewAssetComponentUseCase(dbUsecase, assetComponentRepo, userIdentityRepository, attachmentUseCase)
	customAssetCategoryUsecase := usecase.NewCustomAssetCategoryUseCase(dbUsecase, assetRepository, customAssetCategoryRepo, brandRepo)
	digispectUsecase := usecase.NewDigispectUseCase(dbUsecase, persistence.NewDigispectRepository(), inspectionTyreRepository, inspectionVehicleRepository, attachmentUc, userIdentityRepository)
	vehicleTargetTyreRemovalUsecase := usecase.NewVehicleTargetTyreRemovalUseCase(dbUsecase, vehicleTargetTyreRemovalRepo, assetRepository, assetVehicleRepository, userRepo)

	weightBridgeUsecase := usecase.NewWeightBridgeUseCase(dbUsecase, weightBridgeRepo, userIdentityRepository, attachmentUc, persistence.NewLocationRepository(), materialRepo, workShiftRepo, assetRepository, assetAssignmentRepository, ticketRepo)
	userUseCase := userUsecase.NewUserUseCase(
		dbUsecase,
		userRepo,
		permissionRepo,
		nil,
		financeUsecase,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
	)

	permissionUseCase := userUsecase.NewPermissionUseCase(dbUsecase, permissionRepo, userRepo)

	userUseCase.SetPermissionUseCase(permissionUseCase)
	haulUsecase := usecase.NewHaulUseCase(
		dbUsecase, haulRepo, integrationRepo, userRepo, userUseCase, assetRepository)

	// Inject Use Case to existing usecase with hypothetical function
	assetVehicleUc.SetNotifUseCase(notificationUc)
	assetUseCase.UpdateAssetAssignmentUseCase(assetAssignmentUseCase)
	assetUseCase.UpdateAssetVehicleUseCase(assetVehicleUc)
	assetTyreUseCase.UpdateAssetAssignmentUseCase(assetAssignmentUseCase)
	assetTyreUseCase.UpdateAssetLinkedUseCase(assetLinkedUseCase)
	assetTyreUseCase.UpdateAssetUseCase(*assetUseCase)
	assetLinkedUseCase.UpdateAssetAssignmentUseCase(assetAssignmentUseCase)
	assetLinkedUseCase.UpdateAssetTyreUseCase(assetTyreUseCase)
	assetLinkedUseCase.UpdateAssetUseCase(assetUseCase)
	assetVehicleUc.UpdateAssetAssignmentUseCase(assetAssignmentUseCase)
	assetLinkedUseCase.SetNotifUseCase(&notificationUc)

	// Handlers
	bonusPenaltyHandler := handler.NewBonusPenaltyHandler(bonusPenaltyTargetFormationUsecase, bonusPenaltyParameterUsecase, bonusPenaltyUsecase)
	assetHandler := handler.NewAssetHandler(
		assetUseCase, attachmentUseCase, assetVehicleUc,
	)
	assetVehicleHandler := handler.NewAssetVehicleHandler(
		assetUseCase, attachmentUseCase, assetVehicleUc,
	)
	assetTyreHandler := handler.NewAssetTyreHandler(assetTyreUseCase, attachmentUseCase)
	assetLinkedHandler := handler.NewAssetLinkedHandler(assetLinkedUseCase)
	inspectionHandler := handler.NewAssetInspectionHandler(*inspectionUseCase)
	assetAssignmentHandler := handler.NewAssetAssignmentHandler(assetAssignmentUseCase)
	locationHandler := handler.NewLocationHandler(locationUsecase)
	assetStatusRequestHandler := handler.NewAssetStatusRequestHandler(assetStatusRequestUsecase)
	assetTransactionHandler := handler.NewAssetTransactionHandler(assetTransactionUsecase)
	brandHandler := handler.NewBrandHandler(brandUsecase)
	assetModelHandler := handler.NewAssetModelHandler(assetModelUsecase)
	assetVehicleBodyTypeHandler := handler.NewAssetVehicleBodyTypeHandler(assetVehicleBodyTypeUsecase)
	assetComponentHandler := handler.NewAssetComponentHandler(assetComponentUsecase)
	customAssetCategoryHandler := handler.NewCustomAssetCategoryHandler(customAssetCategoryUsecase)
	digispectHandler := handler.NewDigispectHandler(digispectUsecase)
	vehicleTargetTyreRemovalHandler := handler.NewVehicleTargetTyreRemovalHandler(vehicleTargetTyreRemovalUsecase)
	weightBridgeHandler := handler.NewWeightBridgeHandler(weightBridgeUsecase)
	haulHandler := handler.NewHaulHandler(haulUsecase)

	// Register Routes
	routers.RegisterBonusPenaltyRoutes(router, bonusPenaltyHandler)
	routers.RegisterAssetRoutes(router, assetHandler)
	routers.RegisterAssetVehicleRoutes(router, assetVehicleHandler)
	routers.RegisterAssetTyreRoutes(router, assetTyreHandler)
	routers.RegisterAssetLinkedRoutes(router, assetLinkedHandler)
	routers.RegisterAssetInspectionRoutes(router, inspectionHandler)
	routers.RegisterAssetInspectionVehicleRoutes(router, inspectionHandler)
	routers.RegisterAssetInspectionTyreRoutes(router, inspectionHandler)
	routers.RegisterAssetInspectionFindingRoutes(router, inspectionHandler)
	routers.RegisterAssetAssignmentRoutes(router, assetAssignmentHandler)
	routers.RegisterLocationRoutes(router, locationHandler)
	routers.RegisterAssetStatusRequestRoutes(router, assetStatusRequestHandler)
	routers.RegisterAssetTransactionRoutes(router, assetTransactionHandler)
	routers.RegisterBrandRoutes(router, brandHandler)
	routers.RegisterAssetVehicleBodyTypeRoutes(router, assetVehicleBodyTypeHandler)
	routers.RegisterAssetComponentRoutes(router, assetComponentHandler)
	routers.RegisterCustomAssetCategoryRoutes(router, customAssetCategoryHandler)
	routers.RegisterAssetModelRoutes(router, assetModelHandler)
	routers.RegisterDigispectRoutes(router, digispectHandler)
	routers.RegisterVehicleTargetTyreRemovalRoutes(router, vehicleTargetTyreRemovalHandler)
	routers.RegisterWeightBridgeRoutes(router, weightBridgeHandler)
	routers.RegisterHaulRoutes(router, haulHandler)

	return nil
}
