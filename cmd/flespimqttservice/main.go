package main

import (
	"assetfindr/cmd/flespimqttservice/model"
	storagePersistence "assetfindr/internal/app/storage/persistence"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/cloudStorage"
	"assetfindr/internal/infrastructure/email"
	"assetfindr/internal/infrastructure/firebaseApp"
	"runtime/debug"

	notificationUsecase "assetfindr/internal/app/notification/usecase"
	"assetfindr/internal/config"
	"assetfindr/internal/constants"
	"assetfindr/internal/infrastructure/bq"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/internal/infrastructure/flespimqtt"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/helpers/httphelpers"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	notificationPersistence "assetfindr/internal/app/notification/presistence"
	taskPersistence "assetfindr/internal/app/task/persistence"

	"os"

	"assetfindr/internal/app/geo/dtos"
	geoModel "assetfindr/internal/app/geo/models"
	geoPersistence "assetfindr/internal/app/geo/persistence"
	geoRepo "assetfindr/internal/app/geo/repository"
	geoUsecase "assetfindr/internal/app/geo/usecase"

	integrationConstant "assetfindr/internal/app/integration/constants"
	"assetfindr/internal/app/integration/models"
	integrationRepo "assetfindr/internal/app/integration/repository"
	integrationUsecase "assetfindr/internal/app/integration/usecase"

	assetPersistence "assetfindr/internal/app/asset/persistence"
	integrationPersistence "assetfindr/internal/app/integration/presistence"
	truphonePersistence "assetfindr/internal/app/truphone/presistence"
	userIdentityPersistence "assetfindr/internal/app/user-identity/persistence"

	"cloud.google.com/go/bigquery"
	mqtt "github.com/eclipse/paho.mqtt.golang"
	"github.com/spf13/viper"
	"gopkg.in/guregu/null.v4"
)

type flespiService struct {
	BQ                 bq.BQUsecase
	trackingRepo       geoRepo.TrackingRepository
	trackingUseCase    geoUsecase.TrackingUseCase
	integrationUsecase integrationUsecase.IntegrationUseCase
	alertUsecase       integrationUsecase.AlertUseCase
	integrationRepo    integrationRepo.IntegrationRepository
	alertRepo          integrationRepo.AlertRepository
}

func NewFlespiService(bq bq.BQUsecase, dbUcPostgres, dbUcTimesacle, dbUcBigquery database.DBUsecase) flespiService {
	trackingRepository := geoPersistence.NewTrackingRepository()
	assetVehicleRepo := assetPersistence.NewAssetVehicleRepository()
	assetRepo := assetPersistence.NewAssetRepository()
	assetAssignmentRepo := assetPersistence.NewAssetAssignmentRepository()
	assetLinkedRepo := assetPersistence.NewAssetLinkedRepository()
	storageRepository := storagePersistence.NewStorageRepository(cloudStorage.Bucket)

	userRepo := userIdentityPersistence.NewUserRepository()
	clientRepo := userIdentityPersistence.NewClientRepository()
	integrationRepository := integrationPersistence.NewIntegrationRepository()
	alertRepository := integrationPersistence.NewAlertRepository()

	ticketRepo := taskPersistence.NewTicketRepository()

	accurateRepo := integrationPersistence.NewAccurateRepository(httphelpers.NewClientAllowRedirectAuth())

	gpsidRepository := integrationPersistence.NewGPSIDRepository()
	notificationRepo := notificationPersistence.NewAssetTyreRepository()
	emailRepo := notificationPersistence.NewEmailRepository(email.GetEmailService())

	notificationUc := notificationUsecase.NewNotificationUsecase(
		dbUcPostgres,
		notificationRepo,
		emailRepo,
		userRepo,
	)
	integrationUseCase := integrationUsecase.NewIntegrationUsecase(
		dbUcPostgres,
		bq,
		dbUcTimesacle,
		integrationRepository,
		gpsidRepository,
		trackingRepository,
		assetVehicleRepo,
		assetAssignmentRepo,
		userRepo,
		assetRepo,
		accurateRepo,
		clientRepo,
		storageRepository,
		alertRepository,
		truphonePersistence.NewTruphoneRepository(),
		emailRepo,
	)
	alertUseCase := integrationUsecase.NewAlertUsecase(
		dbUcPostgres,
		dbUcTimesacle,
		alertRepository,
		gpsidRepository,
		trackingRepository,
		assetVehicleRepo,
		assetAssignmentRepo,
		userRepo,
		assetRepo,
		accurateRepo,
		clientRepo,
		ticketRepo,
		notificationUc,
		assetLinkedRepo,
	)
	trackingUseCase := geoUsecase.NewTrackingUseCase(
		dbUcPostgres,
		bq,
		dbUcTimesacle,
		dbUcBigquery,
		trackingRepository,
		integrationRepository,
		assetVehicleRepo,
		assetAssignmentRepo,
		userRepo,
		assetRepo,
		alertRepository,
		ticketRepo,
		notificationUc,
		assetLinkedRepo,
		integrationPersistence.NewTyreAlertRepository(),
	)
	f := flespiService{
		BQ:                 bq,
		trackingRepo:       trackingRepository,
		trackingUseCase:    *trackingUseCase,
		integrationUsecase: integrationUseCase,
		integrationRepo:    integrationRepository,
		alertUsecase:       alertUseCase,
		alertRepo:          alertRepository,
	}

	return f
}

func (f *flespiService) messagePubHandler(client mqtt.Client, msg mqtt.Message) {
	defer func() {
		if r := recover(); r != nil {
			commonlogger.Errorf("Recovered. From: %s: %s\n", r, debug.Stack())
		}
	}()

	data := model.FlespiTrackingDto{}
	err := json.Unmarshal(msg.Payload(), &data)
	if err != nil {
		commonlogger.Errorf("falied to parse json from flespi err: %v, payload: %s", err, string(msg.Payload()))
		return
	}

	f.CreateLogRawSensorDatalake(data, msg.Payload())

	integration, err := f.integrationRepo.GetIntegration(context.Background(), f.integrationUsecase.DB.DB(), models.IntegrationCondition{
		Where: models.IntegrationWhere{
			IntegrationTargetTypeCode: integrationConstant.INTEGRATION_TARGET_TYPE_FLESPI_TRACKING,
			IdentifierJSON: map[string]string{
				"imei": data.Ident,
			},
			Status: integrationConstant.INTEGRATION_STATUS_CODE_ACTIVE,
		},
	})
	if err != nil {
		commonlogger.Warnf("failed to get integration my identifier, err:%v, ident:%s", err, data.Ident)
		return
	}

	f.CreateTracking(integration, data, msg.Payload())

	f.CreateDataLake(integration, data, msg.Payload())

	f.CreateCanBusData(integration, data, msg.Payload())
	f.CreateCanBusDataLake(integration, data, msg.Payload())

	f.CreateGpsPositionDataLake(integration, data, msg.Payload())
	f.CreateGeneralSensorAndDatalate(integration, data, msg.Payload())

	f.alertUsecase.MonitorAlert(context.Background(), integration, data)
}

func (f *flespiService) messagePubHandlerV2(_ mqtt.Client, msg mqtt.Message) {
	defer func() {
		if r := recover(); r != nil {
			commonlogger.Errorf("Recovered. From: %s: %s\n", r, debug.Stack())
		}
	}()

	err := f.trackingUseCase.ProcessIntegrationsDataMapping(context.Background(), msg.Payload(), true)
	if err != nil && !errorhandler.IsErrNotFound(err) {
		commonlogger.Errorf("falied to process integration data mapping data err: %v", err)
		return
	}
}

func (f *flespiService) CreateTracking(integration *models.Integration, data model.FlespiTrackingDto, msg []byte) {
	commonlogger.Infof("ongoing to insert into timescale, msg: %v, ident: %v, asset_id: %v", string(msg), data.Ident, integration.InternalReferenceID)
	err := f.trackingRepo.CreateTrackings(context.Background(), f.integrationUsecase.DBTimeScale.DB(), []geoModel.Tracking{
		{
			Time:          time.Unix(int64(data.Timestamp), 0).In(time.UTC),
			Long:          data.PositionLongitude,
			Lat:           data.PositionLatitude,
			KM:            null.NewInt(int64(data.VehicleMileage.Float64), data.VehicleMileage.Valid),
			Speed:         data.PositionSpeed,
			Angle:         null.Int{},
			IMEI:          data.Ident,
			AssetID:       integration.InternalReferenceID,
			TargetCode:    integrationConstant.TRACKING_FLESPI,
			ClientID:      integration.ClientID,
			IntegrationID: integration.ID,
		},
	})
	if err != nil {
		commonlogger.Errorf("failed to create tracking, err:%v, ident:%s", err, data.Ident)
		return
	}
}

func (f *flespiService) CreateCanBusData(integration *models.Integration, data model.FlespiTrackingDto, msg []byte) {
	if !data.IsCanBusValid() {
		return
	}

	item := data.ConvertToCanBusDataModel()
	item.AssetID = integration.InternalReferenceID
	item.Time = time.Unix(int64(data.Timestamp), 0).In(time.UTC)
	item.Ident = data.Ident
	item.CreatedAt = time.Now().In(time.UTC)
	item.IntegrationID = integration.ID
	item.ClientID = integration.ClientID

	err := f.trackingRepo.CreateCanBusSensor(
		context.Background(),
		f.integrationUsecase.DBTimeScale.DB(),
		&item,
	)
	if err != nil {
		commonlogger.Errorf("failed to create can bus data, err:%v, ident:%s", err, data.Ident)
		return
	}
}

func (f *flespiService) CreateBigQueryTableV2(schema bigquery.Schema, dataset string, tableName string) {
	tableRef := f.BQ.Conn().GetConn().Dataset(dataset).Table(tableName)
	if err := tableRef.Create(context.Background(), &bigquery.TableMetadata{
		Schema: schema,
	}); err != nil {
		return
	}
}

func (f *flespiService) UpdateBigQueryTable(schema bigquery.Schema, dataset string, tableName string) {
	tableRef := f.BQ.Conn().GetConn().Dataset(dataset).Table(tableName)
	meta, err := tableRef.Metadata(context.Background())
	if err != nil {
		commonlogger.Fatalf("Failed to fetch table metadata: %v", err)
	}

	if _, err := tableRef.Update(context.Background(), bigquery.TableMetadataToUpdate{
		Schema: schema,
	}, meta.ETag); err != nil {
		return
	}
}

func (f *flespiService) CreateDataLake(integration *models.Integration, data model.FlespiTrackingDto, msqPayload []byte) {
	tableName := "flespi_data_stream"
	if os.Getenv(constants.ENV_APP_ENV) == "local" {
		tableName = "testing_local_rizqo"
	}
	inserter := f.BQ.Conn().GetConn().Dataset("logs").Table(tableName).Inserter()
	items := []*model.FlespiData{
		{
			AssetId:   integration.InternalReferenceID,
			Time:      time.Unix(int64(data.Timestamp), 0).In(time.UTC),
			Ident:     data.Ident,
			Data:      string(msqPayload),
			CreatedAt: time.Now().In(time.UTC),
		},
	}
	var err error
	for attempt := 0; attempt < 5; attempt++ {
		err = inserter.Put(context.Background(), items)
		if err == nil {
			return
		}

		time.Sleep(time.Duration(attempt) * time.Second)
	}

	commonlogger.Errorf("failed to insert into bigquery after retry, last err: %v", err)
}

func (f *flespiService) CreateCanBusDataLake(integration *models.Integration, data model.FlespiTrackingDto, msqPayload []byte) {
	if !data.IsCanBusValid() {
		return
	}

	item := data.ConvertToCanBusModel()
	item.AssetID = integration.InternalReferenceID
	item.Time = time.Unix(int64(data.Timestamp), 0).In(time.UTC)
	item.Ident = data.Ident
	item.IntegrationID = integration.ID
	item.CreatedAt = time.Now().In(time.UTC)
	item.ClientID = integration.ClientID

	err := f.trackingRepo.CreateCanBusSensorDataLake(context.Background(), f.BQ.Conn(), &item)
	if err != nil {
		return
	}
}

func (f *flespiService) CreateGpsPositionDataLake(integration *models.Integration, data model.FlespiTrackingDto, msqPayload []byte) {
	item := data.ConvertToGpsModel()
	item.AssetId = integration.InternalReferenceID
	item.Time = time.Unix(int64(data.Timestamp), 0).In(time.UTC)
	item.Ident = data.Ident
	item.CreatedAt = time.Now().In(time.UTC)
	item.ClientID = integration.ClientID
	item.IntegrationID = integration.ID

	err := f.trackingRepo.CreateGpsSensorDataLake(context.Background(), f.BQ.Conn(), &item)
	if err != nil {
		return
	}
}

func (f *flespiService) CreateGeneralSensorAndDatalate(integration *models.Integration, data model.FlespiTrackingDto, msg []byte) {
	generalSensorData := &geoModel.GeneralSensor{
		AssetID:                    integration.InternalReferenceID,
		Time:                       time.Unix(int64(data.Timestamp), 0).In(time.UTC),
		Ident:                      data.Ident,
		CreatedAt:                  time.Now().In(time.UTC),
		IntegrationID:              integration.ID,
		ClientID:                   integration.ClientID,
		BatteryCurrent:             data.BatteryCurrent,
		BatteryVoltage:             data.BatteryVoltage,
		GsmSignalLevel:             data.GsmSignalLevel,
		MovementStatus:             data.MovementStatus,
		ExternalPowersourceVoltage: data.ExternalPowersourceVoltage,
		SdStatus:                   data.SdStatus,
		GsmOperatorCode:            data.GsmOperatorCode,
		XAccelaration:              data.XAccelaration,
		YAccelaration:              data.YAccelaration,
		ZAccelaration:              data.ZAccelaration,
		PtoDriveEngagementEnum:     data.PtoDriveEngagementEnum,
		FuelConsumed:               data.FuelConsumed,
	}
	err := f.trackingRepo.CreateGeneralSensor(context.Background(), f.trackingUseCase.DBTimeScale.DB(), generalSensorData)
	if err != nil {
		commonlogger.Errorf("failed to create general sensor data, err:%v, ident:%s", err, data.Ident)
	}

	err = f.trackingRepo.CreateGeneralSensorDataLake(context.Background(), f.BQ.Conn(), generalSensorData)
	if err != nil {
		commonlogger.Errorf("failed to create general sensor data, err:%v, ident:%s", err, data.Ident)
	}
}

func (f *flespiService) CreateLogRawSensorDatalake(data model.FlespiTrackingDto, msg []byte) {
	logRawSensorData := &geoModel.LogRawSensorData{
		Time:       null.TimeFrom(time.Unix(int64(data.Timestamp), 0).In(time.UTC)),
		Ident:      data.Ident,
		SourceCode: integrationConstant.TRACKING_FLESPI,
		Data:       string(msg),
		CreatedAt:  time.Now().In(time.UTC),
	}
	err := f.trackingRepo.CreateLogRawSensorDataLake(context.Background(), f.BQ.Conn(), logRawSensorData)
	if err != nil {
		commonlogger.Errorf("failed to create low raw sensor data, err:%v, ident:%s", err, data.Ident)
	}
}

func (f *flespiService) sendquipMessagePubHandler(client mqtt.Client, msg mqtt.Message) {
	defer func() {
		if r := recover(); r != nil {
			commonlogger.Errorf("Recovered. From: %s: %s\n", r, debug.Stack())
		}
	}()

	data := dtos.SendquipCompressorReq{}
	err := json.Unmarshal(msg.Payload(), &data)
	if err != nil {
		commonlogger.Warnf("falied to parse json from flespi, topic: %s err: %v, payload: %s", msg.Topic(), err, string(msg.Payload()))
	} else {
		f.trackingUseCase.CreateLogRawSensorDatalakeSendquip(data, string(msg.Payload()))
	}

	err = f.trackingUseCase.ProcessIntegrationsDataMapping(context.Background(), msg.Payload(), false)
	if err != nil && !errorhandler.IsErrNotFound(err) {
		commonlogger.Errorf("falied to process integration data mapping data err: %v", err)
		return
	}
}

func (f *flespiService) generalMessagePubHandler(client mqtt.Client, msg mqtt.Message) {
	defer func() {
		if r := recover(); r != nil {
			commonlogger.Errorf("Recovered. From: %s: %s\n", r, debug.Stack())
		}
	}()

	f.trackingUseCase.CreateLogRawSensorDatalake(string(msg.Payload()))
}

func (f *flespiService) OnMQTTConnect(client mqtt.Client) {
	commonlogger.Infof("Connected")
	// topic := viper.GetString(constants.CONFIG_FLESPI_MQTT_TOPIC)
	topic := "flespi/message/gw/devices/#"
	if token := client.Subscribe(topic, 0, f.messagePubHandlerV2); token.Wait() && token.Error() != nil {
		panic(token.Error())
	}
	commonlogger.Infof("Subscribed to topic: %s\n", topic)

	sendquipTopic := "/testing"
	if token := client.Subscribe(sendquipTopic, 0, f.sendquipMessagePubHandler); token.Wait() && token.Error() != nil {
		panic(token.Error())
	}

	commonlogger.Infof("Subscribed to topic: %s\n", sendquipTopic)

	generalTopic := "general"
	if token := client.Subscribe(generalTopic, 0, f.messagePubHandlerV2); token.Wait() && token.Error() != nil {
		panic(token.Error())
	}

	commonlogger.Infof("Subscribed to topic: %s\n", generalTopic)

	// topicBackup := viper.GetString(constants.CONFIG_FLESPI_MQTT_TOPIC_BACKUP)
	// if token := client.Subscribe(topicBackup, 0, f.messagePubHandlerV2); token.Wait() && token.Error() != nil {
	// 	panic(token.Error())
	// }
	// commonlogger.Infof("Subscribed to topic: %s\n", topicBackup)
}

func main() {
	if err := config.SetupConfig(); err != nil {
		commonlogger.Fatalf("config SetupConfig() error: %s", err)
	}

	masterDSN, replicaDSN := config.DbConfiguration()

	if err := database.DbConnection(masterDSN, replicaDSN); err != nil {
		commonlogger.Fatalf("database DbConnection error: %s", err)
		panic(err)
	}

	timeseriesDBDsn := config.TimeseriesDbConfig()
	err := database.TimeseriesDbConnect(timeseriesDBDsn)
	if err != nil {
		commonlogger.Fatalf("timeseries database connection error: %v", err)
		panic(err)
	}
	masterBQProjectID := config.BQConfiguration()

	if err := bq.BQConnection(masterBQProjectID); err != nil {
		commonlogger.Warnf("Bigquery connection error: %s", err)
	}

	if err := email.SetupEmailServices(); err != nil {
		commonlogger.Fatalf("Failed to create email services:%v\n", err)
		return
	}

	if err := firebaseApp.InitFirebaseClients(); err != nil {
		commonlogger.Fatalf("config Firebase App error: %s", err)
	}

	flespiService := NewFlespiService(bq.NewBQUsecase(bq.BQ), database.NewGormDBUsecase(database.DB), database.NewGormDBTimeSeriesUsecase(database.TimeseriesDB), database.NewGormDBUsecase(database.GetBigqueryDB()))

	client := flespimqtt.ConnectToFlespiMqtt(flespiService.OnMQTTConnect)
	if token := client.Connect(); token.Wait() && token.Error() != nil {
		panic(token.Error())
	}

	hiveClient := flespimqtt.ConnectToHivemqMqtt(flespiService.OnMQTTConnectHivemq)
	if token := hiveClient.Connect(); token.Wait() && token.Error() != nil {
		panic(token.Error())
	}

	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		fmt.Fprintf(w, "hello world")
	})

	// Determine port for HTTP service.
	port := os.Getenv("PORT")
	if port == "" {
		port = "8000"
		commonlogger.Infof("defaulting to port %s", port)
	}

	// Start HTTP server.
	commonlogger.Infof("listening on port %s", port)
	if err := http.ListenAndServe(":"+port, nil); err != nil {
		commonlogger.Fatal(err)
	}

	topic := viper.GetString(constants.CONFIG_FLESPI_MQTT_TOPIC)
	client.Unsubscribe(topic)
	client.Disconnect(250)
	hiveClient.Disconnect(250)
}
