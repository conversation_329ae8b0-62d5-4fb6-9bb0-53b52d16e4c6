package contentservice

import (
	"assetfindr/internal/infrastructure/cloudStorage"
	"assetfindr/internal/infrastructure/database"

	"github.com/gin-gonic/gin"

	assetPersistence "assetfindr/internal/app/asset/persistence"
	contentHandlr "assetfindr/internal/app/content/handler"
	contentPersistence "assetfindr/internal/app/content/presistence"
	contentRouters "assetfindr/internal/app/content/routers"
	contentUsecase "assetfindr/internal/app/content/usecase"
	storagePersistence "assetfindr/internal/app/storage/persistence"
	storageUsecase "assetfindr/internal/app/storage/usecase"
	taskPersistence "assetfindr/internal/app/task/persistence"
	userIdentityPersistence "assetfindr/internal/app/user-identity/persistence"
)

func InitializeContent(dbUsecase database.DBUsecase, router *gin.Engine) error {

	assetRepository := assetPersistence.NewAssetRepository()
	formRepository := contentPersistence.NewFormRepository()
	commentRepository := contentPersistence.NewCommentRepository()
	ticketRepository := taskPersistence.NewTicketRepository()
	storageRepository := storagePersistence.NewStorageRepository(cloudStorage.Bucket)
	attachmentRepository := storagePersistence.NewAttachmentRepository()
	userIdentityRepository := userIdentityPersistence.NewUserRepository()
	materialRepository := contentPersistence.NewMaterialRepository()
	weightBridgeRepository := assetPersistence.NewWeightBridgeRepository()

	attachmentUseCase := storageUsecase.NewAttachmentUseCase(dbUsecase, attachmentRepository, storageRepository)
	formUseCase := contentUsecase.NewFormUsecase(dbUsecase, formRepository, ticketRepository, attachmentUseCase)
	commentUseCase := contentUsecase.NewCommentUsecase(dbUsecase, commentRepository, assetRepository, userIdentityRepository, attachmentUseCase)
	materialUseCase := contentUsecase.NewMaterialUsecase(dbUsecase, materialRepository, weightBridgeRepository)
	formHandler := contentHandlr.NewFormHandler(formUseCase)
	commentHandler := contentHandlr.NewCommentHandler(commentUseCase)
	materialHandler := contentHandlr.NewMaterialHandler(materialUseCase)

	contentRouters.RegisterFormRoutes(router, formHandler)
	contentRouters.RegisterCommentRoutes(router, commentHandler)
	contentRouters.RegisterMaterialRoutes(router, materialHandler)
	return nil
}
