package usecase

import (
	"assetfindr/internal/app/content/constants"
	"assetfindr/internal/app/content/dtos"
	"assetfindr/internal/app/content/models"
	"assetfindr/internal/app/content/repository"
	storageConstants "assetfindr/internal/app/storage/constants"
	storageDtos "assetfindr/internal/app/storage/dtos"
	storageUsecase "assetfindr/internal/app/storage/usecase"
	ticketModels "assetfindr/internal/app/task/models"
	ticketRepo "assetfindr/internal/app/task/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"
	"assetfindr/pkg/common/helpers/pgtypehelpers"
	"context"
	"fmt"
	"sort"

	"gopkg.in/guregu/null.v4"
)

type FormUseCase struct {
	DB             database.DBUsecase
	formRepo       repository.FormRepository
	ticketRepo     ticketRepo.TicketRepository
	storageUsecase *storageUsecase.AttachmentUseCase
}

func NewFormUsecase(
	DB database.DBUsecase,
	contentRepo repository.FormRepository,
	ticketRepo ticketRepo.TicketRepository,
	storageUsecase *storageUsecase.AttachmentUseCase,
) FormUseCase {
	return FormUseCase{
		DB:             DB,
		formRepo:       contentRepo,
		ticketRepo:     ticketRepo,
		storageUsecase: storageUsecase,
	}
}

func (uc *FormUseCase) appendFormFieldRecursive(
	dst *[]models.FormField,
	attachments *[]storageDtos.UpsertAttachmentReq,
	in dtos.FieldReq,
	parentID string, // empty => root
) string {
	ff := models.FormField{
		FormFieldTypeCode: in.FieldTypeCode,
		Options:           in.Options,
		Sequence:          in.Sequence,
		Label:             in.Label,
		Value:             in.Value,
		IsMandatory:       in.IsMandatory,
		TemplateFieldID:   in.TemplateFieldID,
		Remark:            in.Remark,
	}

	// Set/keep ID so children can reference it
	if in.ID != "" {
		ff.ID = in.ID // reuse if provided
	} else {
		ff.SetID() // generate new ID
	}

	if parentID != "" {
		ff.FieldParentID = null.StringFrom(parentID)
	} // else zero-value null.String = NULL

	if len(in.Attachments) > 0 {
		*attachments = append(*attachments, storageDtos.UpsertAttachmentReq{
			ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_CUSTOM_FORM,
			SourceReferenceID: ff.ID,
			Photos:            in.Attachments,
		})
		ff.HasAttachment = null.BoolFrom(true)
	}
	*dst = append(*dst, ff)

	// Recurse with THIS node’s ID (not template field id)
	thisID := ff.ID
	for _, child := range in.Children {
		uc.appendFormFieldRecursive(dst, attachments, child, thisID)
	}
	return thisID
}

func (uc *FormUseCase) CreateForm(ctx context.Context, req dtos.FormReq) (*commonmodel.CreateResponse, error) {

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	form := &models.Form{
		ReferenceID:      req.ReferenceID,
		FormCategoryCode: req.FormCategoryCode,
		StatusCode:       req.StatusCode,
		TemplateID:       req.TemplateID,
	}

	form.FormFields = make([]models.FormField, 0, len(req.Fields))
	attachments := []storageDtos.UpsertAttachmentReq{}

	for _, root := range req.Fields {
		uc.appendFormFieldRecursive(&form.FormFields, &attachments, root, "")
	}

	err = uc.formRepo.CreateForm(ctx, uc.DB.WithCtx(ctx).DB(), form)
	if err != nil {
		return nil, err
	}

	/*
		TODO will need to create bulk handle attachments
		for current usecase should be fine
		need to revisit if we have performance issue
	*/
	for _, v := range attachments {
		v.ClientID = claim.GetLoggedInClientID()
		_, err = uc.storageUsecase.CreateAttachmentsPhotosV2(ctx, v)
		if err != nil {
			return nil, err
		}
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: form.ID,
		Data:        nil,
	}, nil
}

type updateItem struct {
	id    string
	patch models.FormField
}

func (uc *FormUseCase) collectFormOpsDFS(
	in commonmodel.FieldReq,
	parentID string,
	toCreate *[]models.FormField,
	toUpdate *[]updateItem,
	toDelete *[]string,
	attachments *[]storageDtos.UpsertAttachmentReq,
) string {
	// Handle delete first
	if in.IsDelete {
		if in.ID != "" {
			*toDelete = append(*toDelete, in.ID)
			// optional: recurse if you also send children to delete explicitly
			for _, ch := range in.Children {
				uc.collectFormOpsDFS(ch, in.ID, toCreate, toUpdate, toDelete, attachments)
			}
		}
		return in.ID
	}

	// Assign/keep this node's ID so children can link
	var thisID string
	if in.ID != "" {
		thisID = in.ID
	} else {
		// generate without appending yet: make a temp model to use SetID()
		tmp := models.FormField{}
		tmp.SetID()
		thisID = tmp.ID
	}

	payload := models.FormField{
		ModelV2:           commonmodel.ModelV2{ID: thisID}, // if your model embeds this
		FormFieldTypeCode: in.FieldTypeCode,
		Options:           in.Options,
		Sequence:          in.Sequence,
		Label:             in.Label,
		Value:             in.Value,
		IsMandatory:       in.IsMandatory,
		TemplateFieldID:   in.TemplateFieldID,
		Remark:            in.Remark,
	}
	if parentID != "" {
		payload.FieldParentID = null.StringFrom(parentID)
	}

	if in.IsNew() || in.ID == "" {
		*toCreate = append(*toCreate, payload)
	} else {
		*toUpdate = append(*toUpdate, updateItem{id: thisID, patch: payload})
	}

	if len(in.Attachments) > 0 {
		*attachments = append(*attachments, storageDtos.UpsertAttachmentReq{
			ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_CUSTOM_FORM,
			SourceReferenceID: thisID,
			Photos:            in.Attachments,
		})
		payload.HasAttachment = null.BoolFrom(true)
	}

	// Children point to thisID
	for _, ch := range in.Children {
		uc.collectFormOpsDFS(ch, thisID, toCreate, toUpdate, toDelete, attachments)
	}
	return thisID
}

func (uc *FormUseCase) UpsertForm(ctx context.Context, req commonmodel.FormReq) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	form := &models.Form{
		ReferenceID:      req.ReferenceID,
		FormCategoryCode: req.FormCategoryCode,
		StatusCode:       req.StatusCode,
		TemplateID:       req.TemplateID,
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}
	defer tx.Rollback()

	var creates []models.FormField
	var updates []updateItem
	var deletes []string
	attachments := []storageDtos.UpsertAttachmentReq{}

	for _, root := range req.Fields {
		uc.collectFormOpsDFS(root, "", &creates, &updates, &deletes, &attachments)
	}

	// 1) delete
	if len(deletes) > 0 {
		if err := uc.formRepo.DeleteFormFieldByIDs(ctx, tx.DB(), deletes); err != nil {
			return nil, err
		}
	}

	// 2) update
	for _, u := range updates {
		if err := uc.formRepo.UpdateFormField(ctx, tx.DB(), u.id, &u.patch); err != nil {
			return nil, err
		}
	}

	// 3) create
	if len(creates) > 0 {
		form.FormFields = append(form.FormFields, creates...)
	}

	if err := uc.formRepo.UpsertForm(ctx, tx.DB(), form); err != nil {
		return nil, err
	}

	/*
		TODO will need to create bulk handle attachments
		for current usecase should be fine
		need to revisit if we have performance issue
	*/
	for _, v := range attachments {
		v.ClientID = claim.GetLoggedInClientID()
		_, err = uc.storageUsecase.UpdateAttachmentPhotos(ctx, v)
		if err != nil {
			return nil, err
		}
	}

	if err := tx.Commit(); err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: form.ID,
	}, nil
}

// Returns the ID of the node it just appended, so children can link to it.
func (uc *FormUseCase) appendTemplateFieldRecursive(
	dst *[]models.FormTemplateField,
	attachments *[]storageDtos.UpsertAttachmentReq,
	in dtos.FieldReq, // adjust to your real DTO
	parentID string, // empty means "no parent"
) string {
	ff := models.FormTemplateField{
		FormFieldTypeCode: in.FieldTypeCode,
		Options:           in.Options, // pgtype.JSONB
		Sequence:          in.Sequence,
		Label:             in.Label,
		Value:             in.Value,       // pgtype.JSONB
		IsMandatory:       in.IsMandatory, // null.Bool
		Remark:            in.Remark,      // null.String
	}

	// Ensure this field has an ID so children can reference it.
	if in.ID != "" {
		ff.ID = in.ID // reuse if given by client
	} else {
		ff.SetID() // your ModelV2 helper – gives ff.ID
	}

	if parentID != "" {
		ff.FieldParentID = null.StringFrom(parentID)
		// else leave zero-value null.String{} = NULL
	}

	if len(in.Attachments) > 0 {
		*attachments = append(*attachments, storageDtos.UpsertAttachmentReq{
			ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_CUSTOM_FORM,
			SourceReferenceID: ff.ID,
			Photos:            in.Attachments,
		})
		ff.HasAttachment = null.BoolFrom(true)
	}
	*dst = append(*dst, ff)

	thisID := ff.ID
	for _, child := range in.Children {
		uc.appendTemplateFieldRecursive(dst, attachments, child, thisID)
	}
	return thisID
}

func (uc *FormUseCase) CreateFormTemplate(ctx context.Context, req dtos.TemplateReq) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	template := &models.FormTemplate{
		Name:             req.Name,
		FormCategoryCode: req.FormCategoryCode,
		IsDefault:        req.IsDefault,
	}

	// Build a *flat* slice with correct parent links by DFS.
	template.FormFields = make([]models.FormTemplateField, 0, len(req.Fields))
	attachments := []storageDtos.UpsertAttachmentReq{}
	for _, root := range req.Fields {
		uc.appendTemplateFieldRecursive(&template.FormFields, &attachments, root, "")
	}

	// Persist template + associated fields (bulk insert if your repo supports it)
	if err := uc.formRepo.CreateFormTemplate(ctx, uc.DB.WithCtx(ctx).DB(), template); err != nil {
		return nil, err
	}

	/*
		TODO will need to create bulk handle attachments
		for current usecase should be fine
		need to revisit if we have performance issue
	*/
	for _, v := range attachments {
		v.ClientID = claim.GetLoggedInClientID()
		_, err = uc.storageUsecase.CreateAttachmentsPhotosV2(ctx, v)
		if err != nil {
			return nil, err
		}
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: template.ID,
	}, nil
}

// ---- helper types -----------------------------------------------------------
type tmplUpdateItem struct {
	id    string
	patch models.FormTemplateField
}

// Depth-first traversal that collects create/update/delete ops for template fields.
// It guarantees every node has an ID (reuse in.ID if present, else generate) so
// children can safely reference parentID.
func (uc *FormUseCase) collectTemplateOpsDFS(
	in dtos.FieldReq,
	parentID string, // "" = root
	templateID string, // the enclosing FormTemplate ID
	toCreate *[]models.FormTemplateField,
	toUpdate *[]tmplUpdateItem,
	toDelete *[]string,
	attachments *[]storageDtos.UpsertAttachmentReq,
) string {
	// Deletion first: collect the id and (optionally) its subtree from the payload
	if in.IsDelete {
		if in.ID != "" {
			*toDelete = append(*toDelete, in.ID)
		}
		// If you want to also mark payload-children deleted explicitly:
		for _, ch := range in.Children {
			uc.collectTemplateOpsDFS(ch, in.ID, templateID, toCreate, toUpdate, toDelete, attachments)
		}
		return in.ID
	}

	// Ensure this node has an ID (so children can point to it)
	thisID := in.ID
	if thisID == "" {
		tmp := models.FormTemplateField{}
		tmp.SetID() // from commonmodel.ModelV2
		thisID = tmp.ID
	}

	// Build the field payload once
	payload := models.FormTemplateField{
		ModelV2:           commonmodel.ModelV2{ID: thisID},
		FormTemplateID:    templateID,
		FormFieldTypeCode: in.FieldTypeCode,
		Options:           in.Options, // pgtype.JSONB
		Sequence:          in.Sequence,
		Label:             in.Label,
		Value:             in.Value,       // pgtype.JSONB
		IsMandatory:       in.IsMandatory, // null.Bool
		Remark:            in.Remark,      // null.String
	}
	if parentID != "" {
		payload.FieldParentID = null.StringFrom(parentID) // parent = FIELD.ID
	}

	// Classify op
	if in.IsNew() || in.ID == "" {
		*toCreate = append(*toCreate, payload)
	} else {
		*toUpdate = append(*toUpdate, tmplUpdateItem{id: thisID, patch: payload})
	}

	if len(in.Attachments) > 0 {
		*attachments = append(*attachments, storageDtos.UpsertAttachmentReq{
			ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_CUSTOM_FORM,
			SourceReferenceID: thisID,
			Photos:            in.Attachments,
		})
		payload.HasAttachment = null.BoolFrom(true)
	}

	// Recurse to children, passing THIS node's field ID
	for _, ch := range in.Children {
		uc.collectTemplateOpsDFS(ch, thisID, templateID, toCreate, toUpdate, toDelete, attachments)
	}
	return thisID
}

func (uc *FormUseCase) UpdateFormTemplate(ctx context.Context, id string, req dtos.TemplateReq) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}
	defer tx.Rollback()

	// Ensure template exists & belongs to client; preload fields if your repo needs it
	_, err = uc.formRepo.GetFormTemplate(ctx, uc.DB.DB(), models.FormTemplateCondition{
		Where: models.FormTemplateWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
		Preload: models.FormTemplatePreload{FormFields: true},
	})
	if err != nil {
		return nil, err
	}

	// Collect ops in one DFS
	var toCreate []models.FormTemplateField
	var toUpdate []tmplUpdateItem
	var toDelete []string
	attachments := []storageDtos.UpsertAttachmentReq{}

	for _, root := range req.Fields {
		uc.collectTemplateOpsDFS(root, "", id, &toCreate, &toUpdate, &toDelete, &attachments)
	}

	// 1) Deletes first (avoid unique/constraint collisions)
	if len(toDelete) > 0 {
		if err := uc.formRepo.DeleteFormTemplateFieldByIDs(ctx, tx.DB(), toDelete); err != nil {
			return nil, err
		}
	}

	// 2) Updates (one by one; batch if your repo supports it)
	for _, u := range toUpdate {
		if err := uc.formRepo.UpdateFormTemplateField(ctx, tx.DB(), u.id, &u.patch); err != nil {
			return nil, err
		}
	}

	// 3) Creates (bulk insert if available)
	if len(toCreate) > 0 {
		// All payloads already have FormTemplateID and FieldParentID set
		if err := uc.formRepo.CreateFormTemplateFields(ctx, tx.DB(), toCreate); err != nil {
			return nil, err
		}
	}

	// 4) Update template meta (name, category, default flag)
	template := &models.FormTemplate{
		Name:             req.Name,
		FormCategoryCode: req.FormCategoryCode,
		IsDefault:        req.IsDefault,
	}
	if err := uc.formRepo.UpdateFormTemplate(ctx, tx.DB(), id, template); err != nil {
		return nil, err
	}

	/*
		TODO will need to create bulk handle attachments
		for current usecase should be fine
		need to revisit if we have performance issue
	*/
	for _, v := range attachments {
		v.ClientID = claim.GetLoggedInClientID()
		_, err = uc.storageUsecase.UpdateAttachmentPhotos(ctx, v)
		if err != nil {
			return nil, err
		}
	}

	if err := tx.Commit(); err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
	}, nil
}

func (uc *FormUseCase) GetForm(ctx context.Context, req dtos.GetFormReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	form, err := uc.formRepo.GetForm(ctx, uc.DB.DB(), models.FormCondition{
		Where: models.FormWhere{
			ReferenceID:      req.ReferenceID,
			FormCategoryCode: req.FormCategoryCode,
			ClientID:         claim.GetLoggedInClientID(),
		},
		Preload: models.FormPreload{FormFields: true},
	})
	if err != nil {
		return nil, err
	}

	// Step 1: materialize all nodes into a map[id]*node
	type node struct {
		f   dtos.FormField
		kid []*node
	}
	nodes := make(map[string]*node, len(form.FormFields))

	for _, field := range form.FormFields {
		nodes[field.ID] = &node{
			f: dtos.FormField{
				ID:                field.ID,
				FormFieldTypeCode: field.FormFieldTypeCode,
				Options:           pgtypehelpers.HandleValue(field.Options),
				Sequence:          field.Sequence,
				Label:             field.Label,
				Value:             pgtypehelpers.HandleValue(field.Value),
				IsMandatory:       field.IsMandatory,
				TemplateFieldID:   field.TemplateFieldID,
				FieldParentID:     field.FieldParentID,
				HasAttachment:     field.HasAttachment,
				Remark:            field.Remark,
				Children:          nil, // fill later
			},
		}
	}

	// Step 2: wire parent → children; collect roots
	roots := make([]*node, 0, len(nodes))
	for _, field := range form.FormFields {
		n := nodes[field.ID]
		if field.FieldParentID.Valid && field.FieldParentID.String != "" {
			if p := nodes[field.FieldParentID.String]; p != nil {
				p.kid = append(p.kid, n)
			} else {
				// Orphan safety: if parent not found, treat as root
				roots = append(roots, n)
			}
		} else {
			roots = append(roots, n)
		}
	}

	// Step 3: sort by Sequence recursively and convert to []dtos.FormField
	var sortKids func(x *node)
	sortKids = func(x *node) {
		sort.Slice(x.kid, func(i, j int) bool {
			if x.kid[i].f.Sequence == x.kid[j].f.Sequence {
				// tie-breaker for stability: lexicographic ID
				return x.kid[i].f.ID < x.kid[j].f.ID
			}
			return x.kid[i].f.Sequence < x.kid[j].f.Sequence
		})
		for _, c := range x.kid {
			sortKids(c)
		}
	}
	sort.Slice(roots, func(i, j int) bool {
		if roots[i].f.Sequence == roots[j].f.Sequence {
			return roots[i].f.ID < roots[j].f.ID
		}
		return roots[i].f.Sequence < roots[j].f.Sequence
	})
	for _, r := range roots {
		sortKids(r)
	}

	// Convert node tree → dtos.FormField tree
	var materialize func(x *node) dtos.FormField
	materialize = func(x *node) dtos.FormField {
		out := x.f
		if len(x.kid) > 0 {
			out.Children = make([]dtos.FormField, 0, len(x.kid))
			for _, c := range x.kid {
				out.Children = append(out.Children, materialize(c))
			}
		}
		return out
	}

	resp := dtos.Form{
		ID:               form.ID,
		ReferenceID:      form.ReferenceID,
		FormCategoryCode: form.FormCategoryCode,
		StatusCode:       form.StatusCode,
		TemplateID:       form.TemplateID,
		CreatedAt:        form.CreatedAt,
		FormFields:       make([]dtos.FormField, 0, len(roots)),
	}
	for _, r := range roots {
		resp.FormFields = append(resp.FormFields, materialize(r))
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: form.ID,
		Data:        resp,
	}, nil
}

func (uc *FormUseCase) GetFormTemplate(ctx context.Context, id string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tmpl, err := uc.formRepo.GetFormTemplate(ctx, uc.DB.DB(), models.FormTemplateCondition{
		Where: models.FormTemplateWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
		Preload: models.FormTemplatePreload{
			FormFields: true,
		},
	})
	if err != nil {
		return nil, err
	}

	// 1) Build node map
	type node struct {
		f   dtos.FormField
		kid []*node
	}
	nodes := make(map[string]*node, len(tmpl.FormFields))
	for _, field := range tmpl.FormFields {
		nodes[field.ID] = &node{
			f: dtos.FormField{
				ID:                field.ID,
				FormFieldTypeCode: field.FormFieldTypeCode,
				Options:           pgtypehelpers.HandleValue(field.Options),
				Sequence:          field.Sequence,
				Label:             field.Label,
				Value:             pgtypehelpers.HandleValue(field.Value),
				IsMandatory:       field.IsMandatory,
				FieldParentID:     field.FieldParentID, // parent = field.ID
				HasAttachment:     field.HasAttachment,
				Remark:            field.Remark,
				Children:          nil, // fill later
			},
		}
	}

	// 2) Wire parents → children; collect roots
	roots := make([]*node, 0, len(nodes))
	for _, field := range tmpl.FormFields {
		n := nodes[field.ID]
		if field.FieldParentID.Valid && field.FieldParentID.String != "" {
			if p := nodes[field.FieldParentID.String]; p != nil {
				p.kid = append(p.kid, n)
			} else {
				// orphan safety: parent missing -> treat as root
				roots = append(roots, n)
			}
		} else {
			roots = append(roots, n)
		}
	}

	// 3) Sort siblings by Sequence (stable), then by ID as tiebreaker
	var sortKids func(x *node)
	sortKids = func(x *node) {
		sort.Slice(x.kid, func(i, j int) bool {
			if x.kid[i].f.Sequence == x.kid[j].f.Sequence {
				return x.kid[i].f.ID < x.kid[j].f.ID
			}
			return x.kid[i].f.Sequence < x.kid[j].f.Sequence
		})
		for _, c := range x.kid {
			sortKids(c)
		}
	}
	sort.Slice(roots, func(i, j int) bool {
		if roots[i].f.Sequence == roots[j].f.Sequence {
			return roots[i].f.ID < roots[j].f.ID
		}
		return roots[i].f.Sequence < roots[j].f.Sequence
	})
	for _, r := range roots {
		sortKids(r)
	}

	// 4) Materialize node tree → []dtos.FormField
	var materialize func(x *node) dtos.FormField
	materialize = func(x *node) dtos.FormField {
		out := x.f
		if len(x.kid) > 0 {
			out.Children = make([]dtos.FormField, 0, len(x.kid))
			for _, c := range x.kid {
				out.Children = append(out.Children, materialize(c))
			}
		}
		return out
	}

	resp := dtos.FormTemplate{
		ID:               tmpl.ID,
		Name:             tmpl.Name,
		FormCategoryCode: tmpl.FormCategoryCode,
		IsDefault:        tmpl.IsDefault,
		FormFields:       make([]dtos.FormField, 0, len(roots)),
	}
	for _, r := range roots {
		resp.FormFields = append(resp.FormFields, materialize(r))
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: tmpl.ID,
		Data:        resp,
	}, nil
}

func (uc *FormUseCase) GetFormTemplateList(ctx context.Context, req dtos.FromTemplateListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	count, tmpls, err := uc.formRepo.GetFormTemplateList(ctx, uc.DB.DB(), models.GetFormTemplateListParam{
		ListRequest: req.ListRequest,
		Cond: models.FormTemplateCondition{
			Where: models.FormTemplateWhere{
				FormCategoryCode: req.Type,
				ClientID:         claim.GetLoggedInClientID(),
			},
		},
	})
	if err != nil {
		return nil, err
	}

	resp := make([]dtos.FormTemplate, 0, len(tmpls))
	for _, tmpl := range tmpls {
		resp = append(resp, dtos.FormTemplate{
			ID:               tmpl.ID,
			Name:             tmpl.Name,
			FormCategoryCode: tmpl.FormCategoryCode,
			IsDefault:        tmpl.IsDefault,
		})
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         resp,
	}, nil
}

func (uc *FormUseCase) UpsertFlows(ctx context.Context, req []dtos.FlowReq) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	deleteFlowIDs := []string{}
	newFlows := []models.Flow{}
	for _, flow := range req {
		switch {
		case flow.IsNew():
			if flow.FlowTypeCode == constants.FLOW_TYPE_CUSTOM_FORM && flow.FlowTypeID.String == "" {
				return nil, errorhandler.ErrBadRequest("flow_type_id is required on flow_type_code CUSTOM_FORM")
			}
			newFlows = append(newFlows, models.Flow{
				ReferenceID:   flow.ReferenceID,
				ReferenceCode: flow.ReferenceCode,
				FlowTypeID:    flow.FlowTypeID,
				FlowTypeCode:  flow.FlowTypeCode,
			})
		case flow.IsDeleted:
			deleteFlowIDs = append(deleteFlowIDs, flow.ID)
		default:
			exsistingFlow, err := uc.formRepo.GetFlow(ctx, tx.DB(), models.FlowCondition{
				Where: models.FlowWhere{
					ID:       flow.ID,
					ClientID: claim.GetLoggedInClientID(),
				},
			})
			if err != nil {
				return nil, err
			}

			if exsistingFlow.FlowTypeID != flow.FlowTypeID {
				form, err := uc.formRepo.GetForm(ctx, tx.DB(), models.FormCondition{
					Where: models.FormWhere{
						ReferenceID: flow.ID,
					},
				})
				if err == nil {
					err = uc.formRepo.DeleteFormByIDs(ctx, tx.DB(), form.ID)
					if err != nil {
						return nil, err
					}

					err = uc.formRepo.DeleteFormFeildsByFormIDs(ctx, tx.DB(), form.ID)
					if err != nil {
						return nil, err
					}
				}
			}
			err = uc.formRepo.UpdateFlow(ctx, tx.DB(), flow.ID,
				&models.Flow{
					ReferenceID:   flow.ReferenceID,
					ReferenceCode: flow.ReferenceCode,
					FlowTypeID:    flow.FlowTypeID,
					FlowTypeCode:  flow.FlowTypeCode,
				},
			)
			if err != nil {
				return nil, err
			}
		}
	}

	if len(deleteFlowIDs) > 0 {
		forms, err := uc.formRepo.GetForms(ctx, tx.DB(), models.FormCondition{
			Where: models.FormWhere{
				ReferenceIDs: deleteFlowIDs,
			},
		})
		if err != nil {
			return nil, err
		}

		formIDs := make([]string, 0, len(forms))
		for i := range forms {
			formIDs = append(formIDs, forms[i].ID)
		}

		err = uc.formRepo.DeleteFormByIDs(ctx, tx.DB(), formIDs...)
		if err != nil {
			return nil, err
		}

		err = uc.formRepo.DeleteFormFeildsByFormIDs(ctx, tx.DB(), formIDs...)
		if err != nil {
			return nil, err
		}
	}

	err = uc.formRepo.DeleteFlows(ctx, tx.DB(), deleteFlowIDs)
	if err != nil {
		return nil, err
	}

	err = uc.formRepo.CreateFlows(ctx, tx.DB(), newFlows)
	if err != nil {
		return nil, err
	}

	ticketNote := ticketModels.TicketNote{
		TicketID: req[0].ReferenceID,
		UserID:   claim.UserID,
		Title:    fmt.Sprintf("%s added new template(s) to Work Order Flow.", claim.GetName()),
		Notes:    "",
		ClientID: claim.GetLoggedInClientID(),
	}

	err = uc.ticketRepo.CreateNote(ctx, tx.DB(), &ticketNote)
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        nil,
	}, nil
}

func (uc *FormUseCase) GetFlows(ctx context.Context, req dtos.FlowListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	if req.ReferenceID == "" {
		return nil, errorhandler.ErrBadRequest("reference_id is required in url param")
	}

	flows, err := uc.formRepo.GetFlows(ctx, uc.DB.DB(), models.FlowCondition{
		Where: models.FlowWhere{
			ReferenceID:   req.ReferenceID,
			ReferenceCode: req.ReferenceCode,
			ClientID:      claim.GetLoggedInClientID(),
		},
		Preload: models.FlowPreload{
			FormTemplate: true,
			Form:         true,
		},
	})
	if err != nil {
		return nil, err
	}

	resp := make([]dtos.Flow, 0, len(flows))
	for _, tmpl := range flows {
		resp = append(resp, dtos.Flow{
			ID:            tmpl.ID,
			ReferenceID:   tmpl.ReferenceID,
			ReferenceCode: tmpl.ReferenceCode,
			FlowTypeID:    tmpl.FlowTypeID,
			FlowTypeCode:  tmpl.FlowTypeCode,
			FormTemplate:  dtos.BuildFormTemplateResp(tmpl.FormTemplate),
			Form:          dtos.BuildFormResp(tmpl.Form),
		})
	}

	return &commonmodel.ListResponse{
		TotalRecords: len(flows),
		PageSize:     len(flows),
		PageNo:       1,
		Data:         resp,
	}, nil
}
