package usecase

import (
	assetModel "assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/utils"
	"assetfindr/internal/app/geo/models"
	integrationConstants "assetfindr/internal/app/integration/constants"
	integrationModel "assetfindr/internal/app/integration/models"
	notificationConstants "assetfindr/internal/app/notification/constants"
	notificationDtos "assetfindr/internal/app/notification/dtos"
	taskConstants "assetfindr/internal/app/task/constants"
	taskModels "assetfindr/internal/app/task/models"
	userModels "assetfindr/internal/app/user-identity/models"
	"assetfindr/internal/errorhandler"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/contexthelpers"
	"assetfindr/pkg/common/helpers/timehelpers"
	"assetfindr/pkg/common/helpers/tmplhelpers"
	"html/template"

	"context"
	"fmt"
	"math"

	"github.com/jackc/pgtype"
	"gopkg.in/guregu/null.v4"
)

func (uc *TrackingUseCase) MonitorTyreAlertV2(ctx context.Context, tyreSensorData *models.TyreSensor, assetVehicle *assetModel.AssetVehicle, axleConfigs []assetModel.AxleConfiguration) {
	if !tyreSensorData.ParentAssetID.Valid {
		return
	}

	tyreAlertConfig, err := uc.tyreAlertRepo.GetTyreAlertConfig(ctx, uc.DB.DB(), integrationModel.TyreAlertConfigCondition{
		Where: integrationModel.TyreAlertConfigWhere{
			ParentAssetID: tyreSensorData.ParentAssetID.String,
		},
	})
	if err != nil {
		if !errorhandler.IsErrNotFound(err) {
			commonlogger.Warnf("failed to get tyre alert config, err: %v", err)
			return
		}
		return
	}

	uc.MonitorTyreAlertPressure(ctx, tyreSensorData, tyreAlertConfig, assetVehicle, axleConfigs)
	uc.MonitorTyreAlertHighTemperature(ctx, tyreSensorData, tyreAlertConfig)
	uc.MonitorTyreAlertPressureMismatch(ctx, tyreSensorData, tyreAlertConfig, assetVehicle, axleConfigs)
}

const (
	labelPressure       = "tyre.pressure"
	labelPressureMax    = "pressure_max"
	labelPressureMin    = "pressure_min"
	labelTemperature    = "tyre.temperature"
	labelTemperatureMax = "temperature_max"
)

func (uc *TrackingUseCase) MonitorTyreAlertPressure(ctx context.Context, tyreSensorData *models.TyreSensor, tyreAlertConfig *integrationModel.TyreAlertConfig, assetVehicle *assetModel.AssetVehicle, axleConfigs []assetModel.AxleConfiguration) {
	if !tyreAlertConfig.UsePressureAlert.Bool {
		return
	}

	if !tyreSensorData.Pressure.Valid {
		return
	}

	mapPositionToPressureFromAxle := assetModel.MapPositionToPressureFromAxle(axleConfigs)
	pressureConfig := mapPositionToPressureFromAxle[int(tyreSensorData.TyrePosition.Int64)]
	if !pressureConfig.PressureMin.Valid && !pressureConfig.PressureMax.Valid {
		return
	}

	recordedValue := pgtype.JSONB{}
	recordedValue.Set(map[string]interface{}{
		labelPressure: tyreSensorData.Pressure,
	})

	if pressureConfig.PressureMax.Valid && tyreSensorData.Pressure.Float64 > pressureConfig.PressureMax.Float64 {
		_, err := uc.tyreAlertRepo.GetTyreAlert(ctx, uc.DB.DB(), integrationModel.TyreAlertCondition{
			Where: integrationModel.TyreAlertWhere{
				ParentAssetID:          tyreSensorData.ParentAssetID.String,
				TyreAlertTypeCode:      integrationConstants.TYRE_ALERT_TYPE_PRESSURE_OVERINFLATED,
				InLastDefaultResetTime: true,
				IsRead:                 null.BoolFrom(false),
			},
		})
		if err == nil {
			return
		}

		if !errorhandler.IsErrNotFound(err) {
			commonlogger.Errorf("failed to get tyre alert, err: %v", err)
			return
		}

		tyreSensorData.TyreAlertTypes = append(tyreSensorData.TyreAlertTypes, integrationConstants.TYRE_ALERT_TYPE_PRESSURE_OVERINFLATED)
		thresholdValue := pgtype.JSONB{}
		thresholdValue.Set(map[string]interface{}{
			labelPressureMax: pressureConfig.PressureMax,
		})

		// Get asset info for title generation
		asset, err := uc.assetRepo.GetAsset(ctx, uc.DB.DB(), assetModel.AssetCondition{
			Where: assetModel.AssetWhere{
				ID: tyreSensorData.AssetID,
			},
		})
		if err != nil {
			commonlogger.Errorf("failed to get asset for tyre alert title generation, err: %v", err)
			return
		}

		parentAsset, err := uc.assetRepo.GetAsset(ctx, uc.DB.DB(), assetModel.AssetCondition{
			Where: assetModel.AssetWhere{
				ID: tyreSensorData.ParentAssetID.String,
			},
		})
		if err != nil {
			commonlogger.Errorf("failed to get parent asset for tyre alert title generation, err: %v", err)
			return
		}

		title := uc.GenerateTyreAlertTitle(integrationConstants.TYRE_ALERT_TYPE_PRESSURE_OVERINFLATED,
			asset.SerialNumber, parentAsset.GetAssetIdentForNotif(), int(tyreSensorData.TyrePosition.Int64))
		description := uc.GenerateTyreAlertDescription(integrationConstants.TYRE_ALERT_TYPE_PRESSURE_OVERINFLATED,
			tyreSensorData.Pressure.Float64, pressureConfig.PressureMax.Float64)

		tyreAlert := &integrationModel.TyreAlert{
			ModelV2: commonmodel.ModelV2{
				CreatedBy: "SYSTEM",
				UpdatedBy: "SYSTEM",
				ClientID:  tyreSensorData.ClientID,
			},
			Title:             title,
			Description:       description,
			TyreAlertConfigID: tyreAlertConfig.ID,
			AssetID:           tyreSensorData.AssetID,
			ParentAssetID:     tyreSensorData.ParentAssetID.String,
			Time:              tyreSensorData.Time,
			TyreAlertTypeCode: integrationConstants.TYRE_ALERT_TYPE_PRESSURE_OVERINFLATED,
			RecordedValue:     recordedValue,
			ThresholdValue:    thresholdValue,
		}
		err = uc.tyreAlertRepo.CreateTyreAlert(ctx, uc.DB.DB(), tyreAlert)
		if err != nil {
			commonlogger.Errorf("failed to create tyre alert, err: %v", err)
			return
		}

		uc.createAlertV2FromTyreAlert(ctx, tyreAlert)

		if tyreAlertConfig.UseSendNotification.Bool {
			go uc.sendTyreOverinflatedAlertNotification(contexthelpers.WithoutCancel(ctx), tyreSensorData, tyreAlertConfig,
				tyreSensorData.Pressure,
				pressureConfig.PressureMax,
			)
		}

		if tyreAlertConfig.UseCreateTickets.Bool {
			ticketSubject := uc.GenerateTyreAlertTicketSubject(integrationConstants.TYRE_ALERT_TYPE_PRESSURE_OVERINFLATED)
			assetAssigneeUserID, assetAssigneeUserName, err := uc.getAssetAssignmentUser(ctx, tyreAlert.ParentAssetID)
			if err != nil {
				commonlogger.Errorf("failed to get asset assignment user name for tyre alert ticket creation, err: %v", err)
				return
			}

			ticketDescription := fmt.Sprintf(`Tyre %s on your vehicle %s (Tyre Position %d) is experiencing overinflated.

Current Pressure: %.0f psi
Maximum Allowed Pressure: %.0f psi
Alert Time: %s
Asset Assignee: %s`, asset.SerialNumber, parentAsset.GetAssetIdentForNotif(), int(tyreSensorData.TyrePosition.Int64),
				tyreSensorData.Pressure.Float64, pressureConfig.PressureMax.Float64, tyreSensorData.Time.Local().Format(timehelpers.RFC1123Notif),
				assetAssigneeUserName,
			)
			uc.createTicketFromTyreAlert(ctx, tyreAlert, tyreAlertConfig, assetAssigneeUserID, ticketSubject, ticketDescription)
		}
	}

	if pressureConfig.PressureMin.Valid && tyreSensorData.Pressure.Float64 < pressureConfig.PressureMin.Float64 {
		_, err := uc.tyreAlertRepo.GetTyreAlert(ctx, uc.DB.DB(), integrationModel.TyreAlertCondition{
			Where: integrationModel.TyreAlertWhere{
				ParentAssetID:          tyreSensorData.ParentAssetID.String,
				TyreAlertTypeCode:      integrationConstants.TYRE_ALERT_TYPE_PRESSURE_UNDERINFLATED,
				InLastDefaultResetTime: true,
				IsRead:                 null.BoolFrom(false),
			},
		})
		if err == nil {
			return
		}

		if !errorhandler.IsErrNotFound(err) {
			commonlogger.Errorf("failed to get tyre alert, err: %v", err)
			return
		}

		tyreSensorData.TyreAlertTypes = append(tyreSensorData.TyreAlertTypes, integrationConstants.TYRE_ALERT_TYPE_PRESSURE_UNDERINFLATED)
		thresholdValue := pgtype.JSONB{}
		thresholdValue.Set(map[string]interface{}{
			labelPressureMin: pressureConfig.PressureMin,
		})

		// Get asset info for title generation
		asset, err := uc.assetRepo.GetAsset(ctx, uc.DB.DB(), assetModel.AssetCondition{
			Where: assetModel.AssetWhere{
				ID: tyreSensorData.AssetID,
			},
		})
		if err != nil {
			commonlogger.Errorf("failed to get asset for tyre alert title generation, err: %v", err)
			return
		}

		parentAsset, err := uc.assetRepo.GetAsset(ctx, uc.DB.DB(), assetModel.AssetCondition{
			Where: assetModel.AssetWhere{
				ID: tyreSensorData.ParentAssetID.String,
			},
		})
		if err != nil {
			commonlogger.Errorf("failed to get parent asset for tyre alert title generation, err: %v", err)
			return
		}

		title := uc.GenerateTyreAlertTitle(integrationConstants.TYRE_ALERT_TYPE_PRESSURE_UNDERINFLATED,
			asset.SerialNumber, parentAsset.GetAssetIdentForNotif(), int(tyreSensorData.TyrePosition.Int64))
		description := uc.GenerateTyreAlertDescription(integrationConstants.TYRE_ALERT_TYPE_PRESSURE_UNDERINFLATED,
			tyreSensorData.Pressure.Float64, pressureConfig.PressureMin.Float64)

		tyreAlert := &integrationModel.TyreAlert{
			ModelV2: commonmodel.ModelV2{
				CreatedBy: "SYSTEM",
				UpdatedBy: "SYSTEM",
				ClientID:  tyreSensorData.ClientID,
			},
			Title:             title,
			Description:       description,
			TyreAlertConfigID: tyreAlertConfig.ID,
			AssetID:           tyreSensorData.AssetID,
			ParentAssetID:     tyreSensorData.ParentAssetID.String,
			Time:              tyreSensorData.Time,
			TyreAlertTypeCode: integrationConstants.TYRE_ALERT_TYPE_PRESSURE_UNDERINFLATED,
			RecordedValue:     recordedValue,
			ThresholdValue:    thresholdValue,
		}
		err = uc.tyreAlertRepo.CreateTyreAlert(ctx, uc.DB.DB(), tyreAlert)
		if err != nil {
			commonlogger.Errorf("failed to create tyre alert, err: %v", err)
			return
		}

		uc.createAlertV2FromTyreAlert(ctx, tyreAlert)

		if tyreAlertConfig.UseSendNotification.Bool {
			go uc.sendTyreUnderinflatedAlertNotification(contexthelpers.WithoutCancel(ctx), tyreSensorData, tyreAlertConfig,
				tyreSensorData.Pressure,
				pressureConfig.PressureMin,
			)
		}

		if tyreAlertConfig.UseCreateTickets.Bool {
			ticketSubject := uc.GenerateTyreAlertTicketSubject(integrationConstants.TYRE_ALERT_TYPE_PRESSURE_UNDERINFLATED)
			assetAssigneeUserID, assetAssigneeUserName, err := uc.getAssetAssignmentUser(ctx, tyreAlert.ParentAssetID)
			if err != nil {
				commonlogger.Errorf("failed to get asset assignment user name for tyre alert ticket creation, err: %v", err)
				return
			}

			ticketDescription := fmt.Sprintf(`Tyre %s on your vehicle %s (Tyre Position %d) is experiencing underinflated.

Current Pressure: %.0f psi
Minimum Allowed Pressure: %.0f psi
Alert Time: %s
Asset Assignee: %s`, asset.SerialNumber, parentAsset.GetAssetIdentForNotif(), int(tyreSensorData.TyrePosition.Int64),
				tyreSensorData.Pressure.Float64, pressureConfig.PressureMin.Float64, tyreSensorData.Time.Local().Format(timehelpers.RFC1123Notif),
				assetAssigneeUserName,
			)
			uc.createTicketFromTyreAlert(ctx, tyreAlert, tyreAlertConfig, assetAssigneeUserID, ticketSubject, ticketDescription)
		}
	}
}

func (tu *TrackingUseCase) getAssetAssignmentUser(ctx context.Context, assetID string) (id, name string, err error) {
	assetAssignment, err := tu.assetAssignmentRepo.GetAssetAssignment(ctx, tu.DB.DB(), assetModel.AssetAssignmentCondition{
		Where: assetModel.AssetAssignmentWhere{
			AssetID:  assetID,
			Assigned: true,
		},
	})
	if err == nil {
		user, err := tu.userRepo.GetUser(ctx, tu.DB.DB(), userModels.UserCondition{
			Where: userModels.UserWhere{
				ID: assetAssignment.UserID,
			},
		})
		if err == nil {
			return assetAssignment.UserID, user.GetName(), nil
		}

		if !errorhandler.IsErrNotFound(err) {
			return "", "-", err
		}

		commonlogger.Warnf("failed to get asset assignment user, err: %v", err)
		return "", "-", nil
	}

	if !errorhandler.IsErrNotFound(err) {
		return "", "-", err
	}

	commonlogger.Warnf("failed to get asset assignment, err: %v", err)
	return "", "-", nil
}

func (uc *TrackingUseCase) createAlertV2FromTyreAlert(ctx context.Context, tyreAlert *integrationModel.TyreAlert) {
	alert := &integrationModel.AlertV2{
		ModelV2: commonmodel.ModelV2{
			CreatedBy: "SYSTEM",
			UpdatedBy: "SYSTEM",
			ClientID:  tyreAlert.ClientID,
		},
		AlertConfigID: "",
		AssetID:       tyreAlert.AssetID,
		ParentAssetID: tyreAlert.ParentAssetID,
		Time:          tyreAlert.Time,
		RecordedValue: tyreAlert.RecordedValue,
		TyreAlertID:   null.StringFrom(tyreAlert.ID),
	}
	err := uc.alertRepo.CreateAlertV2(ctx, uc.DB.DB(), alert)
	if err != nil {
		commonlogger.Errorf("failed to create alert v2 from tyre alert, err: %v", err)
		return
	}
}

func (uc *TrackingUseCase) MonitorTyreAlertHighTemperature(ctx context.Context, tyreSensorData *models.TyreSensor, tyreAlertConfig *integrationModel.TyreAlertConfig) {
	if !tyreAlertConfig.UseHighTemperatureAlert.Bool {
		return
	}

	if !tyreSensorData.Temperature.Valid {
		return
	}

	if !tyreAlertConfig.MaxTemperatureThreshold.Valid {
		return
	}

	if tyreSensorData.Temperature.Float64 > tyreAlertConfig.MaxTemperatureThreshold.Float64 {
		_, err := uc.tyreAlertRepo.GetTyreAlert(ctx, uc.DB.DB(), integrationModel.TyreAlertCondition{
			Where: integrationModel.TyreAlertWhere{
				ParentAssetID:          tyreSensorData.ParentAssetID.String,
				TyreAlertTypeCode:      integrationConstants.TYRE_ALERT_TYPE_HIGH_TEMPERATURE,
				InLastDefaultResetTime: true,
				IsRead:                 null.BoolFrom(false),
			},
		})
		if err == nil {
			return
		}

		if !errorhandler.IsErrNotFound(err) {
			commonlogger.Errorf("failed to get tyre alert, err: %v", err)
			return
		}

		tyreSensorData.TyreAlertTypes = append(tyreSensorData.TyreAlertTypes, integrationConstants.TYRE_ALERT_TYPE_HIGH_TEMPERATURE)
		recordedValue := pgtype.JSONB{}
		recordedValue.Set(map[string]interface{}{
			labelTemperature: tyreSensorData.Temperature,
		})

		thresholdValue := pgtype.JSONB{}
		thresholdValue.Set(map[string]interface{}{
			labelTemperatureMax: tyreAlertConfig.MaxTemperatureThreshold,
		})

		// Get asset info for title generation
		asset, err := uc.assetRepo.GetAsset(ctx, uc.DB.DB(), assetModel.AssetCondition{
			Where: assetModel.AssetWhere{
				ID: tyreSensorData.AssetID,
			},
		})
		if err != nil {
			commonlogger.Errorf("failed to get asset for tyre alert title generation, err: %v", err)
			return
		}

		parentAsset, err := uc.assetRepo.GetAsset(ctx, uc.DB.DB(), assetModel.AssetCondition{
			Where: assetModel.AssetWhere{
				ID: tyreSensorData.ParentAssetID.String,
			},
		})
		if err != nil {
			commonlogger.Errorf("failed to get parent asset for tyre alert title generation, err: %v", err)
			return
		}

		title := uc.GenerateTyreAlertTitle(integrationConstants.TYRE_ALERT_TYPE_HIGH_TEMPERATURE,
			asset.SerialNumber, parentAsset.GetAssetIdentForNotif(), int(tyreSensorData.TyrePosition.Int64))
		description := uc.GenerateTyreAlertDescription(integrationConstants.TYRE_ALERT_TYPE_HIGH_TEMPERATURE,
			tyreSensorData.Temperature.Float64, tyreAlertConfig.MaxTemperatureThreshold.Float64)

		tyreAlert := &integrationModel.TyreAlert{
			ModelV2: commonmodel.ModelV2{
				CreatedBy: "SYSTEM",
				UpdatedBy: "SYSTEM",
				ClientID:  tyreSensorData.ClientID,
			},
			Title:             title,
			Description:       description,
			TyreAlertConfigID: tyreAlertConfig.ID,
			AssetID:           tyreSensorData.AssetID,
			ParentAssetID:     tyreSensorData.ParentAssetID.String,
			Time:              tyreSensorData.Time,
			TyreAlertTypeCode: integrationConstants.TYRE_ALERT_TYPE_HIGH_TEMPERATURE,
			RecordedValue:     recordedValue,
			ThresholdValue:    thresholdValue,
		}
		err = uc.tyreAlertRepo.CreateTyreAlert(ctx, uc.DB.DB(), tyreAlert)
		if err != nil {
			commonlogger.Errorf("failed to create tyre alert, err: %v", err)
			return
		}

		uc.createAlertV2FromTyreAlert(ctx, tyreAlert)

		if tyreAlertConfig.UseSendNotification.Bool {
			go uc.sendTyreHighTemperatureAlertNotification(contexthelpers.WithoutCancel(ctx), tyreSensorData, tyreAlertConfig,
				tyreSensorData.Temperature,
			)
		}

		if tyreAlertConfig.UseCreateTickets.Bool {
			ticketSubject := uc.GenerateTyreAlertTicketSubject(integrationConstants.TYRE_ALERT_TYPE_HIGH_TEMPERATURE)
			assetAssigneeUserID, assetAssigneeUserName, err := uc.getAssetAssignmentUser(ctx, tyreAlert.ParentAssetID)
			if err != nil {
				commonlogger.Errorf("failed to get asset assignment user name for tyre alert ticket creation, err: %v", err)
				return
			}

			ticketDescription := fmt.Sprintf(`Tyre %s on your vehicle %s (Tyre Position %d) has reached a high temperature.

Current Temperature: %.0f °C
Maximum Allowed Temperature: %.0f °C
Alert Time: %s
Asset Assignee: %s`, asset.SerialNumber, parentAsset.GetAssetIdentForNotif(), int(tyreSensorData.TyrePosition.Int64),
				tyreSensorData.Temperature.Float64, tyreAlertConfig.MaxTemperatureThreshold.Float64, tyreSensorData.Time.Local().Format(timehelpers.RFC1123Notif),
				assetAssigneeUserName,
			)
			uc.createTicketFromTyreAlert(ctx, tyreAlert, tyreAlertConfig, assetAssigneeUserID, ticketSubject, ticketDescription)
		}
	}
}

func (uc *TrackingUseCase) MonitorTyreAlertPressureMismatch(ctx context.Context, tyreSensorData *models.TyreSensor, tyreAlertConfig *integrationModel.TyreAlertConfig, assetVehicle *assetModel.AssetVehicle, axleConfigs []assetModel.AxleConfiguration) {
	if !tyreAlertConfig.UsePressureMismatchAlert.Bool {
		return
	}

	if !tyreSensorData.Pressure.Valid {
		return
	}

	pressureConfig := assetModel.MapPositionToPressureFromAxle(axleConfigs)[int(tyreSensorData.TyrePosition.Int64)]
	numTyreInRow := utils.NumOfTyreByAxleType(pressureConfig.Axle)

	tyreSensors, err := uc.trackingRepo.GetLatestTyreSensorDataForCheckPressureMismatch(ctx, uc.DBTimeScale.DB(),
		tyreSensorData,
	)
	if err != nil {
		commonlogger.Errorf("failed to get tyre sensor data for check pressure mismatch, err: %v", err)
		return
	}

	if len(tyreSensors) < numTyreInRow-1 {
		return
	}

	// Check if pressure mismatch threshold is configured
	if !pressureConfig.PressureTolerance.Valid || pressureConfig.PressureTolerance.Float64 == 0 {
		return
	}

	// Calculate average pressure for the row (P_row)
	var totalPressure float64 = tyreSensorData.Pressure.Float64
	for _, sensor := range tyreSensors {
		if sensor.Pressure.Valid {
			totalPressure += sensor.Pressure.Float64
		}
	}

	averageRowPressure := totalPressure / float64(numTyreInRow)

	// Check pressure mismatch using formula: |P_tyre - P_row| / P_row > %threshold
	currentTyrePressure := tyreSensorData.Pressure.Float64
	pressureDifference := math.Abs(currentTyrePressure - averageRowPressure)
	mismatchPercentage := (pressureDifference / averageRowPressure) * 100

	if mismatchPercentage > pressureConfig.PressureTolerance.Float64 {
		// Check if alert already exists
		_, err := uc.tyreAlertRepo.GetTyreAlert(ctx, uc.DB.DB(), integrationModel.TyreAlertCondition{
			Where: integrationModel.TyreAlertWhere{
				AssetID:                tyreSensorData.AssetID,
				ParentAssetID:          tyreSensorData.ParentAssetID.String,
				TyreAlertTypeCode:      integrationConstants.TYRE_ALERT_TYPE_PRESSURE_MISMATCH,
				InLastDefaultResetTime: true,
				IsRead:                 null.BoolFrom(false),
			},
		})
		if err == nil {
			return
		}

		if !errorhandler.IsErrNotFound(err) {
			commonlogger.Errorf("failed to get tyre pressure mismatch alert, err: %v", err)
			return
		}

		tyreSensorData.TyreAlertTypes = append(tyreSensorData.TyreAlertTypes, integrationConstants.TYRE_ALERT_TYPE_PRESSURE_MISMATCH)

		recordedValue := pgtype.JSONB{}
		recordedValue.Set(map[string]interface{}{
			labelPressure:          tyreSensorData.Pressure,
			"average_row_pressure": averageRowPressure,
			"mismatch_percentage":  mismatchPercentage,
		})

		thresholdValue := pgtype.JSONB{}
		thresholdValue.Set(map[string]interface{}{
			"threshold_percentage": pressureConfig.PressureTolerance,
		})

		// Get asset info for title generation
		asset, err := uc.assetRepo.GetAsset(ctx, uc.DB.DB(), assetModel.AssetCondition{
			Where: assetModel.AssetWhere{
				ID: tyreSensorData.AssetID,
			},
		})
		if err != nil {
			commonlogger.Errorf("failed to get asset for tyre pressure mismatch alert title generation, err: %v", err)
			return
		}

		parentAsset, err := uc.assetRepo.GetAsset(ctx, uc.DB.DB(), assetModel.AssetCondition{
			Where: assetModel.AssetWhere{
				ID: tyreSensorData.ParentAssetID.String,
			},
		})
		if err != nil {
			commonlogger.Errorf("failed to get parent asset for tyre pressure mismatch alert title generation, err: %v", err)
			return
		}

		title := uc.GenerateTyreAlertTitle(integrationConstants.TYRE_ALERT_TYPE_PRESSURE_MISMATCH,
			asset.SerialNumber, parentAsset.GetAssetIdentForNotif(), int(tyreSensorData.TyrePosition.Int64))
		description := fmt.Sprintf("There is a significant pressure difference detected on this tyre. Current pressure is %.0f psi, while the average on its axle is %.0f psi.",
			currentTyrePressure, averageRowPressure)

		tyreAlert := &integrationModel.TyreAlert{
			ModelV2: commonmodel.ModelV2{
				CreatedBy: "SYSTEM",
				UpdatedBy: "SYSTEM",
				ClientID:  tyreSensorData.ClientID,
			},
			Title:             title,
			Description:       description,
			TyreAlertConfigID: tyreAlertConfig.ID,
			AssetID:           tyreSensorData.AssetID,
			ParentAssetID:     tyreSensorData.ParentAssetID.String,
			Time:              tyreSensorData.Time,
			TyreAlertTypeCode: integrationConstants.TYRE_ALERT_TYPE_PRESSURE_MISMATCH,
			RecordedValue:     recordedValue,
			ThresholdValue:    thresholdValue,
		}
		err = uc.tyreAlertRepo.CreateTyreAlert(ctx, uc.DB.DB(), tyreAlert)
		if err != nil {
			commonlogger.Errorf("failed to create tyre pressure mismatch alert, err: %v", err)
			return
		}

		uc.createAlertV2FromTyreAlert(ctx, tyreAlert)

		if tyreAlertConfig.UseSendNotification.Bool {
			go uc.sendTyrePressureMismatchAlertNotification(contexthelpers.WithoutCancel(ctx), tyreSensorData, tyreAlertConfig,
				null.FloatFrom(currentTyrePressure),
				null.FloatFrom(averageRowPressure),
			)
		}

		if tyreAlertConfig.UseCreateTickets.Bool {
			ticketSubject := uc.GenerateTyreAlertTicketSubject(integrationConstants.TYRE_ALERT_TYPE_PRESSURE_MISMATCH)
			assetAssigneeUserID, assetAssigneeUserName, err := uc.getAssetAssignmentUser(ctx, tyreAlert.ParentAssetID)
			if err != nil {
				commonlogger.Errorf("failed to get asset assignment user name for tyre alert ticket creation, err: %v", err)
				return
			}

			ticketDescription := fmt.Sprintf(`A pressure mismatch has been detected for Tyre %s on vehicle %s (Tyre Position %d). The pressure of this tyre differs significantly from the average pressure of other tyres on the same axle row.

Current Pressure: %.0f psi
Average Row Pressure: %.0f psi
Alert Time: %s
Asset Assignee: %s`, asset.SerialNumber, parentAsset.GetAssetIdentForNotif(), int(tyreSensorData.TyrePosition.Int64),
				currentTyrePressure, averageRowPressure, tyreSensorData.Time.Local().Format(timehelpers.RFC1123Notif),
				assetAssigneeUserName,
			)
			uc.createTicketFromTyreAlert(ctx, tyreAlert, tyreAlertConfig, assetAssigneeUserID, ticketSubject, ticketDescription)
		}
	}

}

func (uc *TrackingUseCase) sendTyreOverinflatedAlertNotification(
	ctx context.Context,
	tyreSensorData *models.TyreSensor,
	tyreAlertConfig *integrationModel.TyreAlertConfig,
	currentPressure null.Float,
	pressureMax null.Float,
) {

	asset, err := uc.assetRepo.GetAsset(ctx, uc.DB.DB(), assetModel.AssetCondition{
		Where: assetModel.AssetWhere{
			ID: tyreSensorData.AssetID,
		},
	})
	if err != nil {
		commonlogger.Errorf("failed to get asset for tyre overinflated alert notification, err: %v", err)
		return
	}

	parentAsset, err := uc.assetRepo.GetAsset(ctx, uc.DB.DB(), assetModel.AssetCondition{
		Where: assetModel.AssetWhere{
			ID: tyreSensorData.ParentAssetID.String,
		},
	})
	if err != nil {
		commonlogger.Errorf("failed to get parent asset for tyre overinflated alert notification, err: %v", err)
		return
	}

	client, err := uc.userRepo.GetClient(ctx, uc.DB.DB(), userModels.ClientCondition{
		Where: userModels.ClientWhere{
			ID: asset.ClientID,
		},
	})
	if err != nil {
		commonlogger.Errorf("failed to get client for tyre overinflated alert notification, err: %v", err)
		return
	}

	targetURL := uc.notifUseCase.GenerateTargetURL(ctx, client.ClientAlias, notificationConstants.DESTINATION_TYPE_ASSET_ALERT_TYRE, tyreSensorData.ParentAssetID.String, &tyreSensorData.Time)

	templ := tmplhelpers.TyreOverinflatedAlertTemplate{
		TyreSerialNumber:     asset.SerialNumber,
		TyrePosition:         int(tyreSensorData.TyrePosition.Int64),
		AssetIDCredential:    parentAsset.GetAssetIdentForNotif(),
		CurrentPressure:      currentPressure.Float64,
		ThresholdMaxPressure: pressureMax.Float64,
		RedirectLink:         template.URL(targetURL),
	}

	uc.sendTyreAlertNotificationCommon(ctx, tyreSensorData, tyreAlertConfig, asset,
		templ.GenerateEmailSubject(), templ.GenerateEmailBody(),
		templ.GeneratePushNotifSubject(), templ.GeneratePushNotifBody(), targetURL)
}

func (uc *TrackingUseCase) sendTyreUnderinflatedAlertNotification(
	ctx context.Context,
	tyreSensorData *models.TyreSensor,
	tyreAlertConfig *integrationModel.TyreAlertConfig,
	currentPressure null.Float,
	thresholdMinPressure null.Float,
) {

	asset, err := uc.assetRepo.GetAsset(ctx, uc.DB.DB(), assetModel.AssetCondition{
		Where: assetModel.AssetWhere{
			ID: tyreSensorData.AssetID,
		},
	})
	if err != nil {
		commonlogger.Errorf("failed to get asset for tyre underinflated alert notification, err: %v", err)
		return
	}

	parentAsset, err := uc.assetRepo.GetAsset(ctx, uc.DB.DB(), assetModel.AssetCondition{
		Where: assetModel.AssetWhere{
			ID: tyreSensorData.ParentAssetID.String,
		},
	})
	if err != nil {
		commonlogger.Errorf("failed to get parent asset for tyre underinflated alert notification, err: %v", err)
		return
	}

	client, err := uc.userRepo.GetClient(ctx, uc.DB.DB(), userModels.ClientCondition{
		Where: userModels.ClientWhere{
			ID: asset.ClientID,
		},
	})
	if err != nil {
		commonlogger.Errorf("failed to get client for tyre underinflated alert notification, err: %v", err)
		return
	}

	targetURL := uc.notifUseCase.GenerateTargetURL(ctx, client.ClientAlias, notificationConstants.DESTINATION_TYPE_ASSET_ALERT_TYRE, tyreSensorData.ParentAssetID.String, &tyreSensorData.Time)

	templ := tmplhelpers.TyreUnderinflatedAlertTemplate{
		TyreSerialNumber:     asset.SerialNumber,
		TyrePosition:         int(tyreSensorData.TyrePosition.Int64),
		AssetIDCredential:    parentAsset.GetAssetIdentForNotif(),
		CurrentPressure:      currentPressure.Float64,
		ThresholdMinPressure: thresholdMinPressure.Float64,
		RedirectLink:         template.URL(targetURL),
	}

	uc.sendTyreAlertNotificationCommon(ctx, tyreSensorData, tyreAlertConfig, asset,
		templ.GenerateEmailSubject(), templ.GenerateEmailBody(),
		templ.GeneratePushNotifSubject(), templ.GeneratePushNotifBody(), targetURL)
}

func (uc *TrackingUseCase) sendTyreHighTemperatureAlertNotification(
	ctx context.Context,
	tyreSensorData *models.TyreSensor,
	tyreAlertConfig *integrationModel.TyreAlertConfig,
	currentTemperature null.Float,
) {

	asset, err := uc.assetRepo.GetAsset(ctx, uc.DB.DB(), assetModel.AssetCondition{
		Where: assetModel.AssetWhere{
			ID: tyreSensorData.AssetID,
		},
	})
	if err != nil {
		commonlogger.Errorf("failed to get asset for tyre high temperature alert notification, err: %v", err)
		return
	}

	parentAsset, err := uc.assetRepo.GetAsset(ctx, uc.DB.DB(), assetModel.AssetCondition{
		Where: assetModel.AssetWhere{
			ID: tyreSensorData.ParentAssetID.String,
		},
	})
	if err != nil {
		commonlogger.Errorf("failed to get parent asset for tyre high temperature alert notification, err: %v", err)
		return
	}

	client, err := uc.userRepo.GetClient(ctx, uc.DB.DB(), userModels.ClientCondition{
		Where: userModels.ClientWhere{
			ID: asset.ClientID,
		},
	})
	if err != nil {
		commonlogger.Errorf("failed to get client for tyre high temperature alert notification, err: %v", err)
		return
	}

	targetURL := uc.notifUseCase.GenerateTargetURL(ctx, client.ClientAlias, notificationConstants.DESTINATION_TYPE_ASSET_ALERT_TYRE, tyreSensorData.ParentAssetID.String, &tyreSensorData.Time)

	templ := tmplhelpers.TyreHighTemperatureAlertTemplate{
		TyreSerialNumber:     asset.SerialNumber,
		TyrePosition:         int(tyreSensorData.TyrePosition.Int64),
		AssetIDCredential:    parentAsset.GetAssetIdentForNotif(),
		CurrentTemperature:   currentTemperature.Float64,
		ThresholdTemperature: tyreAlertConfig.MaxTemperatureThreshold.Float64,
		RedirectLink:         template.URL(targetURL),
	}

	uc.sendTyreAlertNotificationCommon(ctx, tyreSensorData, tyreAlertConfig, asset,
		templ.GenerateEmailSubject(), templ.GenerateEmailBody(),
		templ.GeneratePushNotifSubject(), templ.GeneratePushNotifBody(), targetURL)
}

func (uc *TrackingUseCase) sendTyrePressureMismatchAlertNotification(
	ctx context.Context,
	tyreSensorData *models.TyreSensor,
	tyreAlertConfig *integrationModel.TyreAlertConfig,
	currentPressure null.Float,
	averageRowPressure null.Float,
) {

	asset, err := uc.assetRepo.GetAsset(ctx, uc.DB.DB(), assetModel.AssetCondition{
		Where: assetModel.AssetWhere{
			ID: tyreSensorData.AssetID,
		},
	})
	if err != nil {
		commonlogger.Errorf("failed to get asset for tyre pressure mismatch alert notification, err: %v", err)
		return
	}

	parentAsset, err := uc.assetRepo.GetAsset(ctx, uc.DB.DB(), assetModel.AssetCondition{
		Where: assetModel.AssetWhere{
			ID: tyreSensorData.ParentAssetID.String,
		},
	})
	if err != nil {
		commonlogger.Errorf("failed to get parent asset for tyre pressure mismatch alert notification, err: %v", err)
		return
	}

	client, err := uc.userRepo.GetClient(ctx, uc.DB.DB(), userModels.ClientCondition{
		Where: userModels.ClientWhere{
			ID: asset.ClientID,
		},
	})
	if err != nil {
		commonlogger.Errorf("failed to get client for tyre pressure mismatch alert notification, err: %v", err)
		return
	}

	targetURL := uc.notifUseCase.GenerateTargetURL(ctx, client.ClientAlias, notificationConstants.DESTINATION_TYPE_ASSET_ALERT_TYRE, tyreSensorData.ParentAssetID.String, &tyreSensorData.Time)

	templ := tmplhelpers.TyrePressureMismatchAlertTemplate{
		TyreSerialNumber:   asset.SerialNumber,
		TyrePosition:       int(tyreSensorData.TyrePosition.Int64),
		AssetIDCredential:  parentAsset.GetAssetIdentForNotif(),
		CurrentPressure:    currentPressure.Float64,
		AverageRowPressure: averageRowPressure.Float64,
		RedirectLink:       template.URL(targetURL),
	}

	uc.sendTyreAlertNotificationCommon(ctx, tyreSensorData, tyreAlertConfig, asset,
		templ.GenerateEmailSubject(), templ.GenerateEmailBody(),
		templ.GeneratePushNotifSubject(), templ.GeneratePushNotifBody(), targetURL)
}

func (uc *TrackingUseCase) sendTyreAlertNotificationCommon(
	ctx context.Context,
	tyreSensorData *models.TyreSensor,
	tyreAlertConfig *integrationModel.TyreAlertConfig,
	asset *assetModel.Asset,
	emailSubject string,
	emailBody string,
	pushSubject string,
	pushBody string,
	targetURL string,
) {

	userIDs := make([]string, 0)

	for _, userID := range tyreAlertConfig.NotificationRecipientUserIDs {
		userIDs = append(userIDs, userID)
	}

	if tyreAlertConfig.NotifyAssetAssignee.Bool {
		assetAssignment, err := uc.assetAssignmentRepo.GetAssetAssignment(ctx, uc.DB.DB(), assetModel.AssetAssignmentCondition{
			Where: assetModel.AssetAssignmentWhere{
				AssetID: tyreSensorData.ParentAssetID.String,
			},
		})
		if err == nil && assetAssignment.UserID != "" {
			userIDs = append(userIDs, assetAssignment.UserID)
		}
	}

	if len(userIDs) == 0 {
		commonlogger.Warnf("no recipients configured for tyre alert notification")
		return
	}

	notifItems := make([]notificationDtos.CreateNotificationItem, 0, len(userIDs))
	for _, userID := range userIDs {
		notifItem := notificationDtos.CreateNotificationItem{
			UserID:            userID,
			SourceCode:        notificationConstants.NOTIFICATION_SOURCE_CODE_TYRE_ALERT,
			SourceReferenceID: tyreSensorData.AssetID,
			TargetReferenceID: tyreSensorData.ParentAssetID.String,
			TargetURL:         targetURL,
			MessageHeader:     emailSubject,
			MessageBody:       emailBody,
			MessageFirebase: notificationDtos.MessageFirebase{
				Title: pushSubject,
				Body:  pushBody,
			},
			ClientID:        asset.ClientID,
			TypeCode:        notificationConstants.NOTIFICATION_TYPE_ALERT_CODE,
			ContentTypeCode: notificationConstants.NOTIF_CONTENT_TYPE_CODE_ALERT,
			ReferenceCode:   notificationConstants.NOTIF_REF_ASSET_ALERT,
			ReferenceValue:  tyreSensorData.AssetID,
		}
		notifItems = append(notifItems, notifItem)
	}

	sendToEmail := false
	sendToPushNotif := false
	for _, actionType := range tyreAlertConfig.NotificationActionTypes {
		switch actionType {
		case integrationConstants.ALERT_ACTION_TYPE_CODE_EMAIL_NOTIFICATION:
			sendToEmail = true
		case integrationConstants.ALERT_ACTION_TYPE_CODE_WEB_NOTIFICATION,
			integrationConstants.ALERT_ACTION_TYPE_CODE_MOBILE_APP_NOTIFICATION,
			integrationConstants.ALERT_ACTION_TYPE_CODE_WEB_MOBILE_APP_NOTIFICATION:
			sendToPushNotif = true
		}
	}

	createNotifReq := notificationDtos.CreateNotificationReq{
		Items:           notifItems,
		SendToEmail:     sendToEmail,
		SendToPushNotif: sendToPushNotif,
	}

	err := uc.notifUseCase.CreateNotification(ctx, createNotifReq)
	if err != nil {
		commonlogger.Errorf("failed to send tyre alert notification, err: %v", err)
	}
}

func (uc *TrackingUseCase) GenerateTyreAlertTitle(alertType string, tyreSerialNumber string, parentAssetIdent string, tyrePosition int) string {
	switch alertType {
	case integrationConstants.TYRE_ALERT_TYPE_PRESSURE_OVERINFLATED:
		return fmt.Sprintf("High Pressure on Tyre %s (%s - Tyre Position %d)", tyreSerialNumber, parentAssetIdent, tyrePosition)
	case integrationConstants.TYRE_ALERT_TYPE_PRESSURE_UNDERINFLATED:
		return fmt.Sprintf("Low Pressure on Tyre %s (%s - Tyre Position %d)", tyreSerialNumber, parentAssetIdent, tyrePosition)
	case integrationConstants.TYRE_ALERT_TYPE_HIGH_TEMPERATURE:
		return fmt.Sprintf("High Tyre Temperature on Tyre %s (%s - Tyre Position %d)", tyreSerialNumber, parentAssetIdent, tyrePosition)
	case integrationConstants.TYRE_ALERT_TYPE_PRESSURE_MISMATCH:
		return fmt.Sprintf("Mismatch Tyre Pressure on Tyre %s (%s - Tyre Position %d)", tyreSerialNumber, parentAssetIdent, tyrePosition)
	default:
		return fmt.Sprintf("Tyre Alert on Tyre %s (%s - Tyre Position %d)", tyreSerialNumber, parentAssetIdent, tyrePosition)
	}
}

func (uc *TrackingUseCase) GenerateTyreAlertDescription(alertType string, currentValue, thresholdValue float64) string {
	switch alertType {
	case integrationConstants.TYRE_ALERT_TYPE_PRESSURE_OVERINFLATED:
		return fmt.Sprintf("Current pressure: %.0f psi, Threshold Max: %.0f psi.", currentValue, thresholdValue)
	case integrationConstants.TYRE_ALERT_TYPE_PRESSURE_UNDERINFLATED:
		return fmt.Sprintf("Current pressure: %.0f psi, Threshold Min: %.0f psi.", currentValue, thresholdValue)
	case integrationConstants.TYRE_ALERT_TYPE_HIGH_TEMPERATURE:
		return fmt.Sprintf("Current temperature: %.0f°C, Threshold: %.0f°C", currentValue, thresholdValue)
	default:
		return fmt.Sprintf("Current value: %.2f, Threshold: %.2f", currentValue, thresholdValue)
	}
}

func (uc *TrackingUseCase) GenerateTyreAlertTicketSubject(alertType string) string {
	switch alertType {
	case integrationConstants.TYRE_ALERT_TYPE_PRESSURE_OVERINFLATED:
		return "Overinflated Tyre Pressure Alert"
	case integrationConstants.TYRE_ALERT_TYPE_PRESSURE_UNDERINFLATED:
		return "Underinflated Tyre Pressure Alert"
	case integrationConstants.TYRE_ALERT_TYPE_HIGH_TEMPERATURE:
		return "High Tyre Temperature Alert"
	case integrationConstants.TYRE_ALERT_TYPE_PRESSURE_MISMATCH:
		return "Tyre Pressure Mismatch Alert"
	default:
		return "Tyre Alert"
	}
}

func (uc *TrackingUseCase) createTicketFromTyreAlert(
	ctx context.Context,
	tyreAlert *integrationModel.TyreAlert,
	tyreAlertConfig *integrationModel.TyreAlertConfig,
	assetAssigneeUserID string,
	subject string,
	description string,
) {
	dataInformation, err := uc.assetRepo.GetAssetDataInformation(ctx, uc.DB.DB(), tyreAlert.ParentAssetID)
	if err != nil {
		commonlogger.Errorf("failed to get asset data information, err:%v", err)
		return
	}

	ticket := &taskModels.Ticket{
		ModelV2: commonmodel.ModelV2{
			CreatedBy: "SYSTEM",
			UpdatedBy: "SYSTEM",
			ClientID:  tyreAlert.ClientID,
		},
		Subject:              subject,
		Description:          description,
		TicketCategoryCode:   taskConstants.TICKET_CATEGORY_CODE_OTHERS,
		TicketReferenceCode:  taskConstants.TICKET_ASSET_REF,
		ReferenceID:          tyreAlert.ParentAssetID,
		StatusCode:           taskConstants.TICKET_STATUS_CODE_OPEN,
		SeverityLevelCode:    taskConstants.TICKET_SEVERITY_NOT_SET,
		RequesterUserID:      tyreAlertConfig.CreatedBy,
		AssignedToUserID:     null.StringFrom(tyreAlertConfig.TicketAssignedUserID.String),
		AssetDataInformation: pgtype.JSONB{Bytes: dataInformation, Status: pgtype.Present},
	}

	err = uc.ticketRepo.CreateTicket(ctx, uc.DB.DB(), ticket)
	if err != nil {
		commonlogger.Errorf("failed to create ticket on tyre alert, err: %v", err)
		return
	}

	asset, err := uc.assetRepo.GetAsset(ctx, uc.DB.DB(), assetModel.AssetCondition{
		Where: assetModel.AssetWhere{
			ID: tyreAlert.ParentAssetID,
		},
	})
	if err != nil {
		commonlogger.Errorf("failed to get asset for tyre alert ticket creation, err: %v", err)
		return
	}

	client, err := uc.userRepo.GetClient(ctx, uc.DB.DB(), userModels.ClientCondition{
		Where: userModels.ClientWhere{
			ID: asset.ClientID,
		},
	})
	if err != nil {
		commonlogger.Errorf("failed to get client for tyre alert ticket creation, err: %v", err)
		return
	}

	user, err := uc.userRepo.GetUser(ctx, uc.DB.DB(), userModels.UserCondition{
		Where: userModels.UserWhere{
			ID: ticket.AssignedToUserID.String,
		},
	})
	if err != nil {
		commonlogger.Warnf("error get user on notify update ticket status", err)
		return
	}

	href := uc.notifUseCase.GenerateTargetURL(ctx, client.ClientAlias, notificationConstants.DESTINATION_TYPE_WORK_ORDER, ticket.ID)

	templateBod := tmplhelpers.CreateTicketBody{
		Subject:        ticket.Subject,
		AssetName:      asset.GetAssetIdentForNotif(),
		UserAssigned:   user.GetName(),
		RedirectWOLink: template.URL(href),
		SeverityLevel:  taskConstants.MapTicketSeverityLabel[ticket.SeverityLevelCode],
		TicketDesc:     ticket.Description,
	}

	notifItem := notificationDtos.CreateNotificationItem{
		SourceCode:        notificationConstants.NOTIFICATION_SOURCE_CODE_TICKET_ASSIGNMENT,
		SourceReferenceID: ticket.ID,
		TargetReferenceID: ticket.ReferenceID,
		TargetURL:         href,
		MessageHeader:     templateBod.ConstructTitleEmail(),
		MessageBody:       templateBod.ConstructBodyEmail(),
		MessageFirebase: notificationDtos.MessageFirebase{
			Title: templateBod.ConstructTitlePushNotif(),
			Body:  templateBod.ConstructBodyPushNotif(),
		},
		ClientID:       asset.ClientID,
		TypeCode:       notificationConstants.NOTIFICATION_TYPE_USER_ACTIVITY_CODE,
		ReferenceCode:  notificationConstants.NOTIF_REF_WORK_ORDER,
		ReferenceValue: ticket.ID,
		UserID:         ticket.AssignedToUserID.String,
	}

	notifItems := []notificationDtos.CreateNotificationItem{notifItem}
	if assetAssigneeUserID != "" && assetAssigneeUserID != ticket.AssignedToUserID.String {
		notifItem.UserID = assetAssigneeUserID
		notifItems = append(notifItems, notifItem)
	}

	go func(req notificationDtos.CreateNotificationReq) {
		_ = uc.notifUseCase.CreateNotification(
			contexthelpers.WithoutCancel(ctx), req,
		)
	}(notificationDtos.CreateNotificationReq{
		Items:           notifItems,
		SendToEmail:     true,
		SendToPushNotif: true,
	})
}
