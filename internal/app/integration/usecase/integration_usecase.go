package usecase

import (
	assetModel "assetfindr/internal/app/asset/models"
	assetRepo "assetfindr/internal/app/asset/repository"
	"assetfindr/internal/app/asset/usecase"
	geoModel "assetfindr/internal/app/geo/models"
	geoRepo "assetfindr/internal/app/geo/repository"
	"assetfindr/internal/app/integration/constants"
	"assetfindr/internal/app/integration/dtos"
	"assetfindr/internal/app/integration/errorconstants"
	"assetfindr/internal/app/integration/models"
	"assetfindr/internal/app/integration/repository"
	notificationRepo "assetfindr/internal/app/notification/repository"
	storageConstants "assetfindr/internal/app/storage/constants"
	storageRepository "assetfindr/internal/app/storage/repository"
	truphoneRepo "assetfindr/internal/app/truphone/repository"
	userModel "assetfindr/internal/app/user-identity/models"
	userIdentityRepository "assetfindr/internal/app/user-identity/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/bq"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers"
	"assetfindr/pkg/common/helpers/authhelpers"
	"assetfindr/pkg/common/helpers/cryptohelpers"
	"assetfindr/pkg/common/helpers/timehelpers"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/jackc/pgtype"
	"github.com/xuri/excelize/v2"
	"gopkg.in/guregu/null.v4"
)

type IntegrationUseCase struct {
	DB                  database.DBUsecase
	BQ                  bq.BQUsecase
	DBTimeScale         database.DBUsecase
	AssetUsecase        usecase.AssetUseCase
	integrationRepo     repository.IntegrationRepository
	gpsidRepo           repository.GPSIDRepository
	assetVehicleRepo    assetRepo.AssetVehicleRepository
	trackingRepo        geoRepo.TrackingRepository
	assetAssignmentRepo assetRepo.AssetAssignmentRepository
	userRepo            userIdentityRepository.UserRepository
	assetRepo           assetRepo.AssetRepository
	accurateRepo        repository.AccurateRepository
	clientRepo          userIdentityRepository.ClientRepository
	StorageRepository   storageRepository.StorageRepository
	alertRepo           repository.AlertRepository
	truphoneRepo        truphoneRepo.TruphoneRepository
	emailRepository     notificationRepo.EmailRepository
}

func NewIntegrationUsecase(
	DB database.DBUsecase,
	BQ bq.BQUsecase,
	DBTimeScale database.DBUsecase,
	integrationRepo repository.IntegrationRepository,
	gpsidRepo repository.GPSIDRepository,
	trackingRepo geoRepo.TrackingRepository,
	assetVehicleRepo assetRepo.AssetVehicleRepository,
	assetAssignmentRepo assetRepo.AssetAssignmentRepository,
	userRepo userIdentityRepository.UserRepository,
	assetRepo assetRepo.AssetRepository,
	accurateRepo repository.AccurateRepository,
	clientRepo userIdentityRepository.ClientRepository,
	storageRepository storageRepository.StorageRepository,
	alertRepo repository.AlertRepository,
	truphoneRepo truphoneRepo.TruphoneRepository,
	emailRepository notificationRepo.EmailRepository,
) IntegrationUseCase {
	return IntegrationUseCase{
		DB:                  DB,
		BQ:                  BQ,
		DBTimeScale:         DBTimeScale,
		integrationRepo:     integrationRepo,
		gpsidRepo:           gpsidRepo,
		trackingRepo:        trackingRepo,
		assetVehicleRepo:    assetVehicleRepo,
		assetAssignmentRepo: assetAssignmentRepo,
		userRepo:            userRepo,
		assetRepo:           assetRepo,
		accurateRepo:        accurateRepo,
		clientRepo:          clientRepo,
		StorageRepository:   storageRepository,
		alertRepo:           alertRepo,
		truphoneRepo:        truphoneRepo,
		emailRepository:     emailRepository,
	}
}

func (uc *IntegrationUseCase) GetIntegrationAccounts(ctx context.Context, req dtos.IntegrationAccountListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	where := models.IntegrationAccountWhere{
		ShowDeleted: req.ShowDeleted,
		TargetCodes: req.TargetCodes,
	}

	if req.IsIncludeGeneral {
		where.ClientIDOrGeneral = claim.GetLoggedInClientID()
	} else {
		where.ClientID = claim.GetLoggedInClientID()
	}

	count, integrationAccounts, err := uc.integrationRepo.GetIntegrationAccountList(ctx, uc.DB.DB(), models.GetIntegrationAccountListParam{
		ListRequest: req.ListRequest,
		Cond: models.IntegrationAccountCondition{
			Where: where,
			Preload: models.IntegrationAccountPreload{
				IntegrationTarget: true,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	respData := make([]dtos.IntegrationAccount, 0, len(integrationAccounts))
	for _, integrationAccount := range integrationAccounts {
		if integrationAccount.IntegrationTarget.AuthCredentialJSONTemplate.Status == pgtype.Undefined {
			integrationAccount.IntegrationTarget.AuthCredentialJSONTemplate.Status = pgtype.Null
		}
		respData = append(respData, dtos.IntegrationAccount{
			ID:         integrationAccount.ID,
			Name:       integrationAccount.Name,
			TargetCode: integrationAccount.TargetCode,
			StatusCode: integrationAccount.StatusCode,
			IntegrationTarget: dtos.IntegrationTarget{
				Code:                       integrationAccount.IntegrationTarget.Code,
				Label:                      integrationAccount.IntegrationTarget.Label,
				Description:                integrationAccount.IntegrationTarget.Description,
				Type:                       integrationAccount.IntegrationTarget.Type,
				AuthCredentialJSONTemplate: integrationAccount.IntegrationTarget.AuthCredentialJSONTemplate,
			},
		})
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respData,
	}, nil

}

func (uc *IntegrationUseCase) GetIntegrationTargets(ctx context.Context, req dtos.IntegrationTargetListReq) (*commonmodel.ListResponse, error) {
	count, integrationTargets, err := uc.integrationRepo.GetIntegrationAccountTargetList(ctx, uc.DB.DB(), models.GetIntegrationTargetListParam{
		ListRequest: req.ListRequest,
		Cond: models.IntegrationTargetCondition{
			Where: models.IntegrationTargetWhere{},
		},
	})
	if err != nil {
		return nil, err
	}

	respData := make([]dtos.IntegrationTarget, 0, len(integrationTargets))
	for _, integrationTarget := range integrationTargets {
		respData = append(respData, dtos.IntegrationTarget{
			Code:                       integrationTarget.Code,
			Label:                      integrationTarget.Label,
			Description:                integrationTarget.Description,
			Type:                       integrationTarget.Type,
			AuthCredentialJSONTemplate: integrationTarget.AuthCredentialJSONTemplate,
		})
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respData,
	}, nil

}

func (uc *IntegrationUseCase) GetIntegrationTargetTypes(ctx context.Context, req dtos.IntegrationTargetTypeListReq) (*commonmodel.ListResponse, error) {
	count, integrationTargetTypes, err := uc.integrationRepo.GetIntegrationAccountTargetTypeList(ctx, uc.DB.DB(), models.GetIntegrationTargetTypeListParam{
		ListRequest: req.ListRequest,
		Cond: models.IntegrationTargetTypeCondition{
			Where: models.IntegrationTargetTypeWhere{
				TypeCode: req.TypeCode,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	respData := make([]dtos.IntegrationTargetType, 0, len(integrationTargetTypes))
	for _, integrationTargetType := range integrationTargetTypes {
		respData = append(respData, dtos.IntegrationTargetType{
			Code:                   integrationTargetType.Code,
			Label:                  integrationTargetType.Label,
			Description:            integrationTargetType.Description,
			IntegrationTypeCode:    integrationTargetType.IntegrationTypeCode,
			IntegrationTargetCode:  integrationTargetType.IntegrationTargetCode,
			IdentifierJSONTemplate: integrationTargetType.IdentifierJSONTemplate,
		})
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respData,
	}, nil

}

func (uc *IntegrationUseCase) GetIntegrationAccount(ctx context.Context, id string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	integrationAccount, err := uc.integrationRepo.GetIntegrationAccount(ctx, uc.DB.DB(), models.IntegrationAccountCondition{
		Where: models.IntegrationAccountWhere{
			ID:                id,
			ClientIDOrGeneral: claim.GetLoggedInClientID(),
			ShowDeleted:       true,
		},
		Preload: models.IntegrationAccountPreload{
			IntegrationTarget: true,
		},
	})
	if err != nil {
		return nil, err
	}

	respData := dtos.IntegrationAccount{
		ID:         id,
		Name:       integrationAccount.Name,
		TargetCode: integrationAccount.TargetCode,
		StatusCode: integrationAccount.StatusCode,
		IntegrationTarget: dtos.IntegrationTarget{
			Code:                       integrationAccount.IntegrationTarget.Code,
			Label:                      integrationAccount.IntegrationTarget.Label,
			Description:                integrationAccount.IntegrationTarget.Description,
			Type:                       integrationAccount.IntegrationTarget.Type,
			AuthCredentialJSONTemplate: integrationAccount.IntegrationTarget.AuthCredentialJSONTemplate,
		},
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        respData,
	}, nil
}

func (uc *IntegrationUseCase) CreateIntegrationAccount(ctx context.Context, req dtos.CreateIntegrationAccount) (*commonmodel.CreateResponse, error) {
	tX, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tX.Rollback()
	authCredential := string(req.AuthCredentialJSON.Bytes)

	if req.TargetCode != constants.INTEGRATION_TARGET_ACCURATE {
		authCredential = cryptohelpers.DefaultEncrypt(string(req.AuthCredentialJSON.Bytes))
	}

	integrationAccount := &models.IntegrationAccount{
		Name:               req.Name,
		TargetCode:         req.TargetCode,
		AuthCredentialJSON: authCredential,
		StatusCode:         constants.INTEGRATION_STATUS_CODE_ACTIVE,
	}
	err = uc.integrationRepo.CreateIntegrationAccount(ctx, tX.DB(), integrationAccount)
	if err != nil {
		return nil, err
	}

	err = tX.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: integrationAccount.ID,
		Data:        nil,
	}, nil
}

func (uc *IntegrationUseCase) ValidateCreateIntegrationAccount(ctx context.Context, req dtos.CreateIntegrationAccount) error {
	switch req.TargetCode {
	case constants.INTEGRATION_TARGET_GPS_ID:
		return uc.ValidateGPSIDAccount(ctx, req)
	}
	return errorhandler.ErrBadRequest(errorconstants.INTEGRATION_ACCOUNT_TYPE_CODE_IS_NOT_VALID)
}

func (uc *IntegrationUseCase) ValidateGPSIDAccount(ctx context.Context, req dtos.CreateIntegrationAccount) error {
	account := models.GPSIDAccount{}
	err := json.Unmarshal(req.AuthCredentialJSON.Bytes, &account)
	if err != nil {
		return err
	}

	_, err = uc.gpsidRepo.Login(ctx, account.Username, account.Password)
	if err != nil {
		return err
	}

	return nil
}

func (uc *IntegrationUseCase) DeleteIntegrationAccount(ctx context.Context, id string) (*commonmodel.DeleteResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	integrationAccount, err := uc.integrationRepo.GetIntegrationAccount(ctx, uc.DB.DB(), models.IntegrationAccountCondition{
		Where: models.IntegrationAccountWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	if integrationAccount.StatusCode == constants.INTEGRATION_STATUS_CODE_DELETED {
		return &commonmodel.DeleteResponse{
			Success:     true,
			Message:     "Success",
			ReferenceID: id,
		}, nil
	}

	updateIntegrationAccount := models.IntegrationAccount{
		StatusCode: constants.INTEGRATION_STATUS_CODE_DELETED,
	}
	err = uc.integrationRepo.UpdateIntegrationAccount(ctx, uc.DB.DB(), id, &updateIntegrationAccount)
	if err != nil {
		return nil, err
	}

	return &commonmodel.DeleteResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
	}, nil
}

func (uc *IntegrationUseCase) UpdateIntegrationAccountCredential(ctx context.Context, id string, req dtos.UpdateIntegrationAccountCredential) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	account, err := uc.integrationRepo.GetIntegrationAccount(ctx, tx.DB(), models.IntegrationAccountCondition{
		Where: models.IntegrationAccountWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}
	authCredential := string(req.AuthCredentialJSON.Bytes)

	if account.TargetCode != constants.INTEGRATION_TARGET_ACCURATE {
		authCredential = cryptohelpers.DefaultEncrypt(string(req.AuthCredentialJSON.Bytes))
	}
	err = uc.integrationRepo.UpdateIntegrationAccount(ctx, tx.DB(), id, &models.IntegrationAccount{
		AuthCredentialJSON: authCredential,
	})
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}, nil
}

func (uc *IntegrationUseCase) UpdateIntegrationAccountStatus(ctx context.Context, id string, newStatus string) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()
	_, err = uc.integrationRepo.GetIntegrationAccount(ctx, tx.DB(), models.IntegrationAccountCondition{
		Where: models.IntegrationAccountWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	err = uc.integrationRepo.UpdateIntegrationAccount(ctx, tx.DB(), id, &models.IntegrationAccount{
		StatusCode: newStatus,
	})
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}, nil
}

func (uc *IntegrationUseCase) GetIntegrations(ctx context.Context, req dtos.IntegrationListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	count, integrationAccounts, err := uc.integrationRepo.GetIntegrationList(ctx, uc.DB.DB(), models.GetIntegrationListParam{
		ListRequest: req.ListRequest,
		Cond: models.IntegrationCondition{
			Where: models.IntegrationWhere{
				ClientID:             claim.GetLoggedInClientID(),
				IsEnableCommand:      req.IsEnableCommand,
				InternalReferenceIDs: req.InternalReferenceIDs,
			},
			Preload: models.IntegrationPreload{
				DataMapping: true,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	respData := make([]dtos.Integration, 0, len(integrationAccounts))
	for _, integration := range integrationAccounts {
		if integration.CustomDataMapping.Status == pgtype.Undefined {
			integration.CustomDataMapping.Status = pgtype.Null
		}

		if integration.DataMapping.DataMapping.Status == pgtype.Undefined {
			integration.DataMapping.DataMapping.Status = pgtype.Null
		}

		respData = append(respData, dtos.Integration{
			ID:                         integration.ID,
			Name:                       integration.Name,
			InternalReferenceID:        integration.InternalReferenceID,
			IntegrationTargetTypeCode:  integration.IntegrationTargetTypeCode,
			IdentifierJSON:             integration.IdentifierJSON,
			IntegrationAccountID:       integration.IntegrationAccountID,
			IntegrationTargetCode:      integration.IntegrationTargetCode,
			StatusCode:                 integration.StatusCode,
			LastSuccessSyncTime:        integration.LastSuccessSyncTime,
			DataMappingCode:            integration.DataMappingCode,
			CustomDataMapping:          integration.CustomDataMapping,
			MachineStatusCode:          integration.MachineStatusCode,
			MachineStatusLastUpdatedAt: integration.MachineStatusLastUpdatedAt,
			IsEnableCommand:            integration.IsEnableCommand,
			ShowDataMappingOnPlatform:  integration.ShowDataMappingOnPlatform,
			ShowDataMappingOnClient:    integration.ShowDataMappingOnClient,
			DataMapping: &dtos.IntegrationDataMappingTemplate{
				Code:        integration.DataMapping.Code,
				Label:       integration.DataMapping.Label,
				Description: integration.DataMapping.Description,
				DataMapping: integration.DataMapping.DataMapping,
				CreatedAt:   integration.DataMapping.CreatedAt,
				UpdatedAt:   integration.DataMapping.UpdatedAt,
			},
		})
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respData,
	}, nil

}

func (uc *IntegrationUseCase) GetIntegrationTrackings(ctx context.Context, req dtos.IntegrationListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	count, integrations, err := uc.integrationRepo.GetIntegrationTrackingList(ctx, uc.DB.DB(), models.GetIntegrationListParam{
		ListRequest: req.ListRequest,
		Cond: models.IntegrationCondition{
			Where: models.IntegrationWhere{
				ClientID: claim.GetLoggedInClientID(),
			},
			Preload: models.IntegrationPreload{
				IntegrationAccount:       true,
				IntegrationAccountTarget: true,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	assetIDs := make([]string, 0, len(integrations))
	for _, integration := range integrations {
		assetIDs = append(assetIDs, integration.InternalReferenceID)
	}

	assetVehicles, err := uc.assetVehicleRepo.GetAssetVehiclesV2(ctx, uc.DB.DB(), assetModel.AssetVehicleCondition{
		Where: assetModel.AssetVehicleWhere{
			AssetIDs: assetIDs,
		},
	})
	if err != nil {
		return nil, err
	}

	mapAssetIDRegistrationNumber := map[string]string{}
	for _, assetVehicle := range assetVehicles {
		mapAssetIDRegistrationNumber[assetVehicle.AssetID] = assetVehicle.RegistrationNumber
	}

	respData := make([]dtos.IntegrationTracking, 0, len(integrations))
	for _, integration := range integrations {
		respData = append(respData, dtos.IntegrationTracking{
			Integration: dtos.Integration{
				ID:                        integration.ID,
				Name:                      integration.Name,
				InternalReferenceID:       integration.InternalReferenceID,
				IntegrationTargetTypeCode: integration.IntegrationTargetTypeCode,
				IdentifierJSON:            integration.IdentifierJSON,
				IntegrationAccountID:      integration.IntegrationAccountID,
				IntegrationTargetCode:     integration.IntegrationTargetCode,
				StatusCode:                integration.StatusCode,
				LastSuccessSyncTime:       integration.LastSuccessSyncTime,
			},
			IntegrationAccount: dtos.IntegrationAccount{
				ID:         integration.IntegrationAccount.ID,
				Name:       integration.IntegrationAccount.Name,
				TargetCode: integration.IntegrationAccount.TargetCode,
				StatusCode: integration.IntegrationAccount.StatusCode,
				IntegrationTarget: dtos.IntegrationTarget{
					Code:                       integration.IntegrationAccount.IntegrationTarget.Code,
					Label:                      integration.IntegrationAccount.IntegrationTarget.Label,
					Description:                integration.IntegrationAccount.IntegrationTarget.Description,
					Type:                       integration.IntegrationAccount.IntegrationTarget.Type,
					AuthCredentialJSONTemplate: integration.IntegrationAccount.IntegrationTarget.AuthCredentialJSONTemplate,
				},
			},
			VehicleRegistrationNumber: mapAssetIDRegistrationNumber[integration.InternalReferenceID],
		})
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respData,
	}, nil

}

func (uc *IntegrationUseCase) CreateIntegration(ctx context.Context, req dtos.CreateIntegration) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	integrationAccount, err := uc.integrationRepo.GetIntegrationAccount(ctx, uc.DB.DB(), models.IntegrationAccountCondition{
		Where: models.IntegrationAccountWhere{
			ID:                req.IntegrationAccountID,
			ClientIDOrGeneral: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	integrationTargetType, err := uc.integrationRepo.GetIntegrationAccountTargetType(ctx, uc.DB.DB(), models.IntegrationTargetTypeCondition{
		Where: models.IntegrationTargetTypeWhere{
			Code: req.IntegrationTargetTypeCode,
		},
	})
	if err != nil {
		return nil, err
	}

	// Remove Only One Tracking Integration
	// switch integrationTargetType.IntegrationTypeCode {
	// case constants.INTEGRATION_TYPE_CODE_TRACKING:
	// 	err = uc.ValidateCreateTrackingIntegration(ctx, req)
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// }

	tX, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tX.Rollback()

	integrationStatus := constants.INTEGRATION_STATUS_CODE_ACTIVE

	integration := &models.Integration{
		Name:                      req.Name,
		InternalReferenceID:       req.InternalReferenceID,
		IntegrationTargetTypeCode: req.IntegrationTargetTypeCode,
		IdentifierJSON:            req.IdentifierJSON,
		IntegrationAccountID:      req.IntegrationAccountID,
		IntegrationTargetCode:     integrationAccount.TargetCode,
		StatusCode:                integrationStatus,
		DataMappingCode:           req.DataMappingCode,
		CustomDataMapping:         req.CustomDataMapping,
		ShowDataMappingOnPlatform: req.ShowDataMappingOnPlatform,
		ShowDataMappingOnClient:   req.ShowDataMappingOnClient,
	}
	err = uc.integrationRepo.CreateIntegration(ctx, tX.DB(), integration)
	if err != nil {
		return nil, err
	}

	switch integrationTargetType.IntegrationTypeCode {
	case constants.INTEGRATION_TYPE_CODE_TRACKING:
		err = uc.CreateTrackingIntegration(ctx, integrationAccount, integration)
		if err != nil {
			return nil, err
		}
	}

	err = tX.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: integration.ID,
		Data:        nil,
	}, nil
}

func (uc *IntegrationUseCase) ValidateCreateTrackingIntegration(ctx context.Context, req dtos.CreateIntegration) error {
	_, err := uc.integrationRepo.GetIntegration(ctx, uc.DB.DB(), models.IntegrationCondition{
		Where: models.IntegrationWhere{
			InternalReferenceID: req.InternalReferenceID,
			IntegrationTypeCode: constants.INTEGRATION_TYPE_CODE_TRACKING,
		},
	})
	if err != nil {
		return nil
	}

	return errorhandler.ErrBadRequest("vehicle already have other integration")
}

func (uc *IntegrationUseCase) GetFirstVehicleMeterFromGPSID(
	ctx context.Context,
	integrationAccount *models.IntegrationAccount,
	integration *models.Integration,
) (int, error) {
	account := models.GPSIDAccount{}
	err := json.Unmarshal([]byte(cryptohelpers.DefaultDecrypt(integrationAccount.AuthCredentialJSON)), &account)
	if err != nil {
		return 0, err
	}

	token, err := uc.gpsidRepo.Login(ctx, account.Username, account.Password)
	if err != nil {
		return 0, err
	}
	identifier := models.GPSIDTrackingIdentifier{}
	err = json.Unmarshal(integration.IdentifierJSON.Bytes, &identifier)
	if err != nil {
		return 0, err
	}

	vehicle, err := uc.gpsidRepo.GetVehicleByImei(ctx, token, identifier.Imei)
	if err != nil {
		return 0, err
	}
	return vehicle.Mileage, nil
}

func (uc *IntegrationUseCase) CreateTrackingIntegration(ctx context.Context, integrationAccount *models.IntegrationAccount, integration *models.Integration) error {
	assetVehicle, err := uc.assetVehicleRepo.GetAssetVehicle(ctx, uc.DB.DB(), assetModel.AssetVehicleCondition{
		Where: assetModel.AssetVehicleWhere{
			AssetID: integration.InternalReferenceID,
		},
	})
	if err != nil {
		return err
	}

	firstGPSKM := 0
	switch integration.IntegrationTargetTypeCode {
	case constants.INTEGRATION_TARGET_TYPE_GPS_ID_TRACKING:
		var err error
		firstGPSKM, err = uc.GetFirstVehicleMeterFromGPSID(ctx, integrationAccount, integration)
		if err != nil {
			return err
		}
	}

	err = uc.trackingRepo.CreatePreGPSVehicleMeter(ctx, uc.DBTimeScale.WithCtx(ctx).DB(), &geoModel.PreGPSVehicleMeter{
		IntegrationID:  integration.ID,
		AssetID:        integration.InternalReferenceID,
		FirstVehicleKM: int(assetVehicle.VehicleKM),
		FirstGPSKM:     firstGPSKM,
		TargetCode:     constants.MapIntegrationTrackingTypeCode()[integration.IntegrationTargetTypeCode],
	})
	if err != nil {
		return err
	}
	return nil
}

func (uc *IntegrationUseCase) UpdateTrackingIntegration(ctx context.Context, integrationAccount *models.IntegrationAccount, integration *models.Integration) error {
	assetVehicle, err := uc.assetVehicleRepo.GetAssetVehicle(ctx, uc.DB.DB(), assetModel.AssetVehicleCondition{
		Where: assetModel.AssetVehicleWhere{
			AssetID: integration.InternalReferenceID,
		},
	})
	if err != nil {
		return err
	}

	firstGPSKM := 0
	switch integration.IntegrationTargetTypeCode {
	case constants.INTEGRATION_TARGET_TYPE_GPS_ID_TRACKING:
		var err error
		firstGPSKM, err = uc.GetFirstVehicleMeterFromGPSID(ctx, integrationAccount, integration)
		if err != nil {
			return err
		}
	}

	preGPSVehicleMeter, err := uc.trackingRepo.GetPreGPSVehicleMeter(ctx, uc.DBTimeScale.DB(), geoModel.PreGPSVehicleMeterCondition{
		Where: geoModel.PreGPSVehicleMeterWhere{
			IntegrationID: integration.ID,
		},
	})
	if err != nil {
		return err
	}

	err = uc.trackingRepo.UpdatePreGPSVehicleMeter(ctx, uc.DBTimeScale.WithCtx(ctx).DB(), preGPSVehicleMeter.ID, &geoModel.PreGPSVehicleMeter{
		FirstVehicleKM: int(assetVehicle.VehicleKM),
		FirstGPSKM:     firstGPSKM,
	})
	if err != nil {
		return err
	}

	return nil
}

func (uc *IntegrationUseCase) UpdateIntegrationIdentifier(ctx context.Context, id string, req dtos.UpdateIntegrationIdentifier) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()
	integration, err := uc.integrationRepo.GetIntegration(ctx, tx.DB(), models.IntegrationCondition{
		Where: models.IntegrationWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
		Preload: models.IntegrationPreload{
			IntegrationAccount: true,
		},
	})
	if err != nil {
		return nil, err
	}

	err = uc.integrationRepo.UpdateIntegration(ctx, tx.DB(), id, &models.Integration{
		IdentifierJSON: req.IdentifierJSON,
	})
	if err != nil {
		return nil, err
	}

	integrationTargetType, err := uc.integrationRepo.GetIntegrationAccountTargetType(ctx, uc.DB.DB(), models.IntegrationTargetTypeCondition{
		Where: models.IntegrationTargetTypeWhere{
			Code: integration.IntegrationTargetTypeCode,
		},
	})
	if err != nil {
		return nil, err
	}

	switch integrationTargetType.IntegrationTypeCode {
	case constants.INTEGRATION_TYPE_CODE_TRACKING:
		uc.UpdateTrackingIntegration(ctx, &integration.IntegrationAccount, integration)
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}, nil
}

func (uc *IntegrationUseCase) UpdateIntegrationDataMapping(ctx context.Context, id string, req dtos.UpdateIntegrationDataMapping) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	_, err = uc.integrationRepo.GetIntegration(ctx, uc.DB.DB(), models.IntegrationCondition{
		Where: models.IntegrationWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
		Preload: models.IntegrationPreload{
			IntegrationAccount: true,
		},
	})
	if err != nil {
		return nil, err
	}

	err = uc.integrationRepo.UpdateIntegration(ctx, uc.DB.WithCtx(ctx).DB(), id, &models.Integration{
		DataMappingCode:   req.DataMappingCode,
		CustomDataMapping: req.CustomDataMapping,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}, nil
}

func (uc *IntegrationUseCase) UpdateShowDataMappingOnPlatform(ctx context.Context, id string, req dtos.UpdateIntegrationShowDataMappingOnPlatform) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	_, err = uc.integrationRepo.GetIntegration(ctx, uc.DB.DB(), models.IntegrationCondition{
		Where: models.IntegrationWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
		Preload: models.IntegrationPreload{
			IntegrationAccount: true,
		},
	})
	if err != nil {
		return nil, err
	}

	err = uc.integrationRepo.UpdateIntegration(ctx, uc.DB.WithCtx(ctx).DB(), id, &models.Integration{
		ShowDataMappingOnPlatform: req.ShowDataMappingOnPlatform,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}, nil
}

func (uc *IntegrationUseCase) UpdateShowDataMappingOnClient(ctx context.Context, id string, req dtos.UpdateIntegrationShowDataMappingOnClient) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	_, err = uc.integrationRepo.GetIntegration(ctx, uc.DB.DB(), models.IntegrationCondition{
		Where: models.IntegrationWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
		Preload: models.IntegrationPreload{
			IntegrationAccount: true,
		},
	})
	if err != nil {
		return nil, err
	}

	err = uc.integrationRepo.UpdateIntegration(ctx, uc.DB.WithCtx(ctx).DB(), id, &models.Integration{
		ShowDataMappingOnClient: req.ShowDataMappingOnClient,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}, nil
}

func (uc *IntegrationUseCase) ValidateCreateIntegration(ctx context.Context, req dtos.CreateIntegration) error {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}

	integrationAccount, err := uc.integrationRepo.GetIntegrationAccount(ctx, uc.DB.DB(), models.IntegrationAccountCondition{
		Where: models.IntegrationAccountWhere{
			ID:                req.IntegrationAccountID,
			ClientIDOrGeneral: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return err
	}

	switch req.IntegrationTargetTypeCode {
	case constants.INTEGRATION_TARGET_TYPE_GPS_ID_TRACKING:
		if integrationAccount.TargetCode != constants.INTEGRATION_TARGET_GPS_ID {
			return errorhandler.ErrBadRequest(errorconstants.INTEGRATION_TYPE_CODE_NOT_MATCH_WITH_INTEGRATION_ACCOUNT)
		}

		return uc.ValidateCreateIntegrationGPSID(ctx, req, integrationAccount)
	}

	return nil
}

func (uc *IntegrationUseCase) ValidateCreateIntegrationGPSID(ctx context.Context, req dtos.CreateIntegration, integrationAccount *models.IntegrationAccount) error {
	account := models.GPSIDAccount{}
	err := json.Unmarshal([]byte(cryptohelpers.DefaultDecrypt(integrationAccount.AuthCredentialJSON)), &account)
	if err != nil {
		return err
	}

	token, err := uc.gpsidRepo.Login(ctx, account.Username, account.Password)
	if err != nil {
		return err
	}

	identifier := models.GPSIDTrackingIdentifier{}
	err = json.Unmarshal(req.IdentifierJSON.Bytes, &identifier)
	if err != nil {
		return err
	}

	err = uc.gpsidRepo.ValidateVehicleByImei(ctx, token, identifier.Imei)
	if err != nil {
		return err
	}

	return nil
}

func (uc *IntegrationUseCase) GetIntegration(ctx context.Context, id string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	integration, err := uc.integrationRepo.GetIntegration(ctx, uc.DB.DB(), models.IntegrationCondition{
		Where: models.IntegrationWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
		Preload: models.IntegrationPreload{
			DataMapping: true,
		},
	})
	if err != nil {
		return nil, err
	}

	if integration.CustomDataMapping.Status == pgtype.Undefined {
		integration.CustomDataMapping.Status = pgtype.Null
	}

	if integration.DataMapping.DataMapping.Status == pgtype.Undefined {
		integration.DataMapping.DataMapping.Status = pgtype.Null
	}

	respData := dtos.Integration{
		ID:                         id,
		Name:                       integration.Name,
		InternalReferenceID:        integration.InternalReferenceID,
		IntegrationTargetTypeCode:  integration.IntegrationTargetTypeCode,
		IdentifierJSON:             integration.IdentifierJSON,
		IntegrationAccountID:       integration.IntegrationAccountID,
		IntegrationTargetCode:      integration.IntegrationTargetCode,
		StatusCode:                 integration.StatusCode,
		LastSuccessSyncTime:        integration.LastSuccessSyncTime,
		DataMappingCode:            integration.DataMappingCode,
		CustomDataMapping:          integration.CustomDataMapping,
		MachineStatusCode:          integration.MachineStatusCode,
		IsEnableCommand:            integration.IsEnableCommand,
		MachineStatusLastUpdatedAt: integration.MachineStatusLastUpdatedAt,
		ShowDataMappingOnPlatform:  integration.ShowDataMappingOnPlatform,
		ShowDataMappingOnClient:    integration.ShowDataMappingOnClient,
		DataMapping: &dtos.IntegrationDataMappingTemplate{
			Code:        integration.DataMapping.Code,
			Label:       integration.DataMapping.Label,
			Description: integration.DataMapping.Description,
			DataMapping: integration.DataMapping.DataMapping,
			CreatedAt:   integration.DataMapping.CreatedAt,
			UpdatedAt:   integration.DataMapping.UpdatedAt,
		},
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        respData,
	}, nil
}

func (uc *IntegrationUseCase) DeleteIntegration(ctx context.Context, id string) (*commonmodel.DeleteResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	_, err = uc.integrationRepo.GetIntegration(ctx, uc.DB.DB(), models.IntegrationCondition{
		Where: models.IntegrationWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	err = uc.integrationRepo.DeleteIntegration(ctx, uc.DB.DB(), id)
	if err != nil {
		return nil, err
	}

	return &commonmodel.DeleteResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
	}, nil
}

func (uc *IntegrationUseCase) GetIntegrationCommands(ctx context.Context, req dtos.IntegrationCommandListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	count, integrationCommands, err := uc.integrationRepo.GetIntegrationCommandsList(ctx, uc.DB.DB(), models.IntegrationCommandsListParam{
		ListRequest: req.ListRequest,
		Cond: models.IntegrationCommandsCondition{
			Where: models.IntegrationCommandsWhere{
				ClientID: claim.GetLoggedInClientID(),
			},
			Preload: models.IntegrationCommandsPreload{
				IntegrationCommandStatus: true,
				IntegrationCommandType:   true,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	userIds := []string{}
	for _, val := range integrationCommands {
		userIds = append(userIds, val.CreatedBy)
	}
	users := []userModel.User{}
	err = uc.userRepo.GetUsersByIds(ctx, uc.DB.DB(), &users, userIds)
	if err != nil {
		return nil, err
	}
	mapsUserIds := map[string]userModel.User{}
	for _, val := range users {
		mapsUserIds[val.ID] = val
	}

	response := []dtos.IntegrationCommandsResp{}
	for _, val := range integrationCommands {
		dto := dtos.IntegrationCommandsResp{}
		dto.Set(val, mapsUserIds)
		response = append(response, dto)
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         response,
	}, nil

}

func (uc *IntegrationUseCase) Integrates(ctx context.Context, req dtos.Integrates) (*commonmodel.DetailResponse, error) {
	switch req.IntegrationTargetTypeCode {
	case constants.INTEGRATION_TARGET_TYPE_GPS_ID_TRACKING:
		return uc.IntegratesGPSIDTracking(ctx, req)
	}

	return nil, errorhandler.ErrBadRequest(errorconstants.INTEGRATION_TYPE_CODE_NOT_SUPPORTED)
}

func (uc *IntegrationUseCase) GetIntegrationVehicles(ctx context.Context, req dtos.IntegrationListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	count, integrations, err := uc.integrationRepo.GetIntegrationVehicleList(ctx, uc.DB.DB(), models.GetIntegrationListParam{
		ListRequest: req.ListRequest,
		Cond: models.IntegrationCondition{
			Where: models.IntegrationWhere{
				ClientID:            claim.GetLoggedInClientID(),
				IntegrationTypeCode: constants.INTEGRATION_TYPE_CODE_TRACKING,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	assetIds := []string{}
	for _, val := range integrations {
		assetIds = append(assetIds, val.InternalReferenceID)
	}

	assets, err := uc.assetRepo.GetAssets(ctx, uc.DB.DB(), assetModel.AssetCondition{
		Where: assetModel.AssetWhere{
			IDs:      assetIds,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}
	mapsAsset := map[string]assetModel.Asset{}
	for _, val := range assets {
		mapsAsset[val.ID] = val
	}
	var assetAssignments []assetModel.AssetAssignment
	err = uc.assetAssignmentRepo.GetAssetAssignmentByAssetIds(ctx, uc.DB.DB(), &assetAssignments, assetIds)
	if err != nil {
		return nil, err
	}
	mapAssetUserID := map[string]string{}
	for _, assignment := range assetAssignments {
		mapAssetUserID[assignment.AssetID] = assignment.UserID
	}
	var userIds []string
	for _, assignment := range assetAssignments {
		userIds = append(userIds, assignment.UserID)
	}
	mapUser := map[string]userModel.User{}
	err = uc.userRepo.GetUsersInMapByIds(ctx, uc.DB.DB(), &mapUser, userIds)
	if err != nil {
		return nil, err
	}
	respData := make([]dtos.IntegrationVehicle, 0, len(integrations))
	for _, integration := range integrations {
		respData = append(respData, dtos.IntegrationVehicle{
			ID:                        integration.ID,
			Name:                      integration.Name,
			InternalReferenceID:       integration.InternalReferenceID,
			IntegrationTargetTypeCode: integration.IntegrationTargetTypeCode,
			IdentifierJSON:            integration.IdentifierJSON,
			IntegrationAccountID:      integration.IntegrationAccountID,
			IntegrationTargetCode:     integration.IntegrationTargetCode,
			StatusCode:                integration.StatusCode,
			LastSuccessSyncTime:       integration.LastSuccessSyncTime,
			AssetSerialNumber:         mapsAsset[integration.InternalReferenceID].SerialNumber,
			AssetAssignedUserID:       mapAssetUserID[integration.InternalReferenceID],
			AssetAssignedUserName:     mapUser[mapAssetUserID[integration.InternalReferenceID]].GetName(),
			AssetCategoryCode:         mapsAsset[integration.InternalReferenceID].AssetCategoryCode,
			AssetReferenceNumber:      mapsAsset[integration.InternalReferenceID].ReferenceNumber,
		})
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respData,
	}, nil

}

func (uc *IntegrationUseCase) UpsertIntegrationSessionAccurate(ctx context.Context, req dtos.UpsertIntegrationSessionAccurateReq) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	integrationAccount, err := uc.integrationRepo.GetIntegrationAccount(ctx, uc.DB.DB(), models.IntegrationAccountCondition{
		Where: models.IntegrationAccountWhere{
			TargetCode: constants.INTEGRATION_TARGET_ACCURATE,
			ClientID:   claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	account := models.AccurateAccount{}
	err = json.Unmarshal([]byte(integrationAccount.AuthCredentialJSON), &account)
	if err != nil {
		return nil, err
	}

	accurateOpenDBResp, err := uc.accurateRepo.OpenDB(ctx, req.Token, account.DatabaseID)
	if err != nil {
		return nil, err
	}

	accurateOpenDBResp.Token = req.Token

	data := pgtype.JSONB{}
	data.Set(&accurateOpenDBResp)

	expiredTime, _ := time.Parse("02/01/2006", accurateOpenDBResp.AccessibleUntil)
	err = uc.integrationRepo.UpsertIntegrationSession(ctx, uc.DB.WithCtx(ctx).DB(), &models.IntegrationSession{
		IntegrationAccountID: integrationAccount.ID,
		ExpiredTime:          expiredTime,
		SessionTypeCode:      constants.INTEGRATION_TARGET_ACCURATE,
		Data:                 data,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        data,
	}, nil
}

func (uc *IntegrationUseCase) GetIntegrationTranslogicCalibrations(ctx context.Context, req dtos.IntegrationTranslogicCalibrationListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	where := models.IntegrationTranslogicCalibrationWhere{
		ClientID: claim.GetLoggedInClientID(),
	}

	count, translogicCalibrations, err := uc.integrationRepo.GetIntegrationTranslogicCalibrationList(ctx, uc.DB.DB(), models.GetIntegrationTranslogicCalibrationListParam{
		ListRequest: req.ListRequest,
		Cond: models.IntegrationTranslogicCalibrationCondition{
			Where: where,
		},
	})
	if err != nil {
		return nil, err
	}

	respData := make([]dtos.IntegrationTranslogicCalibrationRes, 0, len(translogicCalibrations))
	for _, translogicCalibration := range translogicCalibrations {
		respData = append(respData, dtos.IntegrationTranslogicCalibrationRes{
			ID:                      translogicCalibration.ID,
			Number:                  translogicCalibration.Number,
			StandardTreadDepthValue: translogicCalibration.StandardTreadDepthValue,
			DeviceID:                translogicCalibration.DeviceID,
			IdleValue:               translogicCalibration.IdleValue,
			TreadDepthValue:         translogicCalibration.TreadDepthValue,
			PressureValue:           translogicCalibration.PressureValue,
			CreatedAt:               translogicCalibration.CreatedAt,
			CreatedBy:               translogicCalibration.CreatedBy,
		})
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respData,
	}, nil
}

func (uc *IntegrationUseCase) CreateTranslogicCalibration(ctx context.Context, req dtos.CreateTranslogicCalibration) (*commonmodel.CreateResponse, error) {
	tX, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tX.Rollback()

	translogicCalibration := &models.IntegrationTranslogicCalibration{
		StandardTreadDepthValue: req.StandardTreadDepthValue,
		DeviceID:                req.DeviceID,
		IdleValue:               req.IdleValue,
		TreadDepthValue:         req.TreadDepthValue,
		PressureValue:           req.PressureValue,
	}
	err = uc.integrationRepo.CreateTranslogicCalibration(ctx, tX.DB(), translogicCalibration)
	if err != nil {
		return nil, err
	}

	err = tX.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: translogicCalibration.ID,
		Data:        nil,
	}, nil
}

func (uc *IntegrationUseCase) GetIntegrationMonitorigs(ctx context.Context, req dtos.IntegrationMonitoringListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	hideOnMonitoring := true
	if req.HideOnMonitoring != "" {
		hideOnMonitoring, err = strconv.ParseBool(strings.ToLower(req.HideOnMonitoring))
		if err != nil {
			return nil, err
		}
	}

	count, internalRefIDs, err := uc.integrationRepo.GetIntegrationDistinctRefIDList(ctx, uc.DB.DB(), models.GetIntegrationListParam{
		ListRequest: req.ListRequest,
		Cond: models.IntegrationCondition{
			Where: models.IntegrationWhere{
				ClientID:             claim.GetLoggedInClientID(),
				InternalReferenceIDs: req.AssetIDs,
				HideOnMonitoring:     hideOnMonitoring,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	assets, err := uc.assetRepo.GetAssets(ctx, uc.DB.DB(), assetModel.AssetCondition{
		Where: assetModel.AssetWhere{
			IDs: internalRefIDs,
		},
		Preload: assetModel.AssetPreload{
			SubCategory: true,
		},
	})
	if err != nil {
		return nil, err
	}

	mapAssets := map[string]assetModel.Asset{}
	for i := range assets {
		mapAssets[assets[i].ID] = assets[i]
	}

	alertCounts, err := uc.alertRepo.GetAlertCountByAssetIDs(ctx, uc.DB.DB(), internalRefIDs)
	if err != nil {
		return nil, err
	}

	alertCountsByRelatedAsset, err := uc.alertRepo.GetAlertCountByRelatedAssetIDs(ctx, uc.DB.DB(), internalRefIDs)
	if err != nil {
		return nil, err
	}

	mapAlertCounts := map[string]int{}
	for i := range alertCounts {
		mapAlertCounts[alertCounts[i].AssetID] = alertCounts[i].Count
	}

	for i := range alertCountsByRelatedAsset {
		mapAlertCounts[alertCountsByRelatedAsset[i].AssetID] = alertCountsByRelatedAsset[i].Count
	}

	needGetCanBusSensorData := false
	needGetCompressorSensorData := false
	needGetGeoLocationHistory := false
	needGetGeneralSensorData := true
	needGetTyreSensorData := false
	needGetTyreChangerData := false
	needGetTyreChangerStateData := false

	respData := make([]dtos.IntegrationMonitoring, 0, len(internalRefIDs))
	for _, assetID := range internalRefIDs {
		asset := mapAssets[assetID]
		item := dtos.IntegrationMonitoring{
			AssetID:              asset.ID,
			AssetReferenceNumber: asset.ReferenceNumber,
			AssetSerialNumber:    asset.SerialNumber,
			AssetName:            asset.Name,
			AssetSubCategory:     commonmodel.ConstantModel(asset.SubCategory),
			AlertCount:           mapAlertCounts[assetID],
		}

		if needGetCanBusSensorData {
			canBusSensorData, err := uc.trackingRepo.GetLatestCanBusData(ctx, uc.DBTimeScale.DB(), assetID, "")
			if err != nil {
				return nil, err
			}

			if canBusSensorData.AssetID != "" {
				item.CanBusSensorData = canBusSensorData
			}
		}

		if needGetCompressorSensorData {
			compressorSensorData, err := uc.trackingRepo.GetLatestCompressorData(ctx, uc.DBTimeScale.DB(), assetID, "")
			if err != nil {
				return nil, err
			}

			if compressorSensorData.AssetID != "" {
				item.CompressorSensorData = compressorSensorData
			}

		}

		if needGetGeoLocationHistory {
			geoLocationHistory, err := uc.trackingRepo.GetLatestTrackingPosition(ctx, uc.DBTimeScale.DB(), assetID, "")
			if err != nil {
				return nil, err
			}

			if geoLocationHistory.AssetID != "" {
				item.GeoLocationHistory = geoLocationHistory
			}
		}

		if needGetGeneralSensorData {
			generalSensorData, err := uc.trackingRepo.GetLatestGeneralData(ctx, uc.DBTimeScale.DB(), assetID, "")
			if err != nil {
				return nil, err
			}

			if generalSensorData.AssetID != "" {
				item.GeneralSensorData = generalSensorData
			}
		}

		if needGetTyreSensorData {
			tyreSensorData, err := uc.trackingRepo.GetLatestTyreSensorData(ctx, uc.DBTimeScale.DB(), geoModel.GetLatestTrackingParam{
				AssetID: assetID,
			})
			if err != nil {
				return nil, err
			}

			if tyreSensorData.AssetID != "" {
				item.TyreSensorData = tyreSensorData
			}
		}

		if needGetTyreChangerData {
			tyreChangerData, err := uc.trackingRepo.GetLatestTyreChangerData(ctx, uc.DBTimeScale.DB(), geoModel.GetLatestTrackingParam{
				AssetID: assetID,
			})
			if err != nil {
				return nil, err
			}

			if tyreChangerData.AssetID != "" {
				item.TyreChangerData = tyreChangerData
			}
		}

		if needGetTyreChangerStateData {
			tyreChangerStateData, err := uc.trackingRepo.GetLatestTyreChangerStateData(ctx, uc.DBTimeScale.DB(), geoModel.GetLatestTrackingParam{
				AssetID: assetID,
			})
			if err != nil {
				return nil, err
			}

			if tyreChangerStateData.AssetID != "" {
				item.TyreChangerStateData = tyreChangerStateData
			}
		}

		respData = append(respData, item)
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respData,
	}, nil

}

func (uc *IntegrationUseCase) generateTranslogicCalibrationExportSignedUrl(ctx context.Context, xlsx *excelize.File) (string, *multipart.FileHeader, error) {
	body := new(bytes.Buffer)
	writer := multipart.NewWriter(body)
	part, err := writer.CreateFormFile("file", "Riwayat_Kalibrasi.xlsx")
	if err != nil {
		return "", nil, err
	}
	xlsxBuffer, err := xlsx.WriteToBuffer()
	if err != nil {
		return "", nil, err
	}
	fileContents, err := ioutil.ReadAll(xlsxBuffer)
	if err != nil {
		return "", nil, err
	}
	part.Write(fileContents)
	err = writer.Close()
	if err != nil {
		return "", nil, err
	}

	xlsxRequest, _ := http.NewRequest("POST", "", body)
	xlsxRequest.Header.Add("Content-Type", writer.FormDataContentType())
	xlsxFile, xlsxHeader, err := xlsxRequest.FormFile("file")
	if err != nil {
		return "", nil, err
	}
	defer xlsxFile.Close()

	xlsxHeader.Filename = storageConstants.TEMP_USER_EXPORT_PREFIX + "Riwayat_Kalibrasi_" + helpers.GenerateSecureFileName(".xlsx")
	err = uc.StorageRepository.UploadFile(ctx, xlsxFile, xlsxHeader)
	if err != nil {
		return "", nil, err
	}

	signedUrl, err := uc.StorageRepository.GetFileSignedURL(ctx, xlsxHeader.Filename, time.Now().Add(24*time.Hour))
	if err != nil {
		return "", nil, err
	}

	return signedUrl, xlsxHeader, nil
}

func (uc *IntegrationUseCase) ExportIntegrationTranslogicCalibrations(ctx context.Context, req dtos.IntegrationTranslogicCalibrationListReq) (commonmodel.DetailResponse, error) {
	var generateExcelResponse commonmodel.DetailResponse

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return generateExcelResponse, err
	}

	where := models.IntegrationTranslogicCalibrationWhere{
		ClientID: claim.GetLoggedInClientID(),
	}

	_, translogicCalibrations, err := uc.integrationRepo.GetIntegrationTranslogicCalibrationList(ctx, uc.DB.DB(), models.GetIntegrationTranslogicCalibrationListParam{
		ListRequest: req.ListRequest,
		Cond: models.IntegrationTranslogicCalibrationCondition{
			Where: where,
		},
	})
	if err != nil {
		return generateExcelResponse, err
	}

	var userIds []string
	respData := make([]dtos.IntegrationTranslogicCalibrationRes, 0, len(translogicCalibrations))
	for _, translogicCalibration := range translogicCalibrations {
		respData = append(respData, dtos.IntegrationTranslogicCalibrationRes{
			ID:                      translogicCalibration.ID,
			Number:                  translogicCalibration.Number,
			StandardTreadDepthValue: translogicCalibration.StandardTreadDepthValue,
			DeviceID:                translogicCalibration.DeviceID,
			IdleValue:               translogicCalibration.IdleValue,
			TreadDepthValue:         translogicCalibration.TreadDepthValue,
			PressureValue:           translogicCalibration.PressureValue,
			CreatedAt:               translogicCalibration.CreatedAt,
			CreatedBy:               translogicCalibration.CreatedBy,
		})

		userIds = append(userIds, translogicCalibration.CreatedBy)
	}

	usersMapById := map[string]userModel.User{}

	err = uc.userRepo.GetUsersInMapByIds(ctx, uc.DB.DB(), &usersMapById, userIds)
	if err != nil {
		commonlogger.Errorf("Error in getting users by user ids from identity service", err)
		return generateExcelResponse, err
	}

	// START GENERATE EXCEL
	// INITIAL DECLARATION
	xlsx := excelize.NewFile()
	sheetName := "TyreOptimaX DigiSpect"
	xlsx.SetSheetName(xlsx.GetSheetName(0), sheetName)
	xlsx.SetColWidth(sheetName, "B", "C", 16)
	xlsx.SetColWidth(sheetName, "F", "I", 16)

	// SET HEADER BG
	headerStyle, err := xlsx.NewStyle(&excelize.Style{
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#F1F6FF"},
		},
	})
	if err != nil {
		commonlogger.Errorf("Error in styling to excel file", err.Error())
		return generateExcelResponse, err
	}
	xlsx.SetCellStyle(sheetName, "A1", "I7", headerStyle)

	// SET HEADER TITLE AND IMAGE
	title := "Riwayat Kalibrasi"
	titleStyle, err := xlsx.NewStyle(&excelize.Style{
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#F1F6FF"},
		},
		Font: &excelize.Font{
			Size: 27,
			Bold: true,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
	})
	if err != nil {
		commonlogger.Errorf("Error in styling to excel file", err.Error())
		return generateExcelResponse, err
	}
	xlsx.MergeCell(sheetName, "A2", "I4")
	xlsx.SetCellStr(sheetName, "A2", title)
	xlsx.SetCellStyle(sheetName, "A2", "A2", titleStyle)
	if err := xlsx.AddPicture(sheetName, "A1", "./statics/tyre-optimax-digispect.png", &excelize.GraphicOptions{ScaleX: 0.4, ScaleY: 0.4}); err != nil {
		commonlogger.Errorf("Error in adding image to excel file", err)
		return generateExcelResponse, err
	}
	if err := xlsx.AddPicture(sheetName, "H2", "./statics/logo-alt-1.png", &excelize.GraphicOptions{ScaleX: 0.3, ScaleY: 0.3}); err != nil {
		commonlogger.Errorf("Error in adding image to excel file", err)
		return generateExcelResponse, err
	}

	// SET DOWNLOAD DATE
	downloadDateStyle, err := xlsx.NewStyle(&excelize.Style{
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#F1F6FF"},
		},
		Font: &excelize.Font{
			Size:  9,
			Color: "#1F1F1F",
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
	})
	now := time.Now()
	username := claim.GetName()
	downloadDate := "downloaded by " + username + " at " + now.Format("02-Jan-2006 15:04")
	xlsx.MergeCell(sheetName, "A5", "I5")
	xlsx.SetCellStr(sheetName, "A5", downloadDate)
	xlsx.SetCellStyle(sheetName, "A5", "I5", downloadDateStyle)

	// SET TABLE TITLES
	tableTitleStyle, err := xlsx.NewStyle(&excelize.Style{
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#185FFF"},
		},
		Font: &excelize.Font{
			Size:  10,
			Bold:  true,
			Color: "#FFFFFF",
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "top",
			WrapText:   true,
		},
		Border: []excelize.Border{
			{
				Type:  "top",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "left",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "right",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "bottom",
				Color: "#000000",
				Style: 4,
			},
		},
	})
	tableTitles := []map[string]interface{}{
		{"column": "A8", "value": "No"},
		{"column": "B8", "value": "ID Perangkat"},
		{"column": "C8", "value": "ID Kalibrasi"},
		{"column": "D8", "value": "Tanggal"},
		{"column": "E8", "value": "Waktu"},
		{"column": "F8", "value": "Panjang Jarum Alat Sebelum Digunakan"},
		{"column": "G8", "value": "Panjang Jarum Alat di 0 mm"},
		{"column": "H8", "value": "Panjang Jarum Alat di 16 mm"},
		{"column": "I8", "value": "Dilakukan oleh"},
	}
	for _, item := range tableTitles {
		title, _ := item["value"].(string)
		col, _ := item["column"].(string)
		xlsx.SetCellStr(sheetName, col, title)
	}
	lastCol, _ := tableTitles[len(tableTitles)-1]["column"].(string)
	xlsx.SetCellStyle(sheetName, "A8", lastCol, tableTitleStyle)

	// SET TABLE VALUES
	tableValueStyle, err := xlsx.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Size:  10,
			Color: "#000000",
		},
		Border: []excelize.Border{
			{
				Type:  "top",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "left",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "right",
				Color: "#000000",
				Style: 4,
			},
			{
				Type:  "bottom",
				Color: "#000000",
				Style: 4,
			},
		},
	})
	for idx, item := range respData {
		xlsx.SetCellValue(sheetName, fmt.Sprintf("A%d", idx+9), idx+1)
		xlsx.SetCellValue(sheetName, fmt.Sprintf("B%d", idx+9), item.DeviceID)
		xlsx.SetCellValue(sheetName, fmt.Sprintf("C%d", idx+9), item.ID)
		xlsx.SetCellValue(sheetName, fmt.Sprintf("D%d", idx+9), item.CreatedAt.Format("02-Jan-2006"))
		xlsx.SetCellValue(sheetName, fmt.Sprintf("E%d", idx+9), item.CreatedAt.Format("15:04"))
		xlsx.SetCellValue(sheetName, fmt.Sprintf("F%d", idx+9), item.IdleValue)
		xlsx.SetCellValue(sheetName, fmt.Sprintf("G%d", idx+9), item.TreadDepthValue)
		xlsx.SetCellValue(sheetName, fmt.Sprintf("H%d", idx+9), item.StandardTreadDepthValue)
		xlsx.SetCellValue(sheetName, fmt.Sprintf("I%d", idx+9), usersMapById[item.CreatedBy].GetName())
	}
	xlsx.SetCellStyle(sheetName, "A9", "I"+fmt.Sprint(8+len(respData)), tableValueStyle)

	signedUrl, xlsxHeader, err := uc.generateTranslogicCalibrationExportSignedUrl(ctx, xlsx)
	if err != nil {
		commonlogger.Errorf("Error in generating exported excel download url", err)
		return generateExcelResponse, err
	}
	// END GENERATE EXCEL

	generateExcelResponse = commonmodel.DetailResponse{
		Success:     true,
		Message:     "translogic calibration list is successfully exported",
		ReferenceID: signedUrl,
		Data:        xlsxHeader,
	}

	return generateExcelResponse, nil
}

func (uc *IntegrationUseCase) GetIntegrationDevices(ctx context.Context, req dtos.IntegrationDeviceListReq, isAdmin bool) (*commonmodel.ListResponse, error) {
	attachedAtStart, _ := timehelpers.ParseDateFilter(req.AttachedAtStart, false)
	attachedAtEnd, _ := timehelpers.ParseDateFilter(req.AttachedAtEnd, true)

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	// IF NOT AF ADMIN CLIENT ID THEN USE USER LOGIN CLIENT ID TO FILTER LIST
	clientId := req.ClientID
	if !isAdmin {
		clientId = claim.GetLoggedInClientID()
	}

	where := models.IntegrationDeviceWhere{
		ClientID:                clientId,
		IntegrationID:           req.IntegrationID,
		IntegrationIDs:          req.IntegrationIDs,
		IntegrationTypeCode:     req.IntegrationTypeCode,
		IntegrationTypeCodes:    req.IntegrationTypeCodes,
		ReferenceCode:           req.ReferenceCode,
		ReferenceCodes:          req.ReferenceCodes,
		AttachedByUserID:        req.AttachedByUserID,
		AttachedByUserIDs:       req.AttachedByUserIDs,
		AttachedAtStart:         attachedAtStart,
		AttachedAtEnd:           attachedAtEnd,
		SerialOrReferenceNumber: req.SerialOrReferenceNumber,
		AssetID:                 req.AssetID,
		AssetIDs:                req.AssetIDs,
	}

	if len(req.ClientIDs) > 0 {
		where.ClientIDs = req.ClientIDs
	}

	count, integrationDevices, err := uc.integrationRepo.GetIntegrationDeviceList(ctx, uc.DB.DB(), models.GetIntegrationDeviceListParam{
		ListRequest: req.ListRequest,
		Cond: models.IntegrationDeviceCondition{
			Where: where,
			Preload: models.IntegrationDevicePreload{
				Integration:                          true,
				IntegrationAsset:                     true,
				IntegrationAssetTyreWithLinkedParent: true,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	integrationIDs := make([]string, 0, len(integrationDevices))
	clientIDs := []string{}
	clientIDsSet := make(map[string]struct{})
	for _, integrationDevice := range integrationDevices {
		if integrationDevice.IntegrationID != nil {
			integrationIDs = append(integrationIDs, *integrationDevice.IntegrationID)
		}

		if _, exists := clientIDsSet[integrationDevice.ClientID]; !exists && integrationDevice.ClientID != "" {
			clientIDs = append(clientIDs, integrationDevice.ClientID)
			clientIDsSet[integrationDevice.ClientID] = struct{}{}
		}
	}

	clientMapByID := map[string]userModel.Client{}
	clients, err := uc.userRepo.GetClients(ctx, uc.DB.DB(), userModel.ClientCondition{
		Where: userModel.ClientWhere{
			IDs: clientIDs,
		},
	})
	if err != nil {
		return nil, err
	}
	for _, client := range clients {
		clientMapByID[client.ID] = client
	}

	integrationDeviceIntegrationMap := map[string]geoModel.LatestIntegrationData{}
	if len(integrationIDs) > 0 {
		_, latestIntegrationData, err := uc.trackingRepo.GetLatestIntegrationDataListWithParam(
			ctx, uc.DBTimeScale.DB(),
			geoModel.GetLatestIntegrationDataListParam{
				ListRequest: req.ListRequest,
				Cond: geoModel.LatestIntegrationDataCondition{
					Where: geoModel.LatestIntegrationDataWhere{
						IntegrationIDs: integrationIDs,
					},
				},
			},
		)
		if err != nil {
			return nil, err
		}

		for _, item := range latestIntegrationData {
			integrationDeviceIntegrationMap[item.IntegrationID] = item
		}
	}

	respData := make([]dtos.IntegrationDevice, 0, len(integrationDevices))
	for _, integrationDevice := range integrationDevices {
		respItem := dtos.IntegrationDevice{}
		respItem.Set(integrationDevice, integrationDeviceIntegrationMap, clientMapByID)
		respData = append(respData, respItem)
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respData,
	}, nil

}

func (uc *IntegrationUseCase) CreateIntegrationDevice(ctx context.Context, req dtos.IntegrationDeviceCreateUpdateReq, isAdmin bool) (*commonmodel.CreateResponse, error) {
	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	existingIntegration, _ := uc.integrationRepo.GetIntegrationWithActiveIntegrationDevice(ctx, tx.DB(), req.AssetTyreID)
	if existingIntegration != nil {
		return nil, errorhandler.ErrBadRequest("asset tyre can only have single integration device")
	}

	integrationDevice := &models.IntegrationDevice{
		IntegrationTypeCode: constants.INTEGRATION_DEVICE_INTEGRATION_TYPE_CODE_TYRE_SENSOR,
		ReferenceCode:       req.ReferenceCode,
		ModelV2: commonmodel.ModelV2{
			ClientID: req.ClientID,
		},
	}

	err = uc.integrationRepo.CreateIntegrationDevice(ctx, tx.DB(), integrationDevice)
	if err != nil {
		return nil, err
	}

	// SET EMPTY CLIENT ID IF ADMIN DOES NOT SEND CLIENT ID
	if isAdmin && req.ClientID == "" {
		integrationDevice.ClientID = ""

		uc.integrationRepo.UpdateIntegrationDevice(ctx, tx.DB(), integrationDevice.ID, integrationDevice)
	}

	isAttached := req.AssetTyreID != ""

	if isAttached {
		err = uc.attachAssetTyreToSensorDevice(ctx, tx.DB(), integrationDevice.ID, req.AssetTyreID, req.ReferenceCode, req.ClientID)
		if err != nil {
			return nil, err
		}
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: integrationDevice.ID,
		Data:        nil,
	}, nil
}

func (uc *IntegrationUseCase) attachAssetTyreToSensorDevice(ctx context.Context, dB database.DBI, deviceId string, assetId string, referenceCode string, clientId string) error {

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}

	var customDataMapping pgtype.JSONB

	err = customDataMapping.Set(map[string]interface{}{}) // empty JSON object {}

	if err != nil {
		return err
	}

	identifierJSON := map[string]interface{}{
		"tyre_sensor_id": referenceCode,
	}

	var identifierJSONJSONB pgtype.JSONB
	err = identifierJSONJSONB.Set(identifierJSON)
	if err != nil {
		return err
	}

	integration := &models.Integration{
		InternalReferenceID:       assetId,
		IntegrationTargetTypeCode: constants.INTEGRATION_TARGET_TYPE_CODE_FLESPI_TRACKING,
		IdentifierJSON:            identifierJSONJSONB,
		IntegrationAccountID:      constants.INTEGRATION_ACCOUNT_ID_GENERAL,
		IntegrationTargetCode:     constants.INTEGRATION_TARGET_CODE_FLESPI,
		StatusCode:                constants.INTEGRATION_STATUS_CODE_ACTIVE,
		DataMappingCode:           constants.INTEGRATION_DATA_MAPPING_CODE_FLESPI_TYRE_SENSOR,
		CustomDataMapping:         customDataMapping,
		HideOnMonitoring:          true,
		ModelV2: commonmodel.ModelV2{
			ClientID: clientId,
		},
	}

	err = uc.integrationRepo.UpsertIntegration(ctx, dB, integration)
	if err != nil {
		return err
	}

	activeDevice, err := uc.integrationRepo.GetIntegrationDeviceDetail(ctx, dB, deviceId)
	if err != nil {
		return err
	}

	// Update integration Device
	activeDevice.IntegrationID = &integration.ID
	activeDevice.AttachedAt = null.TimeFrom(time.Now())
	activeDevice.AttachedByUserID = &claim.UserID
	uc.integrationRepo.UpdateIntegrationDevice(ctx, dB, activeDevice.ID, &activeDevice)

	// CREATE HISTORY
	var integrationID string
	if activeDevice.IntegrationID != nil {
		integrationID = *activeDevice.IntegrationID
	}
	history := models.IntegrationDeviceHistory{
		IntegrationID:       integrationID,
		IntegrationDeviceID: activeDevice.ID,
		AttachedAt:          activeDevice.AttachedAt.Time,
		AttachedByUserID:    *activeDevice.AttachedByUserID,
		ModelV2: commonmodel.ModelV2{
			ClientID: clientId,
		},
	}
	err = uc.integrationRepo.CreateIntegrationDeviceHistory(ctx, dB, &history)
	if err != nil {
		return err
	}

	return nil
}

func (uc *IntegrationUseCase) detachAssetTyreToSensorDevice(ctx context.Context, dB database.DBI, deviceId string, integrationId string, clientId string) error {

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}

	activeIntegration, err := uc.integrationRepo.GetIntegration(ctx, dB, models.IntegrationCondition{
		Where: models.IntegrationWhere{
			ID: integrationId,
		},
	})

	if err != nil {
		println(err)
	}

	var customDataMapping pgtype.JSONB

	err = customDataMapping.Set(map[string]interface{}{}) // empty JSON object {}

	if err != nil {
		return err
	}

	identifierJSON := map[string]interface{}{}

	var identifierJSONJSONB pgtype.JSONB
	err = identifierJSONJSONB.Set(identifierJSON)
	if err != nil {
		return err
	}
	activeIntegration.StatusCode = constants.INTEGRATION_STATUS_CODE_INACTIVE
	activeIntegration.HideOnMonitoring = true
	activeIntegration.IdentifierJSON = identifierJSONJSONB

	err = uc.integrationRepo.UpdateIntegration(ctx, dB, activeIntegration.ID, activeIntegration)
	if err != nil {
		return err
	}

	activeDevice, err := uc.integrationRepo.GetIntegrationDeviceDetail(ctx, dB, deviceId)
	if err != nil {
		return err
	}

	// Update integration Device
	activeDevice.IntegrationID = nil
	activeDevice.Integration = models.Integration{}
	activeDevice.AttachedAt = null.Time{}
	activeDevice.AttachedByUserID = nil

	err = uc.integrationRepo.UpdateIntegrationDevice(ctx, dB, activeDevice.ID, &activeDevice)
	if err != nil {
		return err
	}

	// UPDATE HISTORY
	history, err := uc.integrationRepo.GetLatestIntegrationDeviceHistory(ctx, dB, models.IntegrationDeviceHistoryCondition{
		Where: models.IntegrationDeviceHistoryWhere{
			IntegrationDeviceID: activeDevice.ID,
		},
	})

	if err != nil {
		return err
	}

	history.DetachedAt = null.TimeFrom(time.Now())
	history.DetachedByUserID = claim.UserID

	err = uc.integrationRepo.UpdateIntegrationDeviceHistory(ctx, dB, history.ID, history)
	if err != nil {
		return err
	}

	return nil
}

func (uc *IntegrationUseCase) UpdateIntegrationDevice(ctx context.Context, id string, req dtos.IntegrationDeviceCreateUpdateReq, isAdmin bool) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	existingIntegration, _ := uc.integrationRepo.GetIntegrationWithActiveIntegrationDevice(ctx, tx.DB(), req.AssetTyreID)
	if existingIntegration != nil && existingIntegration.ID != id {
		return nil, errorhandler.ErrBadRequest("asset tyre can only have single integration device")
	}

	device, err := uc.integrationRepo.GetIntegrationDeviceDetail(ctx, tx.DB(), id)
	if err != nil {
		return nil, err
	}

	if !isAdmin && device.ClientID != claim.GetLoggedInClientID() {
		return nil, errorhandler.ErrBadRequest("can't find integration sensor device on this client")
	}

	if !isAdmin {
		req.ClientID = claim.GetLoggedInClientID()
	}

	defer tx.Rollback()

	currentAssetID := device.Integration.InternalReferenceID
	newAssetID := req.AssetTyreID
	// If both are empty (null → null), do nothing
	if currentAssetID == "" && newAssetID == "" {
		// skip detach/attach
	} else {
		// Detach if current is not empty and different from new
		if currentAssetID != "" && currentAssetID != newAssetID {
			if device.IntegrationID != nil {
				err := uc.detachAssetTyreToSensorDevice(ctx, tx.DB(), device.ID, device.Integration.ID, req.ClientID)
				if err != nil {
					return nil, err
				}
			}
		}

		// Attach if new is not empty and different from current
		if newAssetID != "" && currentAssetID != newAssetID {
			err := uc.attachAssetTyreToSensorDevice(ctx, tx.DB(), device.ID, newAssetID, req.ReferenceCode, req.ClientID)
			if err != nil {
				// handle or log error, or return if critical
				return nil, err
			}
		} else if device.IntegrationID != nil && device.ReferenceCode != req.ReferenceCode {
			// Only update integration Identifier JSON
			var customDataMapping pgtype.JSONB

			err = customDataMapping.Set(map[string]interface{}{}) // empty JSON object {}

			if err != nil {
				return nil, err
			}

			identifierJSON := map[string]interface{}{
				"tyre_sensor_id": req.ReferenceCode,
			}

			var identifierJSONJSONB pgtype.JSONB
			err = identifierJSONJSONB.Set(identifierJSON)
			if err != nil {
				return nil, err
			}

			integration := &models.Integration{
				IdentifierJSON: identifierJSONJSONB,
			}

			if device.IntegrationID != nil {
				err = uc.integrationRepo.UpdateIntegration(ctx, tx.DB(), *device.IntegrationID, integration)
				if err != nil {
					return nil, err
				}
			}
		}
	}

	device, err = uc.integrationRepo.GetIntegrationDeviceDetail(ctx, tx.DB(), id)
	if err != nil {
		return nil, err
	}

	device.ClientID = req.ClientID
	device.ReferenceCode = req.ReferenceCode

	err = uc.integrationRepo.UpdateIntegrationDevice(ctx, tx.DB(), id, &device)
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}, nil
}

func (uc *IntegrationUseCase) GetIntegrationDeviceHistories(ctx context.Context, req dtos.IntegrationDeviceHistoryListReq, isAdmin bool) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	attachedAtStart, _ := timehelpers.ParseDateFilter(req.AttachedAtStart, false)
	attachedAtEnd, _ := timehelpers.ParseDateFilter(req.AttachedAtEnd, true)
	detachedAtStart, _ := timehelpers.ParseDateFilter(req.DetachedAtStart, false)
	detachedAtEnd, _ := timehelpers.ParseDateFilter(req.DetachedAtEnd, true)

	clientId := req.ClientID
	if !isAdmin {
		clientId = claim.GetLoggedInClientID()
	}

	where := models.IntegrationDeviceHistoryWhere{
		ClientID:             clientId,
		IntegrationID:        req.IntegrationID,
		IntegrationIDs:       req.IntegrationIDs,
		IntegrationDeviceID:  req.IntegrationDeviceID,
		IntegrationDeviceIDs: req.IntegrationDeviceIDs,
		AttachedByUserID:     req.AttachedByUserID,
		AttachedByUserIDs:    req.AttachedByUserIDs,
		DetachedByUserID:     req.DetachedByUserID,
		DetachedByUserIDs:    req.DetachedByUserIDs,
		AttachedAtStart:      attachedAtStart,
		AttachedAtEnd:        attachedAtEnd,
		DetachedAtStart:      detachedAtStart,
		DetachedAtEnd:        detachedAtEnd,
	}

	if len(req.ClientIDs) > 0 {
		where.ClientIDs = req.ClientIDs
	}

	count, histories, err := uc.integrationRepo.GetIntegrationDeviceHistoryList(ctx, uc.DB.DB(), models.GetIntegrationDeviceHistoryListParam{
		ListRequest: req.ListRequest,
		Cond: models.IntegrationDeviceHistoryCondition{
			Where: where,
			Preload: models.IntegrationDeviceHistoryPreload{
				IntegrationDevice:                    true,
				Integration:                          true,
				IntegrationAsset:                     true,
				IntegrationAssetTyreWithLinkedParent: true,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	userIDsSet := make(map[string]struct{})
	userIDs := []string{}
	clientIDs := []string{}
	clientIDsSet := make(map[string]struct{})
	for _, history := range histories {
		if _, exists := userIDsSet[history.AttachedByUserID]; !exists && history.AttachedByUserID != "" {
			userIDs = append(userIDs, history.AttachedByUserID)
			userIDsSet[history.AttachedByUserID] = struct{}{}
		}

		if history.DetachedByUserID != "" {
			if _, exists := userIDsSet[history.DetachedByUserID]; !exists {
				userIDs = append(userIDs, history.DetachedByUserID)
				userIDsSet[history.DetachedByUserID] = struct{}{}
			}
		}

		if _, exists := clientIDsSet[history.ClientID]; !exists && history.ClientID != "" {
			clientIDs = append(clientIDs, history.ClientID)
			clientIDsSet[history.ClientID] = struct{}{}
		}
	}

	users, err := uc.userRepo.GetUsersV2(ctx, uc.DB.DB(), userModel.UserCondition{
		Where: userModel.UserWhere{
			IDs: userIDs,
		},
	})
	if err != nil {
		return nil, err
	}

	clientMapByID := map[string]userModel.Client{}
	clients, err := uc.userRepo.GetClients(ctx, uc.DB.DB(), userModel.ClientCondition{
		Where: userModel.ClientWhere{
			IDs: clientIDs,
		},
	})
	if err != nil {
		return nil, err
	}
	for _, client := range clients {
		clientMapByID[client.ID] = client
	}

	userMapByID := make(map[string]userModel.User)
	for _, user := range users {
		userMapByID[user.ID] = user
	}

	respData := make([]dtos.IntegrationDeviceHistory, 0, len(histories))
	for _, history := range histories {
		respItem := dtos.IntegrationDeviceHistory{}
		respItem.Set(history, userMapByID, clientMapByID)
		respData = append(respData, respItem)
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respData,
	}, nil

}
