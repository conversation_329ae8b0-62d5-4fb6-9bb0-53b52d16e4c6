package presistence

import (
	"assetfindr/internal/app/integration/errorconstants"
	"assetfindr/internal/app/integration/models"
	"assetfindr/internal/app/integration/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/commonmodel"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
)

type accurateidRepository struct {
	httpClient *http.Client
}

func NewAccurateRepository(httpClient *http.Client) repository.AccurateRepository {
	return &accurateidRepository{
		httpClient: httpClient,
	}
}

var accurateCode string = "ACCURATE"

func (r *accurateidRepository) GetToken(ctx context.Context, code string) (string, error) {
	clientID := os.Getenv("ASSETFINDR_ACCURATE_CLIENT_ID")
	clientSecret := os.Getenv("ASSETFINDR_ACCURATE_CLIENT_SECRET")
	accurateAuthorization := os.Getenv("ASSETFINDR_ACCURATE_CLIENT_AUTHORIZATION")
	handleCallbackURL := "http://localhost:8000/aol-oauth-callback"
	form := url.Values{}
	form.Add("code", code)
	form.Add("grant_type", "authorization_code")
	form.Add("client_id", clientID)
	form.Add("client_secret", clientSecret)
	form.Add("redirect_uri", handleCallbackURL)
	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		"https://account.accurate.id/oauth/token",
		strings.NewReader(form.Encode()),
	)
	if err != nil {
		return "", err
	}

	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Add("Authorization", "Basic "+accurateAuthorization)
	response, err := r.httpClient.Do(req)
	if err != nil {
		log.Println("get token")
		commonlogger.Warnf("failed when make a request to %s, err: %v", req.URL.RequestURI(), err)
		return "", errorhandler.ErrExternal{
			ExternalCode:    accurateCode,
			InternalMessage: "cant reach Accuarate",
		}
	}

	defer response.Body.Close()

	if response.StatusCode == http.StatusBadRequest {
		return "", errorhandler.ErrBadRequest(errorconstants.INTEGRATION_BAD_REQUEST)
	}

	err = errorhandler.ParseCredentialErrorFromHttpResponseCode(req.URL.RequestURI(), response.StatusCode)
	if err != nil {
		log.Println("parse")
		commonlogger.Warnf("failed when make a request to %s, err: %v", req.URL.RequestURI(), err)
		return "", err
	}

	var data struct {
		AccessToken  string `json:"access_token"`
		RefreshToken string `json:"refresh_token"`
	}
	err = json.NewDecoder(response.Body).Decode(&data)
	if err != nil {
		return "", err
	}

	return data.AccessToken, nil
}

func Handle500sErrorAccurate(statusCode int, apiResp string) error {
	return errorhandler.ErrExternal{
		ExternalStatusCode: http.StatusInternalServerError,
		ExternalCode:       accurateCode,
		InternalMessage:    fmt.Sprintf("got status %d from Accurate, please contact Accurate support", statusCode),
		ExternalResp:       apiResp,
	}
}

func Handle200NotSuccessAccurate(externalMsgs []string) error {
	return errorhandler.ErrExternal{
		ExternalStatusCode: http.StatusInternalServerError,
		ExternalCode:       accurateCode,
		InternalMessage:    "got not success from Accurate",
		ExternalResp:       externalMsgs,
	}
}

func (r *accurateidRepository) OpenDB(ctx context.Context, token string, dbID int) (*models.AccurateOpenDBResp, error) {
	form := url.Values{}
	finalURL := "https://account.accurate.id/api/open-db.do"
	form.Add("id", fmt.Sprintf("%d", dbID))
	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		finalURL,
		strings.NewReader(form.Encode()),
	)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Add("Authorization", "Bearer "+token)
	response, err := r.httpClient.Do(req)
	if err != nil {
		log.Println("get token")
		commonlogger.Warnf("failed to request %s, err: %v", finalURL, err)
		return nil, errorhandler.ErrExternal{
			ExternalCode:    accurateCode,
			InternalMessage: "cant reach Accuarate",
		}
	}

	defer response.Body.Close()

	if response.StatusCode == http.StatusUnauthorized {
		return nil, errorhandler.ErrBadRequest(errorconstants.ACCURATE_UNAUTHORIZED)
	}

	if response.StatusCode >= http.StatusInternalServerError {
		bodyBytes, err := io.ReadAll(response.Body)
		if err != nil {
			return nil, err
		}

		return nil, Handle500sErrorAccurate(response.StatusCode, string(bodyBytes))
	}

	var data models.AccurateOpenDBResp
	err = json.NewDecoder(response.Body).Decode(&data)
	if err != nil {
		return nil, err
	}

	return &data, nil
}

func (a *accurateidRepository) GetProductList(ctx context.Context, param models.GetAccurateItemListParam) (int, []models.AccurateItem, error) {
	baseURL := fmt.Sprintf("%s/accurate/api/item/list.do", param.AccurateHeaders.Host)

	// Create query parameters
	query := url.Values{}
	query.Add("fields", "id,name,notes,unitPrice,unit1,suspended,availableToSell,itemType,itemCategory,no")

	if param.PageNo > 0 {
		query.Add("sp.page", fmt.Sprintf("%d", param.PageNo))
	}
	if param.PageSize > 0 {
		query.Add("sp.pageSize", fmt.Sprintf("%d", param.PageSize))
	}

	if param.ProductTypeCode != "" {
		if param.ProductTypeCode == "PRODUCT" {
			param.ProductTypeCode = "INVENTORY"
		}

		query.Add("filter.itemType.op", "EQUAL")
		query.Add("filter.itemType.val", param.ProductTypeCode)
	}

	if param.SearchKeyword != "" {
		query.Add("filter.keywords.op", "CONTAIN")
		query.Add("filter.keywords.val", param.SearchKeyword)
	}

	if param.IsActive != nil {
		query.Add("filter.suspended", strconv.FormatBool(!*param.IsActive))
	}

	// Construct the final URL with query parameters
	finalURL := fmt.Sprintf("%s?%s", baseURL, query.Encode())

	// Create a new HTTP request
	req, err := http.NewRequest("GET", finalURL, nil)
	if err != nil {
		return 0, nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set the required headers
	req.Header.Set("X-Session-ID", param.AccurateHeaders.Session)
	req.Header.Add("Authorization", "Bearer "+param.AccurateHeaders.Token)

	// Perform the HTTP request
	resp, err := a.httpClient.Do(req)
	if err != nil {
		commonlogger.Warnf("failed to request %s, err: %v", finalURL, err)
		return 0, nil, errorhandler.ErrExternal{
			ExternalCode:    accurateCode,
			InternalMessage: "cant reach Accuarate",
		}
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusUnauthorized {
		return 0, nil, errorhandler.ErrBadRequest(errorconstants.ACCURATE_UNAUTHORIZED)
	}

	if resp.StatusCode >= http.StatusInternalServerError {
		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			return 0, nil, err
		}

		return 0, nil, Handle500sErrorAccurate(resp.StatusCode, string(bodyBytes))
	}

	// Parse the response body
	var apiResponse struct {
		S  bool                  `json:"s"`
		D  []models.AccurateItem `json:"d"`
		Sp struct {
			Page      int         `json:"page"`
			Sort      interface{} `json:"sort"`
			PageSize  int         `json:"pageSize"`
			PageCount int         `json:"pageCount"`
			RowCount  int         `json:"rowCount"`
			Start     int         `json:"start"`
			Limit     interface{} `json:"limit"`
		} `json:"sp"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&apiResponse); err != nil {
		return 0, nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return apiResponse.Sp.RowCount, apiResponse.D, nil
}

func (a *accurateidRepository) GetUnitList(ctx context.Context, param models.GetAccurateUnitListParam) (int, []models.AccurateUnit1, error) {
	baseURL := fmt.Sprintf("%s/accurate/api/unit/list.do", param.AccurateHeaders.Host)

	// Create query parameters
	query := url.Values{}
	query.Add("fields", "id,name,notes,unitPrice,unit1,suspended,availableToSell,itemType,itemCategory")

	if param.PageNo > 0 {
		query.Add("sp.page", fmt.Sprintf("%d", param.PageNo))
	}
	if param.PageSize > 0 {
		query.Add("sp.pageSize", fmt.Sprintf("%d", param.PageSize))
	}

	// Construct the final URL with query parameters
	finalURL := fmt.Sprintf("%s?%s", baseURL, query.Encode())

	// Create a new HTTP request
	req, err := http.NewRequest("GET", finalURL, nil)
	if err != nil {
		return 0, nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set the required headers
	req.Header.Set("X-Session-ID", param.AccurateHeaders.Session)
	req.Header.Add("Authorization", "Bearer "+param.AccurateHeaders.Token)

	// Perform the HTTP request
	resp, err := a.httpClient.Do(req)
	if err != nil {
		commonlogger.Warnf("failed to request %s, err: %v", finalURL, err)
		return 0, nil, errorhandler.ErrExternal{
			ExternalCode:    accurateCode,
			InternalMessage: "cant reach Accuarate",
		}
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusUnauthorized {
		return 0, nil, errorhandler.ErrBadRequest(errorconstants.ACCURATE_UNAUTHORIZED)
	}

	if resp.StatusCode >= http.StatusInternalServerError {
		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			return 0, nil, err
		}

		return 0, nil, Handle500sErrorAccurate(resp.StatusCode, string(bodyBytes))
	}

	// Parse the response body
	var apiResponse struct {
		S  bool                   `json:"s"`
		D  []models.AccurateUnit1 `json:"d"`
		Sp struct {
			Page      int         `json:"page"`
			Sort      interface{} `json:"sort"`
			PageSize  int         `json:"pageSize"`
			PageCount int         `json:"pageCount"`
			RowCount  int         `json:"rowCount"`
			Start     int         `json:"start"`
			Limit     interface{} `json:"limit"`
		} `json:"sp"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&apiResponse); err != nil {
		return 0, nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return apiResponse.Sp.RowCount, apiResponse.D, nil
}

func (a *accurateidRepository) GetCustomerList(ctx context.Context, param models.GetAccurateCustomerListParam) (int, []models.AccurateCustomerListModel, error) {
	baseURL := fmt.Sprintf("%s/accurate/api/customer/list.do", param.AccurateHeaders.Host)

	// Create query parameters
	query := url.Values{}
	query.Add("fields", "id,name,mobilePhone,workPhone,customerNo,npwpNo,email,suspended")

	if param.PageNo > 0 {
		query.Add("sp.page", fmt.Sprintf("%d", param.PageNo))
	}
	if param.PageSize > 0 {
		query.Add("sp.pageSize", fmt.Sprintf("%d", param.PageSize))
	}

	if param.SearchKeyword != "" {

		query.Add("filter.keywords.op", "CONTAIN")
		query.Add("filter.keywords.val", param.SearchKeyword)
	}

	if param.IsActive != nil {
		query.Add("filter.suspended", strconv.FormatBool(!*param.IsActive))
	}

	query.Add("sp.sort", "lastUpdate|desc")

	// Construct the final URL with query parameters
	finalURL := fmt.Sprintf("%s?%s", baseURL, query.Encode())

	// Create a new HTTP request
	req, err := http.NewRequest("GET", finalURL, nil)
	if err != nil {
		return 0, nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set the required headers
	req.Header.Set("X-Session-ID", param.AccurateHeaders.Session)
	req.Header.Add("Authorization", "Bearer "+param.AccurateHeaders.Token)

	// Perform the HTTP request
	resp, err := a.httpClient.Do(req)
	if err != nil {
		commonlogger.Warnf("failed to request %s, err: %v", finalURL, err)
		return 0, nil, errorhandler.ErrExternal{
			ExternalCode:    accurateCode,
			InternalMessage: "cant reach Accuarate",
		}
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusUnauthorized {
		return 0, nil, errorhandler.ErrBadRequest(errorconstants.ACCURATE_UNAUTHORIZED)
	}

	if resp.StatusCode >= http.StatusInternalServerError {
		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			return 0, nil, err
		}

		return 0, nil, Handle500sErrorAccurate(resp.StatusCode, string(bodyBytes))
	}

	// Parse the response body
	var apiResponse struct {
		S  bool                               `json:"s"`
		D  []models.AccurateCustomerListModel `json:"d"`
		Sp struct {
			Page      int         `json:"page"`
			Sort      interface{} `json:"sort"`
			PageSize  int         `json:"pageSize"`
			PageCount int         `json:"pageCount"`
			RowCount  int         `json:"rowCount"`
			Start     int         `json:"start"`
			Limit     interface{} `json:"limit"`
		} `json:"sp"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&apiResponse); err != nil {
		return 0, nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return apiResponse.Sp.RowCount, apiResponse.D, nil
}

func (a *accurateidRepository) GetCustomerPaymentTermList(ctx context.Context, param models.GetAccurateCustomerPaymentTermListParam) (int, []models.AccurateCustomerPaymentTermListModel, error) {
	baseURL := fmt.Sprintf("%s/accurate/api/payment-term/list.do", param.AccurateHeaders.Host)

	// Create query parameters
	query := url.Values{}
	query.Add("fields", "id,name")

	if param.PageNo > 0 {
		query.Add("sp.page", fmt.Sprintf("%d", param.PageNo))
	}
	if param.PageSize > 0 {
		query.Add("sp.pageSize", fmt.Sprintf("%d", param.PageSize))
	}

	if param.IsActive != nil {
		query.Add("filter.suspended", strconv.FormatBool(!*param.IsActive))
	}

	// Construct the final URL with query parameters
	finalURL := fmt.Sprintf("%s?%s", baseURL, query.Encode())

	// Create a new HTTP request
	req, err := http.NewRequest("GET", finalURL, nil)
	if err != nil {
		return 0, nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set the required headers
	req.Header.Set("X-Session-ID", param.AccurateHeaders.Session)
	req.Header.Add("Authorization", "Bearer "+param.AccurateHeaders.Token)

	// Perform the HTTP request
	resp, err := a.httpClient.Do(req)
	if err != nil {
		commonlogger.Warnf("failed to request %s, err: %v", finalURL, err)
		return 0, nil, errorhandler.ErrExternal{
			ExternalCode:    accurateCode,
			InternalMessage: "cant reach Accuarate",
		}
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusUnauthorized {
		return 0, nil, errorhandler.ErrBadRequest(errorconstants.ACCURATE_UNAUTHORIZED)
	}

	if resp.StatusCode >= http.StatusInternalServerError {
		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			return 0, nil, err
		}

		return 0, nil, Handle500sErrorAccurate(resp.StatusCode, string(bodyBytes))
	}

	// Parse the response body
	var apiResponse struct {
		S  bool                                          `json:"s"`
		D  []models.AccurateCustomerPaymentTermListModel `json:"d"`
		Sp struct {
			Page      int         `json:"page"`
			Sort      interface{} `json:"sort"`
			PageSize  int         `json:"pageSize"`
			PageCount int         `json:"pageCount"`
			RowCount  int         `json:"rowCount"`
			Start     int         `json:"start"`
			Limit     interface{} `json:"limit"`
		} `json:"sp"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&apiResponse); err != nil {
		return 0, nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return apiResponse.Sp.RowCount, apiResponse.D, nil
}

func (a *accurateidRepository) GetCustomer(ctx context.Context, id string, accurateHeaders commonmodel.AccurateHeaders) (*models.AccurateCustomer, error) {
	baseURL := fmt.Sprintf("%s/accurate/api/customer/detail.do", accurateHeaders.Host)

	// Create query parameters
	query := url.Values{}
	query.Add("id", id)

	// Construct the final URL with query parameters
	finalURL := fmt.Sprintf("%s?%s", baseURL, query.Encode())

	// Create a new HTTP request
	req, err := http.NewRequest("GET", finalURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set the required headers
	req.Header.Set("X-Session-ID", accurateHeaders.Session)
	req.Header.Add("Authorization", "Bearer "+accurateHeaders.Token)

	// Perform the HTTP request
	resp, err := a.httpClient.Do(req)
	if err != nil {
		commonlogger.Warnf("failed to request %s, err: %v", finalURL, err)
		return nil, errorhandler.ErrExternal{
			ExternalCode:    accurateCode,
			InternalMessage: "cant reach Accuarate",
		}
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusUnauthorized {
		return nil, errorhandler.ErrBadRequest(errorconstants.ACCURATE_UNAUTHORIZED)
	}

	if resp.StatusCode >= http.StatusInternalServerError {
		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, err
		}

		return nil, Handle500sErrorAccurate(resp.StatusCode, string(bodyBytes))
	}

	// Parse the response body
	var apiResponse struct {
		S bool                    `json:"s"`
		D models.AccurateCustomer `json:"d"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&apiResponse); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &apiResponse.D, nil
}

func (a *accurateidRepository) CreateSalesInvoice(ctx context.Context, request models.AccurateSalesInvoiceRequest, accurateHeaders commonmodel.AccurateHeaders) (*models.AccurateCreateSalesInvoiceResp, error) {
	baseURL := fmt.Sprintf("%s/accurate/api/sales-invoice/save.do", accurateHeaders.Host)

	// Convert request to JSON
	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	// Create a new HTTP request
	req, err := http.NewRequest(http.MethodPost, baseURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", "application/json")

	// Set the required headers
	req.Header.Set("X-Session-ID", accurateHeaders.Session)
	req.Header.Add("Authorization", "Bearer "+accurateHeaders.Token)

	// Send the request
	resp, err := a.httpClient.Do(req)
	if err != nil {
		commonlogger.Warnf("failed to request %s, err: %v", baseURL, err)
		return nil, errorhandler.ErrExternal{
			ExternalCode:    accurateCode,
			InternalMessage: "cant reach Accuarate",
		}
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusUnauthorized {
		return nil, errorhandler.ErrBadRequest(errorconstants.ACCURATE_UNAUTHORIZED)
	}

	// Check the response
	if resp.StatusCode >= http.StatusInternalServerError {
		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, err
		}

		return nil, Handle500sErrorAccurate(resp.StatusCode, string(bodyBytes))
	}

	// Parse the response body
	var apiResponse struct {
		R models.AccurateCreateSalesInvoiceResp `json:"r"`
		S bool                                  `json:"s"`
		D []string                              `json:"d"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&apiResponse); err != nil {
		return nil, err
	}

	if !apiResponse.S {
		return nil, Handle200NotSuccessAccurate(apiResponse.D)
	}

	return &apiResponse.R, nil
}

func (a *accurateidRepository) SaveCustomer(ctx context.Context, request models.AccurateCustomer, accurateHeaders commonmodel.AccurateHeaders) (*models.AccurateCustomer, error) {
	baseURL := fmt.Sprintf("%s/accurate/api/customer/save.do", accurateHeaders.Host)

	// Convert request to JSON
	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	// Create a new HTTP request
	req, err := http.NewRequest(http.MethodPost, baseURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", "application/json")

	// Set the required headers
	req.Header.Set("X-Session-ID", accurateHeaders.Session)
	req.Header.Add("Authorization", "Bearer "+accurateHeaders.Token)

	// Send the request
	resp, err := a.httpClient.Do(req)
	if err != nil {
		commonlogger.Warnf("failed to request %s, err: %v", baseURL, err)
		return nil, errorhandler.ErrExternal{
			ExternalCode:    accurateCode,
			InternalMessage: "cant reach Accuarate",
		}
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusUnauthorized {
		return nil, errorhandler.ErrBadRequest(errorconstants.ACCURATE_UNAUTHORIZED)
	}

	// Check the response
	if resp.StatusCode >= http.StatusInternalServerError {
		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, err
		}

		return nil, Handle500sErrorAccurate(resp.StatusCode, string(bodyBytes))
	}

	// Parse the response body
	var apiResponse struct {
		R models.AccurateCustomer `json:"r"`
		S bool                    `json:"s"`
		D []string                `json:"d"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&apiResponse); err != nil {
		return nil, err
	}

	if !apiResponse.S {
		return nil, Handle200NotSuccessAccurate(apiResponse.D)
	}

	return &apiResponse.R, nil
}

func (a *accurateidRepository) GetSalesInvoiceList(ctx context.Context, param models.GetAccurateSalesInvoiceListParam) (int, []models.AccurateSalesInvoice, error) {
	baseURL := fmt.Sprintf("%s/accurate/api/sales-invoice/list.do", param.AccurateHeaders.Host)

	// Create query parameters
	query := url.Values{}
	query.Add("fields", "id,number,transDate,customer,totalAmount,statusName")

	if param.PageNo > 0 {
		query.Add("sp.page", fmt.Sprintf("%d", param.PageNo))
	}
	if param.PageSize > 0 {
		query.Add("sp.pageSize", fmt.Sprintf("%d", param.PageSize))
	}

	if param.SearchKeyword != "" {

		query.Add("filter.keywords.op", "CONTAIN")
		query.Add("filter.keywords.val", param.SearchKeyword)
	}

	if param.IsActive != nil {
		query.Add("filter.suspended", strconv.FormatBool(!*param.IsActive))
	}

	if param.Outstanding.Valid {
		query.Add("filter.outstanding", strconv.FormatBool(param.Outstanding.Bool))
	}

	if param.TransDateStart != "" && param.TransDateEnd != "" {
		query.Add("filter.transDate.op", "BETWEEN")
		query.Add("filter.transDate.val[0]", param.TransDateStart)
		query.Add("filter.transDate.val[1]", param.TransDateEnd)
	}

	// Construct the final URL with query parameters
	finalURL := fmt.Sprintf("%s?%s", baseURL, query.Encode())

	// Create a new HTTP request
	req, err := http.NewRequest("GET", finalURL, nil)
	if err != nil {
		return 0, nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set the required headers
	req.Header.Set("X-Session-ID", param.AccurateHeaders.Session)
	req.Header.Add("Authorization", "Bearer "+param.AccurateHeaders.Token)

	// Perform the HTTP request
	resp, err := a.httpClient.Do(req)
	if err != nil {
		commonlogger.Warnf("failed to request %s, err: %v", finalURL, err)
		return 0, nil, errorhandler.ErrExternal{
			ExternalCode:    accurateCode,
			InternalMessage: "cant reach Accuarate",
		}
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusUnauthorized {
		return 0, nil, errorhandler.ErrBadRequest(errorconstants.ACCURATE_UNAUTHORIZED)
	}

	if resp.StatusCode >= http.StatusInternalServerError {
		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			return 0, nil, err
		}

		return 0, nil, Handle500sErrorAccurate(resp.StatusCode, string(bodyBytes))
	}

	// Parse the response body
	var apiResponse struct {
		S  bool                          `json:"s"`
		D  []models.AccurateSalesInvoice `json:"d"`
		Sp struct {
			Page      int         `json:"page"`
			Sort      interface{} `json:"sort"`
			PageSize  int         `json:"pageSize"`
			PageCount int         `json:"pageCount"`
			RowCount  int         `json:"rowCount"`
			Start     int         `json:"start"`
			Limit     interface{} `json:"limit"`
		} `json:"sp"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&apiResponse); err != nil {
		return 0, nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return apiResponse.Sp.RowCount, apiResponse.D, nil
}
