package usecase

import (
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/errorhandler"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"

	"context"
)

func (uc *HaulUseCase) GetHaulDispatchLocation(ctx context.Context, assetID string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	haulDispatchLocation, err := uc.haulRepo.GetHaulDispatchLocation(ctx, uc.DB.DB(), models.HaulDispatchLocationCondition{
		Where: models.HaulDispatchLocationWhere{
			AssetID:  assetID,
			ClientID: claim.GetLoggedInClientID(),
		},
		Preload: models.HaulDispatchLocationPreload{
			LoadLocation:         true,
			DumpLocation:         true,
			DispatchLocationType: true,
		},
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return nil, err
	}

	if haulDispatchLocation == nil {
		return &commonmodel.DetailResponse{
			Success:     true,
			Message:     "Success",
			ReferenceID: assetID,
			Data:        nil,
		}, nil
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: assetID,
		Data:        dtos.BuildHaulDispatchLocation(haulDispatchLocation),
	}, nil
}

func (uc *HaulUseCase) UpsertHaulDispatchLocation(ctx context.Context, assetID string, req dtos.UpsertHaulDispatchLocationReq) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	_, err = uc.assetRepo.GetAsset(ctx, uc.DB.DB(), models.AssetCondition{
		Where: models.AssetWhere{
			ID:       assetID,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	haulDispatchLocation := &models.HaulDispatchLocation{
		AssetID:                  assetID,
		LoadLocationID:           req.LoadLocationID,
		DumpLocationID:           req.DumpLocationID,
		DispatchLocationTypeCode: req.DispatchLocationTypeCode,
	}

	err = uc.haulRepo.UpsertHaulDispatchLocation(ctx, uc.DB.WithCtx(ctx).DB(), haulDispatchLocation)
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: assetID,
		Data:        nil,
	}, nil
}
