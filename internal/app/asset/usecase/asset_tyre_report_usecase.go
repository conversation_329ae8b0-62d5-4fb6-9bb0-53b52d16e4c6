package usecase

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/models"
	financeDtos "assetfindr/internal/app/finance/dtos"
	storageConstants "assetfindr/internal/app/storage/constants"
	userIdentityModel "assetfindr/internal/app/user-identity/models"
	intenalConstants "assetfindr/internal/constants"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers"
	"assetfindr/pkg/common/helpers/authhelpers"
	"bytes"
	"context"
	"io"
	"mime/multipart"
	"net/http"
	"path"
	"time"

	"github.com/xuri/excelize/v2"
)

func (uc *AssetTyreUseCase) GetAssetTyreStockReport(ctx context.Context, req dtos.GetAssetTyreStockReportReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	totalRecords, assetTyres, err := uc.AssetTyreRepository.GetAssetTyreListV2(
		ctx, uc.DB.DB(),
		models.GetAssetTyreListParam{
			ListRequest: req.ListRequest,
			Cond: models.AssetTyreCondition{
				Where: models.AssetTyreWhere{
					ClientID:                   claim.GetLoggedInClientID(),
					BrandIDs:                   req.BrandIDs,
					TyreSizes:                  req.TyreSizes,
					PatternTypes:               req.PatternTypes,
					UtilizationRateStatusCodes: req.UtilizationRateStatusCodes,
					StatusCodes:                []string{constants.ASSET_STATUS_CODE_IN_STOCK, constants.ASSET_STATUS_CODE_NEW_STOCK},
				},
				Preload: models.AssetTyrePreload{
					AssetBrand:            true,
					AssetStatus:           true,
					Tyre:                  true,
					RetreadTyre:           true,
					UtilizationRateStatus: true,
				},
			},
		},
	)
	if err != nil {
		return nil, err
	}

	journalRefs := make([]string, len(assetTyres)*2)
	for _, assetTyre := range assetTyres {
		journalRefs = append(journalRefs, assetTyre.AssetID)
		journalRefs = append(journalRefs, assetTyre.LatestTread().ID)
	}

	refAmounts, err := uc.financeUseCase.GetTotalCostsByReferenceIDs(ctx, journalRefs)
	if err != nil {
		return nil, err
	}

	mapRefAmounts := map[string]financeDtos.ReferenceTotalAmount{}
	for _, refAmount := range refAmounts {
		mapRefAmounts[refAmount.ReferenceID] = refAmount
	}

	respItems := make([]dtos.GetAssetTyreStockReportResp, 0, len(assetTyres))
	for _, assetTyre := range assetTyres {
		respItems = append(respItems, dtos.BuildGetAssetTyreStockReportResp(assetTyre, mapRefAmounts))
	}

	return &commonmodel.ListResponse{
		TotalRecords: totalRecords,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respItems,
	}, nil
}

func (uc *AssetTyreUseCase) GetUtilizationRatePercentageStatus(ctx context.Context) (*commonmodel.ListResponse, error) {
	utilizationRatePercentageStatus, err := uc.AssetTyreRepository.GetUtilizationRatePercentageStatus(ctx, uc.DB.DB())
	if err != nil {
		return nil, err
	}

	return &commonmodel.ListResponse{
		TotalRecords: len(utilizationRatePercentageStatus),
		PageSize:     len(utilizationRatePercentageStatus),
		PageNo:       1,
		Data:         utilizationRatePercentageStatus,
	}, nil
}

func (uc *AssetTyreUseCase) GetAssetTyreStockReportExport(ctx context.Context) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	client, err := uc.userIdentityRepo.GetClient(ctx, uc.DB.DB(), userIdentityModel.ClientCondition{
		Where: userIdentityModel.ClientWhere{
			ID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	assetTyres, err := uc.AssetTyreRepository.GetAssetTyres(
		ctx, uc.DB.DB(),
		models.AssetTyreCondition{
			Where: models.AssetTyreWhere{
				ClientID: claim.GetLoggedInClientID(),
				// BrandIDs:                   req.BrandIDs,
				// TyreSizes:                  req.TyreSizes,
				// PatternTypes:               req.PatternTypes,
				// UtilizationRateStatusCodes: req.UtilizationRateStatusCodes,
				StatusCodes: []string{constants.ASSET_STATUS_CODE_IN_STOCK, constants.ASSET_STATUS_CODE_NEW_STOCK},
			},
			Preload: models.AssetTyrePreload{
				AssetBrand:            true,
				AssetStatus:           true,
				Tyre:                  true,
				RetreadTyre:           true,
				UtilizationRateStatus: true,
			},
		},
	)
	if err != nil {
		return nil, err
	}

	journalRefs := make([]string, len(assetTyres)*2)
	for _, assetTyre := range assetTyres {
		journalRefs = append(journalRefs, assetTyre.AssetID)
		journalRefs = append(journalRefs, assetTyre.LatestTread().ID)
	}

	refAmounts, err := uc.financeUseCase.GetTotalCostsByReferenceIDs(ctx, journalRefs)
	if err != nil {
		return nil, err
	}

	mapRefAmounts := map[string]financeDtos.ReferenceTotalAmount{}
	for _, refAmount := range refAmounts {
		mapRefAmounts[refAmount.ReferenceID] = refAmount
	}

	respItems := make([]dtos.GetAssetTyreStockReportResp, 0, len(assetTyres))
	for _, assetTyre := range assetTyres {
		respItems = append(respItems, dtos.BuildGetAssetTyreStockReportResp(assetTyre, mapRefAmounts))
	}

	var clientLogo []byte
	clientLogo, _ = uc.attachmentUsecase.StorageRepository.GetFile(ctx, client.Photo.String)
	ext := path.Ext(client.Photo.String)
	tmpl := dtos.ConvertGetAssetTyreStockReportRespListToExport(respItems, claim.GetName(), client.Name, clientLogo, ext)

	xlsx, err := tmpl.ExportToExcelFile()
	if err != nil {
		return nil, err
	}

	defer xlsx.Close()

	signedUrl, err := uc.UploadExcel(ctx, xlsx, "Tyre_Stock_Report")
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: signedUrl,
		Data:        nil,
	}, nil
}

func (uc *AssetTyreUseCase) UploadExcel(ctx context.Context, xlsx *excelize.File, fileName string) (string, error) {
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)
	part, err := writer.CreateFormFile("file", fileName+".xlsx")
	if err != nil {
		return "", err
	}

	buffer, err := xlsx.WriteToBuffer()
	if err != nil {
		return "", err
	}

	if _, err := io.Copy(part, buffer); err != nil {
		return "", err
	}

	if err := writer.Close(); err != nil {
		return "", err
	}

	xlsxRequest, _ := http.NewRequest(http.MethodPost, "", &buf)
	xlsxRequest.Header.Set("Content-Type", writer.FormDataContentType())
	xlsxFile, xlsxHeader, err := xlsxRequest.FormFile("file")
	if err != nil {
		return "", err
	}

	defer xlsxFile.Close()

	xlsxHeader.Filename = storageConstants.TEMP_USER_EXPORT_PREFIX + fileName + "_" + helpers.GenerateSecureFileName(".xlsx")
	err = uc.attachmentUsecase.StorageRepository.UploadFile(ctx, xlsxFile, xlsxHeader)
	if err != nil {
		return "", err
	}

	signedUrl, err := uc.attachmentUsecase.StorageRepository.GetFileSignedURL(ctx, xlsxHeader.Filename, time.Now().Add(24*time.Hour))
	if err != nil {
		return "", err
	}

	return signedUrl, nil
}

func (uc *AssetTyreUseCase) GetAssetTyreInstalledReport(ctx context.Context, req dtos.GetAssetTyreInstalledReportReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	totalRecords, assetTyres, err := uc.AssetTyreRepository.GetAssetTyreInstalledReport(
		ctx, uc.DB.DB(),
		models.GetAssetTyreInstalledReportParam{
			ListRequest: req.ListRequest,
			Cond: models.AssetTyreInstalledCondition{
				Where: models.AssetTyreInstalledWhere{
					ClientID:                   claim.GetLoggedInClientID(),
					ParentAssetIDs:             req.ParentAssetIDs,
					BrandIDs:                   req.BrandIDs,
					TyreSizes:                  req.TyreSizes,
					PatternTypes:               req.PatternTypes,
					UtilizationRateStatusCodes: req.UtilizationRateStatusCodes,
				},
				Preload: models.AssetTyreInstalledPreload{
					Tyre:               true,
					RetreadTyre:        true,
					Asset:              true,
					AssetBrand:         true,
					AssetLinkedVehicle: true,
				},
			},
		},
	)

	if err != nil {
		return nil, err
	}

	journalRefs := make([]string, len(assetTyres)*2)
	for _, assetTyre := range assetTyres {
		journalRefs = append(journalRefs, assetTyre.AssetID)
		journalRefs = append(journalRefs, assetTyre.LatestTread().ID)
	}

	refAmounts, err := uc.financeUseCase.GetTotalCostsByReferenceIDs(ctx, journalRefs)
	if err != nil {
		return nil, err
	}

	mapRefAmounts := map[string]financeDtos.ReferenceTotalAmount{}
	for _, refAmount := range refAmounts {
		mapRefAmounts[refAmount.ReferenceID] = refAmount
	}

	respItems := make([]dtos.GetAssetTyreInstalledReportResp, 0, len(assetTyres))
	for _, assetTyre := range assetTyres {
		respItems = append(respItems, dtos.BuildGetAssetTyreInstalledReportResp(assetTyre, mapRefAmounts))
	}

	return &commonmodel.ListResponse{
		TotalRecords: totalRecords,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respItems,
	}, nil
}

func (uc *AssetTyreUseCase) GetAssetTyreInstalledExport(ctx context.Context) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	client, err := uc.userIdentityRepo.GetClient(ctx, uc.DB.DB(), userIdentityModel.ClientCondition{
		Where: userIdentityModel.ClientWhere{
			ID: claim.GetLoggedInClientID(),
		},
	})

	if err != nil {
		return nil, err
	}

	assetTyres, err := uc.AssetTyreRepository.GetAssetTyreInstalledReportExport(
		ctx, uc.DB.DB(),
		models.AssetTyreInstalledCondition{
			Where: models.AssetTyreInstalledWhere{
				ClientID: claim.GetLoggedInClientID(),
			},
			Preload: models.AssetTyreInstalledPreload{
				Tyre:               true,
				RetreadTyre:        true,
				Asset:              true,
				AssetBrand:         true,
				AssetLinkedVehicle: true,
			},
		},
	)
	if err != nil {
		return nil, err
	}

	journalRefs := make([]string, len(assetTyres)*2)
	for _, assetTyre := range assetTyres {
		journalRefs = append(journalRefs, assetTyre.AssetID)
		journalRefs = append(journalRefs, assetTyre.LatestTread().ID)
	}

	refAmounts, err := uc.financeUseCase.GetTotalCostsByReferenceIDs(ctx, journalRefs)
	if err != nil {
		return nil, err
	}

	mapRefAmounts := map[string]financeDtos.ReferenceTotalAmount{}
	for _, refAmount := range refAmounts {
		mapRefAmounts[refAmount.ReferenceID] = refAmount
	}

	respItems := make([]dtos.GetAssetTyreInstalledReportResp, 0, len(assetTyres))
	for _, assetTyre := range assetTyres {
		respItems = append(respItems, dtos.BuildGetAssetTyreInstalledReportResp(assetTyre, mapRefAmounts))
	}

	var clientLogo []byte
	clientLogo, _ = uc.attachmentUsecase.StorageRepository.GetFile(ctx, client.Photo.String)
	ext := path.Ext(client.Photo.String)

	hasCustomerNameCol := client.HasPackage(
		intenalConstants.CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_4,
		intenalConstants.CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_5,
		intenalConstants.CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_6,
		intenalConstants.CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_7,
	)

	tmpl := dtos.ConvertGetAssetTyreInstalledReportRespListToExport(respItems, claim.GetName(), client.Name, clientLogo, ext, !hasCustomerNameCol)

	xlsx, err := tmpl.ExportToExcelFile()
	if err != nil {
		return nil, err
	}

	defer xlsx.Close()

	signedUrl, err := uc.UploadExcel(ctx, xlsx, "Tyre_Installed_Report")

	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: signedUrl,
		Data:        nil,
	}, nil

}

func (uc *AssetTyreUseCase) GetAssetTyreUninstalledReport(ctx context.Context, req dtos.GetAssetTyreUninnstalledReportReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	totalRecords, assetTyres, err := uc.AssetTyreRepository.GetAssetTyreUninstalledReport(
		ctx, uc.DB.DB(),
		models.GetAssetTyreUninstalledReportParam{
			ListRequest: req.ListRequest,
			Cond: models.AssetTyreUninstalledCondition{
				Where: models.AssetTyreUninstalledWhere{
					ClientID:                        claim.GetLoggedInClientID(),
					BrandIDs:                        req.BrandIDs,
					TyreSizes:                       req.TyreSizes,
					PatternTypes:                    req.PatternTypes,
					StatusCodes:                     req.AssetStatusCodes,
					ParentCustomAssetSubcategoryIDs: req.CustomAssetSubCategoryIDs,
				},
			},
		},
	)
	if err != nil {
		return nil, err
	}

	journalRefs := make([]string, len(assetTyres)*2)
	for _, assetTyre := range assetTyres {
		journalRefs = append(journalRefs, assetTyre.ChildAssetID)
		journalRefs = append(journalRefs, assetTyre.ChildAsset.LatestTread().ID)
	}

	refAmounts, err := uc.financeUseCase.GetTotalCostsByReferenceIDs(ctx, journalRefs)
	if err != nil {
		return nil, err
	}

	mapRefAmounts := map[string]financeDtos.ReferenceTotalAmount{}
	for _, refAmount := range refAmounts {
		mapRefAmounts[refAmount.ReferenceID] = refAmount
	}

	respItems := make([]dtos.GetAssetTyreUninstalledReportResp, 0, len(assetTyres))
	for _, assetTyre := range assetTyres {
		respItems = append(respItems, dtos.BuildGetAssetTyreUninstalledReportResp(assetTyre, mapRefAmounts))
	}

	return &commonmodel.ListResponse{
		TotalRecords: totalRecords,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respItems,
	}, nil
}

func (uc *AssetTyreUseCase) GetAssetTyreUninstalledExport(ctx context.Context) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	client, err := uc.userIdentityRepo.GetClient(ctx, uc.DB.DB(), userIdentityModel.ClientCondition{
		Where: userIdentityModel.ClientWhere{
			ID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	assetTyres, err := uc.AssetTyreRepository.GetAssetTyreUninstalledReportExport(
		ctx, uc.DB.DB(),
		models.GetAssetTyreUninstalledReportParam{
			Cond: models.AssetTyreUninstalledCondition{
				Where: models.AssetTyreUninstalledWhere{
					ClientID: claim.GetLoggedInClientID(),
				},
			},
		},
	)
	if err != nil {
		return nil, err
	}

	journalRefs := make([]string, len(assetTyres)*2)
	for _, assetTyre := range assetTyres {
		journalRefs = append(journalRefs, assetTyre.ChildAssetID)
		journalRefs = append(journalRefs, assetTyre.ChildAsset.LatestTread().ID)
	}

	refAmounts, err := uc.financeUseCase.GetTotalCostsByReferenceIDs(ctx, journalRefs)
	if err != nil {
		return nil, err
	}

	mapRefAmounts := map[string]financeDtos.ReferenceTotalAmount{}
	for _, refAmount := range refAmounts {
		mapRefAmounts[refAmount.ReferenceID] = refAmount
	}

	respItems := make([]dtos.GetAssetTyreUninstalledReportResp, 0, len(assetTyres))
	for _, assetTyre := range assetTyres {
		respItems = append(respItems, dtos.BuildGetAssetTyreUninstalledReportResp(assetTyre, mapRefAmounts))
	}

	var clientLogo []byte
	clientLogo, _ = uc.attachmentUsecase.StorageRepository.GetFile(ctx, client.Photo.String)
	ext := path.Ext(client.Photo.String)

	hasCustomerNameCol := client.HasPackage(
		intenalConstants.CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_4,
		intenalConstants.CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_5,
		intenalConstants.CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_6,
		intenalConstants.CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_7,
	)

	tmpl := dtos.ConvertGetAssetTyreUninstalledReportRespListToExport(respItems, claim.GetName(), client.Name, clientLogo, ext, !hasCustomerNameCol)

	xlsx, err := tmpl.ExportToExcelFile()
	if err != nil {
		return nil, err
	}

	defer xlsx.Close()

	signedUrl, err := uc.UploadExcel(ctx, xlsx, "Tyre_Uninstalled_Report")
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: signedUrl,
		Data:        nil,
	}, nil
}

func (uc *AssetTyreUseCase) GetAssetTyreReplacementForecastReport(ctx context.Context, req dtos.GetAssetTyreReplacementForecastReportReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	totalRecords, assetTyres, err := uc.AssetTyreRepository.GetAssetTyreReplacementForecastReport(
		ctx, uc.DB.DB(),
		models.GetAssetTyreReplacementForecastReportParam{
			ListRequest: req.ListRequest,
			Cond: models.AssetTyreReplacementForecastCondition{
				Where: models.AssetTyreReplacementForecastWhere{
					ClientID: claim.GetLoggedInClientID(),
				},
			},
		},
	)
	if err != nil {
		return nil, err
	}

	journalRefs := make([]string, len(assetTyres)*2)
	for _, assetTyre := range assetTyres {
		journalRefs = append(journalRefs, assetTyre.AssetID)
		journalRefs = append(journalRefs, assetTyre.LatestTread().ID)
	}

	refAmounts, err := uc.financeUseCase.GetTotalCostsByReferenceIDs(ctx, journalRefs)
	if err != nil {
		return nil, err
	}

	mapRefAmounts := map[string]financeDtos.ReferenceTotalAmount{}
	for _, refAmount := range refAmounts {
		mapRefAmounts[refAmount.ReferenceID] = refAmount
	}

	respItems := make([]dtos.GetAssetTyreReplacementForecastReportResp, 0, len(assetTyres))
	for _, assetTyre := range assetTyres {
		respItems = append(respItems, dtos.BuildGetAssetTyreReplacementForecastReportResp(assetTyre, mapRefAmounts))
	}

	return &commonmodel.ListResponse{
		TotalRecords: totalRecords,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respItems,
	}, nil
}

func (uc *AssetTyreUseCase) GetAssetTyreReplacementForecastExport(ctx context.Context) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	client, err := uc.userIdentityRepo.GetClient(ctx, uc.DB.DB(), userIdentityModel.ClientCondition{
		Where: userIdentityModel.ClientWhere{
			ID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	assetTyres, err := uc.AssetTyreRepository.GetAssetTyreReplacementForecastReportExport(
		ctx, uc.DB.DB(),
		models.GetAssetTyreReplacementForecastReportParam{
			Cond: models.AssetTyreReplacementForecastCondition{
				Where: models.AssetTyreReplacementForecastWhere{
					ClientID: claim.GetLoggedInClientID(),
				},
			},
		},
	)
	if err != nil {
		return nil, err
	}

	journalRefs := make([]string, len(assetTyres)*2)
	for _, assetTyre := range assetTyres {
		journalRefs = append(journalRefs, assetTyre.AssetID)
		journalRefs = append(journalRefs, assetTyre.LatestTread().ID)
	}

	refAmounts, err := uc.financeUseCase.GetTotalCostsByReferenceIDs(ctx, journalRefs)
	if err != nil {
		return nil, err
	}

	mapRefAmounts := map[string]financeDtos.ReferenceTotalAmount{}
	for _, refAmount := range refAmounts {
		mapRefAmounts[refAmount.ReferenceID] = refAmount
	}

	respItems := make([]dtos.GetAssetTyreReplacementForecastReportResp, 0, len(assetTyres))
	for _, assetTyre := range assetTyres {
		respItems = append(respItems, dtos.BuildGetAssetTyreReplacementForecastReportResp(assetTyre, mapRefAmounts))
	}

	var clientLogo []byte
	clientLogo, _ = uc.attachmentUsecase.StorageRepository.GetFile(ctx, client.Photo.String)
	ext := path.Ext(client.Photo.String)

	hasCustomerNameCol := client.HasPackage(
		intenalConstants.CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_4,
		intenalConstants.CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_5,
		intenalConstants.CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_6,
		intenalConstants.CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_7,
	)

	tmpl := dtos.ConvertGetAssetTyreReplacementForecastReportRespListToExport(respItems, claim.GetName(), client.Name, clientLogo, ext, !hasCustomerNameCol)

	xlsx, err := tmpl.ExportToExcelFile()
	if err != nil {
		return nil, err
	}

	defer xlsx.Close()

	signedUrl, err := uc.UploadExcel(ctx, xlsx, "Tyre_Replacement_Forecast_Report")
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: signedUrl,
		Data:        nil,
	}, nil
}

func (uc *AssetTyreUseCase) GetAssetTyreUsageReport(ctx context.Context, req dtos.GetAssetTyreUsageReportReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	totalRecords, assetTyres, err := uc.AssetTyreRepository.GetAssetTyreUsageReport(
		ctx, uc.DB.DB(),
		models.GetAssetTyreUsageReportParam{
			ListRequest: req.ListRequest,
			Cond: models.AssetTyreUsageCondition{
				Where: models.AssetTyreUsageWhere{
					ClientID:                        claim.GetLoggedInClientID(),
					BrandIDs:                        req.BrandIDs,
					TyreSizes:                       req.TyreSizes,
					PatternTypes:                    req.PatternTypes,
					ParentCustomAssetSubcategoryIDs: req.CustomAssetSubCategoryIDs,
					StartDate:                       req.StartDate,
					EndDate:                         req.EndDate,
				},
			},
		},
	)
	if err != nil {
		return nil, err
	}

	journalRefs := make([]string, len(assetTyres)*2)
	for _, assetTyre := range assetTyres {
		journalRefs = append(journalRefs, assetTyre.AssetID)
		journalRefs = append(journalRefs, assetTyre.LatestTread().ID)
	}

	refAmounts, err := uc.financeUseCase.GetTotalCostsByReferenceIDs(ctx, journalRefs)
	if err != nil {
		return nil, err
	}

	mapRefAmounts := map[string]financeDtos.ReferenceTotalAmount{}
	for _, refAmount := range refAmounts {
		mapRefAmounts[refAmount.ReferenceID] = refAmount
	}

	respItems := make([]dtos.GetAssetTyreUsageReportResp, 0, len(assetTyres))
	for _, assetTyre := range assetTyres {
		respItems = append(respItems, dtos.BuildGetAssetTyreUsageReportResp(assetTyre, mapRefAmounts))
	}

	return &commonmodel.ListResponse{
		TotalRecords: totalRecords,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respItems,
	}, nil
}

func (uc *AssetTyreUseCase) GetAssetTyreUsageReportExport(ctx context.Context, req dtos.GetAssetTyreUsageReportReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	client, err := uc.userIdentityRepo.GetClient(ctx, uc.DB.DB(), userIdentityModel.ClientCondition{
		Where: userIdentityModel.ClientWhere{
			ID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	assetTyres, err := uc.AssetTyreRepository.GetAssetTyreUsageReportExport(
		ctx, uc.DB.DB(),
		models.GetAssetTyreUsageReportParam{
			Cond: models.AssetTyreUsageCondition{
				Where: models.AssetTyreUsageWhere{
					StartDate: req.StartDate,
					EndDate:   req.EndDate,
					ClientID:  claim.GetLoggedInClientID(),
				},
			},
		},
	)
	if err != nil {
		return nil, err
	}

	journalRefs := make([]string, len(assetTyres)*2)
	for _, assetTyre := range assetTyres {
		journalRefs = append(journalRefs, assetTyre.AssetID)
		journalRefs = append(journalRefs, assetTyre.LatestTread().ID)
	}

	refAmounts, err := uc.financeUseCase.GetTotalCostsByReferenceIDs(ctx, journalRefs)
	if err != nil {
		return nil, err
	}

	mapRefAmounts := map[string]financeDtos.ReferenceTotalAmount{}
	for _, refAmount := range refAmounts {
		mapRefAmounts[refAmount.ReferenceID] = refAmount
	}

	respItems := make([]dtos.GetAssetTyreUsageReportResp, 0, len(assetTyres))
	for _, assetTyre := range assetTyres {
		respItems = append(respItems, dtos.BuildGetAssetTyreUsageReportResp(assetTyre, mapRefAmounts))
	}

	var clientLogo []byte
	clientLogo, _ = uc.attachmentUsecase.StorageRepository.GetFile(ctx, client.Photo.String)
	ext := path.Ext(client.Photo.String)

	hasCustomerNameCol := client.HasPackage(
		intenalConstants.CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_4,
		intenalConstants.CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_5,
		intenalConstants.CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_6,
		intenalConstants.CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_7,
	)

	tmpl := dtos.ConvertGetAssetTyreUsageReportRespListToExport(respItems, claim.GetName(), client.Name, clientLogo, ext, !hasCustomerNameCol, req)

	xlsx, err := tmpl.ExportToExcelFile()
	if err != nil {
		return nil, err
	}

	defer xlsx.Close()

	signedUrl, err := uc.UploadExcel(ctx, xlsx, "Tyre_Usage_Report")
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: signedUrl,
		Data:        nil,
	}, nil
}

func (uc *AssetTyreUseCase) GetAssetTyreInspectionReport(ctx context.Context, req dtos.GetAssetTyreInspectionReportReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	totalRecords, assetTyres, err := uc.AssetTyreRepository.GetAssetTyreInspectionReport(
		ctx, uc.DB.DB(),
		models.GetAssetTyreInspectionReportParam{
			ListRequest: req.ListRequest,
			Cond: models.AssetTyreInspectionReportCondition{
				Where: models.AssetTyreInspectionReportWhere{
					ClientID:                   claim.GetLoggedInClientID(),
					StartDate:                  req.StartDate,
					EndDate:                    req.EndDate,
					BrandIDs:                   req.BrandIDs,
					TyreSizes:                  req.TyreSizes,
					PatternTypes:               req.PatternTypes,
					StatusCodes:                req.AssetStatusCodes,
					UtilizationRateStatusCodes: req.UtilizationRateStatusCodes,
				},
			},
		},
	)
	if err != nil {
		return nil, err
	}

	respItems := make([]dtos.GetAssetTyreInspectionReportResp, 0, len(assetTyres))
	for _, assetTyre := range assetTyres {
		respItems = append(respItems, dtos.BuildGetAssetTyreInspectionReportResp(assetTyre))
	}

	return &commonmodel.ListResponse{
		TotalRecords: totalRecords,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respItems,
	}, nil
}

func (uc *AssetTyreUseCase) GetAssetTyreInspectionReportExport(ctx context.Context, req dtos.GetAssetTyreInspectionReportReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	client, err := uc.userIdentityRepo.GetClient(ctx, uc.DB.DB(), userIdentityModel.ClientCondition{
		Where: userIdentityModel.ClientWhere{
			ID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	assetTyres, err := uc.AssetTyreRepository.GetAssetTyreInspectionReportExport(
		ctx, uc.DB.DB(),
		models.GetAssetTyreInspectionReportParam{
			Cond: models.AssetTyreInspectionReportCondition{
				Where: models.AssetTyreInspectionReportWhere{
					ClientID:                   claim.GetLoggedInClientID(),
					StartDate:                  req.StartDate,
					EndDate:                    req.EndDate,
					BrandIDs:                   req.BrandIDs,
					TyreSizes:                  req.TyreSizes,
					PatternTypes:               req.PatternTypes,
					StatusCodes:                req.AssetStatusCodes,
					UtilizationRateStatusCodes: req.UtilizationRateStatusCodes,
				},
			},
		},
	)
	if err != nil {
		return nil, err
	}

	respItems := make([]dtos.GetAssetTyreInspectionReportResp, 0, len(assetTyres))
	for _, assetTyre := range assetTyres {
		respItems = append(respItems, dtos.BuildGetAssetTyreInspectionReportResp(assetTyre))
	}

	var clientLogo []byte
	clientLogo, _ = uc.attachmentUsecase.StorageRepository.GetFile(ctx, client.Photo.String)
	ext := path.Ext(client.Photo.String)

	hasCustomerNameCol := client.HasPackage(
		intenalConstants.CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_4,
		intenalConstants.CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_5,
		intenalConstants.CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_6,
		intenalConstants.CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_7,
	)

	tmpl := dtos.ConvertGetAssetTyreInspectionReportRespListToExport(respItems, claim.GetName(), client.Name, clientLogo, ext, !hasCustomerNameCol, req)

	xlsx, err := tmpl.ExportToExcelFile()
	if err != nil {
		return nil, err
	}

	defer xlsx.Close()

	signedUrl, err := uc.UploadExcel(ctx, xlsx, "Tyre_Inspection_Report")
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: signedUrl,
		Data:        nil,
	}, nil
}

func (uc *AssetTyreUseCase) GetAssetTyreRotationReport(ctx context.Context, req dtos.GetAssetTyreRotationReportReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	totalRecords, assetTyres, err := uc.AssetTyreRepository.GetAssetTyreRotationReport(
		ctx, uc.DB.DB(),
		models.GetAssetTyreRotationReportParam{
			ListRequest: req.ListRequest,
			Cond: models.AssetTyreRotationReportCondition{
				Where: models.AssetTyreRotationReportWhere{
					ClientID:  claim.GetLoggedInClientID(),
					StartDate: req.StartDate,
					EndDate:   req.EndDate,
				},
			},
		},
	)
	if err != nil {
		return nil, err
	}

	respItems := make([]dtos.GetAssetTyreRotationReportResp, 0, len(assetTyres))
	for _, assetTyre := range assetTyres {
		respItems = append(respItems, dtos.BuildGetAssetTyreRotationReportResp(assetTyre))
	}

	return &commonmodel.ListResponse{
		TotalRecords: totalRecords,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respItems,
	}, nil
}

func (uc *AssetTyreUseCase) GetAssetTyreRotationReportExport(ctx context.Context, req dtos.GetAssetTyreRotationReportReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	client, err := uc.userIdentityRepo.GetClient(ctx, uc.DB.DB(), userIdentityModel.ClientCondition{
		Where: userIdentityModel.ClientWhere{
			ID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	assetTyres, err := uc.AssetTyreRepository.GetAssetTyreRotationReportExport(
		ctx, uc.DB.DB(),
		models.GetAssetTyreRotationReportParam{
			Cond: models.AssetTyreRotationReportCondition{
				Where: models.AssetTyreRotationReportWhere{
					ClientID:  claim.GetLoggedInClientID(),
					StartDate: req.StartDate,
					EndDate:   req.EndDate,
				},
			},
		},
	)
	if err != nil {
		return nil, err
	}

	respItems := make([]dtos.GetAssetTyreRotationReportResp, 0, len(assetTyres))
	for _, assetTyre := range assetTyres {
		respItems = append(respItems, dtos.BuildGetAssetTyreRotationReportResp(assetTyre))
	}

	var clientLogo []byte
	clientLogo, _ = uc.attachmentUsecase.StorageRepository.GetFile(ctx, client.Photo.String)
	ext := path.Ext(client.Photo.String)

	hasCustomerNameCol := client.HasPackage(
		intenalConstants.CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_4,
		intenalConstants.CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_5,
		intenalConstants.CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_6,
		intenalConstants.CLIENT_PACKAGE_DIGISPECT_INSPECTION_FLOW_SCENARIO_7,
	)

	tmpl := dtos.ConvertGetAssetTyreRotationReportRespListToExport(respItems, claim.GetName(), client.Name, clientLogo, ext, !hasCustomerNameCol, req)

	xlsx, err := tmpl.ExportToExcelFile()
	if err != nil {
		return nil, err
	}

	defer xlsx.Close()

	signedUrl, err := uc.UploadExcel(ctx, xlsx, "Tyre_Rotation_Report")
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: signedUrl,
		Data:        nil,
	}, nil
}
