package usecase

import (
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"
	contentModel "assetfindr/internal/app/content/models"
	contentRepository "assetfindr/internal/app/content/repository"
	notificationConstants "assetfindr/internal/app/notification/constants"
	notificationDtos "assetfindr/internal/app/notification/dtos"
	notificationUsecase "assetfindr/internal/app/notification/usecase"
	storageConstants "assetfindr/internal/app/storage/constants"
	storageDtos "assetfindr/internal/app/storage/dtos"
	storageUsecase "assetfindr/internal/app/storage/usecase"
	taskConstants "assetfindr/internal/app/task/constants"
	taskModel "assetfindr/internal/app/task/models"
	taskRepository "assetfindr/internal/app/task/repository"
	userIdentityConstants "assetfindr/internal/app/user-identity/constants"
	userIdentityModel "assetfindr/internal/app/user-identity/models"
	userIdentityRepository "assetfindr/internal/app/user-identity/repository"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"
	"assetfindr/pkg/common/helpers/tmplhelpers"
	"context"
	"fmt"
	"html/template"
	"time"

	"github.com/jackc/pgtype"

	"sort"
	"strconv"
	"strings"

	"gopkg.in/guregu/null.v4"
)

type WeightBridgeUseCase struct {
	DB                  database.DBUsecase
	weightBridgeRepo    repository.WeightBridgeRepository
	locationRepository  repository.LocationRepository
	UserRepository      userIdentityRepository.UserRepository
	storageUsecase      *storageUsecase.AttachmentUseCase
	materialRepo        contentRepository.MaterialRepository
	workShiftRepo       userIdentityRepository.WorkShiftRepository
	assetRepo           repository.AssetRepository
	assetAssignmentRepo repository.AssetAssignmentRepository
	ticketRepo          taskRepository.TicketRepository
	notifUseCase        *notificationUsecase.NotificationUseCase
}

func NewWeightBridgeUseCase(
	DB database.DBUsecase,
	weightBridgeRepo repository.WeightBridgeRepository,
	userRepository userIdentityRepository.UserRepository,
	storageUsecase *storageUsecase.AttachmentUseCase,
	locationRepository repository.LocationRepository,
	materialRepo contentRepository.MaterialRepository,
	workShiftRepo userIdentityRepository.WorkShiftRepository,
	assetRepo repository.AssetRepository,
	assetAssignmentRepo repository.AssetAssignmentRepository,
	ticketRepo taskRepository.TicketRepository,
) *WeightBridgeUseCase {
	return &WeightBridgeUseCase{
		DB:                  DB,
		weightBridgeRepo:    weightBridgeRepo,
		UserRepository:      userRepository,
		storageUsecase:      storageUsecase,
		locationRepository:  locationRepository,
		materialRepo:        materialRepo,
		workShiftRepo:       workShiftRepo,
		assetRepo:           assetRepo,
		assetAssignmentRepo: assetAssignmentRepo,
		ticketRepo:          ticketRepo,
	}
}

func (uc *WeightBridgeUseCase) SetNotifUseCase(notifUseCase *notificationUsecase.NotificationUseCase) {
	uc.notifUseCase = notifUseCase
}

// Fetch active work shifts
// GetShift returns the work shift covering oTime and the date the shift belongs to
// (midnight at the shift's start date in loc). Half-open intervals [start, end).
func (uc *WeightBridgeUseCase) GetShift(
	ctx context.Context,
	clientID string,
	oTime time.Time,
) (userIdentityModel.WorkShift, time.Time, bool) {
	// Fetch shifts
	workShifts, err := uc.workShiftRepo.GetWorkShifts(ctx, uc.DB.DB(), userIdentityModel.GetWorkShiftListParam{
		Cond: userIdentityModel.WorkShiftCondition{
			Where: userIdentityModel.WorkShiftWhere{
				ClientID:   clientID,
				StatusCode: userIdentityConstants.WORK_SHIFT_STATUS_CODE_ACTIVE,
			},
		},
	})
	if err != nil || len(workShifts) == 0 {
		return userIdentityModel.WorkShift{}, time.Time{}, false
	}

	// Location (inject this if you can)
	loc := time.FixedZone("UTC+8", 8*60*60)
	t := oTime.In(loc)
	baseDate := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, loc)
	outboundSec := t.Hour()*3600 + t.Minute()*60 + t.Second()

	// Optional: deterministic evaluation
	sort.SliceStable(workShifts, func(i, j int) bool {
		si := mustParseHHMMSS(workShifts[i].Start)
		sj := mustParseHHMMSS(workShifts[j].Start)
		return si < sj
	})

	for _, ws := range workShifts {
		startSec, ok1 := parseHHMMSS(ws.Start)
		endSec, ok2 := parseHHMMSS(ws.End)
		if !ok1 || !ok2 {
			continue
		}

		// Default to today's date; adjust for overnight early-morning below.
		shiftDate := baseDate

		if endSec > startSec {
			// Normal: [start, end)
			if outboundSec >= startSec && outboundSec < endSec {
				return ws, shiftDate, true
			}
		} else if endSec < startSec {
			// Overnight: [start, 24h) ∪ [0, end)
			if outboundSec >= startSec {
				// Evening portion -> same calendar day
				return ws, shiftDate, true
			}
			if outboundSec < endSec {
				// After-midnight portion -> belongs to yesterday
				return ws, shiftDate.AddDate(0, 0, -1), true
			}
		} else {
			// start == end -> 24h shift
			return ws, shiftDate, true
		}
	}

	return userIdentityModel.WorkShift{}, time.Time{}, false
}

// parseHHMMSS parses "HH:MM:SS" to seconds since midnight.
// Accepts "24:00:00" as end-of-day (returns 86400).
func parseHHMMSS(s string) (int, bool) {
	parts := strings.Split(s, ":")
	if len(parts) != 3 {
		return 0, false
	}
	h, err1 := strconv.Atoi(parts[0])
	m, err2 := strconv.Atoi(parts[1])
	sec, err3 := strconv.Atoi(parts[2])
	if err1 != nil || err2 != nil || err3 != nil {
		return 0, false
	}
	if h == 24 && m == 0 && sec == 0 {
		return 24 * 3600, true
	}
	if h < 0 || h > 23 || m < 0 || m > 59 || sec < 0 || sec > 59 {
		return 0, false
	}
	return h*3600 + m*60 + sec, true
}

func mustParseHHMMSS(s string) int {
	if v, ok := parseHHMMSS(s); ok {
		return v
	}
	return 0
}

func (uc *WeightBridgeUseCase) GetWeightBridgeTickets(ctx context.Context, req dtos.WeightBridgeTicketListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	count, tickets, err := uc.weightBridgeRepo.GetWeightBridgeTicketListWithLocationRoute(ctx, uc.DB.DB(), models.GetWeightBridgeTicketListParam{
		ListRequest: req.ListRequest,
		Cond: models.WeightBridgeTicketCondition{
			Where: models.WeightBridgeTicketWhere{
				ClientID:                claim.GetLoggedInClientID(),
				ReconciliationDateStart: req.ReconciliationDateStart,
				ReconciliationDateEnd:   req.ReconciliationDateEnd,
				TicketDateStart:         req.TicketDateStart,
				TicketDateEnd:           req.TicketDateEnd,
				VehicleAssetID:          req.VehicleAssetID,
				VehicleAssetIDs:         req.VehicleAssetIDs,
				WorkShiftID:             req.WorkShiftID,
				WorkShiftIDs:            req.WorkShiftIDs,
			},
			Preload: models.WeightBridgeTicketPreload{
				AssetVehicle:                 true,
				InboundWeightBridgeLocation:  true,
				InboundLocation:              true,
				OutboundWeightBridgeLocation: true,
				OutboundLocation:             true,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	var userIds []string
	var materialIDs []string

	for _, ticket := range tickets {
		if ticket.CreatedBy != "" {
			userIds = append(userIds, ticket.CreatedBy)
			userIds = append(userIds, ticket.UpdatedBy)
		}
		if ticket.MaterialID != "" {
			materialIDs = append(materialIDs, ticket.MaterialID)
		}
	}

	usersMapById := map[string]userIdentityModel.User{}

	err = uc.UserRepository.GetUsersInMapByIds(ctx, uc.DB.DB(), &usersMapById, userIds)
	if err != nil {
		commonlogger.Errorf("Error in getting users by user ids from identity service", err)
		return nil, err
	}

	materialsMapByID := map[string]contentModel.Material{}
	if len(materialIDs) > 0 {
		_, materials, err := uc.materialRepo.GetMaterialList(ctx, uc.DB.DB(), contentModel.GetMaterialListParam{
			ListRequest: commonmodel.ListRequest{
				PageSize: len(materialIDs),
				PageNo:   1,
			},
			Cond: contentModel.MaterialCondition{
				Where: contentModel.MaterialWhere{
					IDs:      materialIDs,
					ClientID: claim.GetLoggedInClientID(),
				},
			},
		})
		if err != nil {
			return nil, err
		}
		for _, material := range materials {
			materialsMapByID[material.ID] = material
		}
	}

	respData := make([]dtos.WeightBridgeTicket, 0, len(tickets))

	for _, ticket := range tickets {
		weightBridgeTicket := dtos.WeightBridgeTicket{}
		weightBridgeTicket.SetWithLocationRoute(ticket, usersMapById, materialsMapByID)
		respData = append(respData, weightBridgeTicket)
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respData,
	}, nil
}

func (uc *WeightBridgeUseCase) GetWeightBridgeTicket(ctx context.Context, id string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	ticket, err := uc.weightBridgeRepo.GetWeightBridgeTicketWithLocationRoute(ctx, uc.DB.DB(), models.WeightBridgeTicketCondition{
		Where: models.WeightBridgeTicketWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
		Preload: models.WeightBridgeTicketPreload{
			AssetVehicle:                 true,
			InboundWeightBridgeLocation:  true,
			InboundLocation:              true,
			OutboundWeightBridgeLocation: true,
			OutboundLocation:             true,
		},
	})
	if err != nil {
		return nil, err
	}

	var userIds []string

	if ticket.CreatedBy != "" {
		userIds = append(userIds, ticket.CreatedBy)
		userIds = append(userIds, ticket.UpdatedBy)
	}

	usersMapById := map[string]userIdentityModel.User{}

	err = uc.UserRepository.GetUsersInMapByIds(ctx, uc.DB.DB(), &usersMapById, userIds)
	if err != nil {
		commonlogger.Errorf("Error in getting users by user ids from identity service", err)
		return nil, err
	}

	materialsMapByID := map[string]contentModel.Material{}
	if ticket.MaterialID != "" {
		material, err := uc.materialRepo.GetMaterial(ctx, uc.DB.DB(), contentModel.MaterialCondition{
			Where: contentModel.MaterialWhere{
				ID:       ticket.MaterialID,
				ClientID: claim.GetLoggedInClientID(),
			},
		})
		if err != nil {
			return nil, err
		}

		materialsMapByID[material.ID] = *material
	}

	respData := dtos.WeightBridgeTicket{}
	respData.SetWithLocationRoute(*ticket, usersMapById, materialsMapByID)

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        respData,
	}, nil

}

func (uc *WeightBridgeUseCase) GetWeightBridgeTicketSummary(ctx context.Context, req dtos.WeightBridgeTicketListReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	ticketSummary, err := uc.weightBridgeRepo.GetWeightBridgeTicketSummary(ctx, uc.DB.DB(), models.WeightBridgeTicketCondition{
		Where: models.WeightBridgeTicketWhere{
			ReconciliationDateStart: req.ReconciliationDateStart,
			ReconciliationDateEnd:   req.ReconciliationDateEnd,
			TicketDateStart:         req.TicketDateStart,
			TicketDateEnd:           req.TicketDateEnd,
			WorkShiftID:             req.WorkShiftID,
			WorkShiftIDs:            req.WorkShiftIDs,
			ClientID:                claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        ticketSummary,
	}, nil
}

func (uc *WeightBridgeUseCase) CreateWeightBridgeTareWorkOrder(ctx context.Context, tX database.DBUsecase, req dtos.CreateWeightBridgeTicket, weightBridgeTicket *models.WeightBridgeTicket) error {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}

	// CONSTRUCT WB RELATED DATA
	tare := req.OutboundWeightKg
	operatorName := weightBridgeTicket.OutboundOperatorUserFirstName + " " + weightBridgeTicket.OutboundOperatorUserLastName
	ticketDate := weightBridgeTicket.OutboundAt
	// format parsedTicket from ticketDate, but use YYYY-mm-dd hh:mm:ss format
	parsedTicket := ticketDate.Time.Format("2006-01-02 15:04:05")

	// CONSTRUCT WORK ORDER
	ticket := &taskModel.Ticket{
		Subject: "Tare Weight Exceeded 94,000 kg",
		Description: fmt.Sprintf(`
		This work order is issued based on the following weighbridge record:
		Weighbridge Ticket Number: %s
		Ticket Date: %s
		Tare Weight (kg): %d
		Operator Name: %s`,
			weightBridgeTicket.ReferenceID,
			parsedTicket,
			tare,
			operatorName,
		),
		TicketCategoryCode:  taskConstants.TICKET_CATEGORY_CODE_OTHERS,
		TicketReferenceCode: taskConstants.TICKET_REFERENCE_CODE_ASSET_VEHICLE,
		ReferenceID:         weightBridgeTicket.VehicleAssetID,
		SeverityLevelCode:   taskConstants.TICKET_SEVERITY_NOT_SET,
		RequesterUserID:     claim.UserID,
		AssignedToUserID:    null.StringFrom(taskConstants.TICKET_ASSIGNED_TO_USER_ASSEFTINDR_CS),
		StatusCode:          taskConstants.TICKET_STATUS_CODE_OPEN,
		DepartmentID:        "",
		LocationID:          "",
		ScheduleDatetime:    nil,
		ScheduleDatetimeEnd: nil,
		DueDatetime:         nil,
	}

	ticket.StatusCode = taskConstants.TICKET_STATUS_CODE_ASSIGNED

	// GET DEPARTMENT ID
	userClient, err := uc.UserRepository.GetUserClient(ctx, uc.DB.DB(), userIdentityModel.UserClientCondition{
		Where: userIdentityModel.UserClientWhere{
			UserID:   taskConstants.TICKET_ASSIGNED_TO_USER_ASSEFTINDR_CS,
			ClientID: taskConstants.TICKET_CLIENT_ASSEFTINDR_CS,
		},
	})
	if err != nil {
		return err
	}

	ticket.DepartmentID = userClient.DepartmentID.String

	dataInformation, err := uc.assetRepo.GetAssetDataInformation(ctx, uc.DB.DB(), req.VehicleAssetID)
	if err != nil {
		return err
	}

	ticket.AssetDataInformation = pgtype.JSONB{Bytes: dataInformation, Status: pgtype.Present}

	err = uc.ticketRepo.CreateTicket(ctx, tX.DB(), ticket)
	if err != nil {
		return err
	}

	// ATTACHMENT DISABLED FOR NOW
	// _, err = uc.attachmentUseCase.CreateAttachmentsPhotosV2(ctx, storageDtos.UpsertAttachmentReq{
	// 	ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_TICKET,
	// 	SourceReferenceID: ticket.ID,
	// 	ClientID:          claim.GetLoggedInClientID(),
	// 	Photos:            nil,
	// })
	// if err != nil {
	// 	return nil, err
	// }

	// notifyAfterCreateTicket
	// Get Ticket Subject
	ticketID := ticket.ID
	ticket, err = uc.ticketRepo.GetTicket(ctx, uc.DB.DB(), taskModel.TicketCondition{
		Where: taskModel.TicketWhere{
			ID: ticketID,
		},
		Preload: taskModel.TicketPreload{
			TicketCategory: true,
		},
	})
	if err != nil {
		commonlogger.Warnf("Error in getting ticket preload", err)
		return nil
	}

	assetName := ""
	if ticket.ReferenceID != taskConstants.GENERAL_TICKET_REFERENCE_ID {
		asset, err := uc.assetRepo.GetAssetByID(ctx, uc.DB.DB(), ticket.ReferenceID)
		if err != nil {
			commonlogger.Warnf("Error in getting asset by asset id from asset service", err)
			return nil
		}

		if asset.ReferenceNumber == "" {
			assetName = fmt.Sprintf("%s - %s", asset.Name, asset.SerialNumber)
		} else {
			assetName = fmt.Sprintf("%s - %s", asset.Name, asset.ReferenceNumber)
		}
	}

	user := &userIdentityModel.User{}
	if ticket.AssignedToUserID.String != "" {
		var err error
		user, err = uc.UserRepository.GetUser(ctx, uc.DB.DB(), userIdentityModel.UserCondition{
			Where: userIdentityModel.UserWhere{
				ID: ticket.AssignedToUserID.String,
			},
		})
		if err != nil {
			commonlogger.Warnf("error get user on notify update ticket status", err)
			return nil
		}
	}

	client, err := uc.UserRepository.GetClient(ctx, uc.DB.DB(), userIdentityModel.ClientCondition{
		Where: userIdentityModel.ClientWhere{
			ID: ticket.ClientID,
		},
	})
	if err != nil {
		commonlogger.Warnf("failed to get client for email %v", err)
		return nil
	}

	href := uc.notifUseCase.GenerateTargetURL(ctx, client.ClientAlias, notificationConstants.DESTINATION_TYPE_WORK_ORDER, ticket.ID)

	templateBod := tmplhelpers.CreateTicketBody{
		Subject:        ticket.Subject,
		AssetName:      assetName,
		UserAssigned:   "-",
		RedirectWOLink: template.URL(href),
		SeverityLevel:  "-",
		TicketDesc:     "-",
	}
	if taskConstants.MapTicketSeverityLabel[ticket.SeverityLevelCode] != "" {
		templateBod.SeverityLevel = taskConstants.MapTicketSeverityLabel[ticket.SeverityLevelCode]
	}
	if ticket.Description != "" {
		templateBod.TicketDesc = ticket.Description
	}
	if user.GetName() != "" {
		templateBod.UserAssigned = user.GetName()
	}

	assetAssignment, err := uc.assetAssignmentRepo.GetAssetAssignment(ctx, uc.DB.DB(), models.AssetAssignmentCondition{
		Where: models.AssetAssignmentWhere{
			AssetID:  ticket.ReferenceID,
			Assigned: true,
		},
	})
	if err != nil {
		commonlogger.Warnf("failed to get asset assignment for id %v", err)
		return nil
	}

	notifItem := notificationDtos.CreateNotificationItem{
		SourceCode:        notificationConstants.NOTIFICATION_SOURCE_CODE_TICKET_ASSIGNMENT,
		SourceReferenceID: ticket.ID,
		TargetReferenceID: ticket.ReferenceID,
		TargetURL:         href,
		MessageHeader:     templateBod.ConstructTitleEmail(),
		MessageBody:       templateBod.ConstructBodyEmail(),
		MessageFirebase: notificationDtos.MessageFirebase{
			Title: templateBod.ConstructTitlePushNotif(),
			Body:  templateBod.ConstructBodyPushNotif(),
		},
		ClientID:        ticket.ClientID,
		TypeCode:        "",
		ContentTypeCode: "",
		ReferenceCode:   notificationConstants.NOTIF_REF_WORK_ORDER,
		ReferenceValue:  ticket.ID,
		UserID:          assetAssignment.UserID,
	}

	itemNotifications := []notificationDtos.CreateNotificationItem{
		notifItem,
	}

	uc.notifUseCase.CreateNotification(ctx, notificationDtos.CreateNotificationReq{
		Items:           itemNotifications,
		SendToEmail:     true,
		SendToPushNotif: true,
	})

	return nil
}

func (uc *WeightBridgeUseCase) CreateWeightBridgeTicket(ctx context.Context, req dtos.CreateWeightBridgeTicket) (*commonmodel.CreateResponse, error) {
	tX, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	defer tX.Rollback()

	weightBridgeTicket := &models.WeightBridgeTicket{
		ReferenceID:                    req.ReferenceID,
		VehicleAssetID:                 req.VehicleAssetID,
		LoadType:                       req.LoadType,
		InboundAt:                      req.InboundAt,
		InboundWeightKg:                req.InboundWeightKg,
		InboundWeightBridgeLocationID:  req.InboundWeightBridgeLocationID,
		InboundOperatorUserID:          req.InboundOperatorUserID,
		InboundOperatorUserFirstName:   req.InboundOperatorUserFirstName,
		InboundOperatorUserLastName:    req.InboundOperatorUserLastName,
		InboundLocationID:              req.InboundLocationID,
		OutboundAt:                     req.OutboundAt,
		OutboundWeightKg:               req.OutboundWeightKg,
		OutboundWeightBridgeLocationID: req.OutboundWeightBridgeLocationID,
		OutboundOperatorUserID:         req.OutboundOperatorUserID,
		OutboundOperatorUserFirstName:  req.OutboundOperatorUserFirstName,
		OutboundOperatorUserLastName:   req.OutboundOperatorUserLastName,
		OutboundLocationID:             req.OutboundLocationID,
		NetWeight:                      req.NetWeight,
		Remark:                         req.Remark,
		MaterialID:                     req.MaterialID,
		TicketDate:                     req.OutboundAt,
	}

	if req.OutboundAt.Valid {
		shift, ticketDate, isSuccess := uc.GetShift(ctx, claim.GetLoggedInClientID(), req.OutboundAt.Time)
		if isSuccess {
			weightBridgeTicket.WorkShiftID = shift.ID
			weightBridgeTicket.WorkShiftAlias = shift.ShiftAlias
			weightBridgeTicket.WorkShiftName = shift.ShiftName
			weightBridgeTicket.ReconciliationDate = null.NewTime(ticketDate, true)

		}
	}

	err = uc.weightBridgeRepo.CreateWeightBridgeTicket(ctx, tX.DB(), weightBridgeTicket)
	if err != nil {
		return nil, err
	}

	if req.OutboundWeightKg > 94000 && !weightBridgeTicket.HasWorkOrder {
		if err := uc.CreateWeightBridgeTareWorkOrder(ctx, tX, req, weightBridgeTicket); err != nil {
			return nil, err
		}

		weightBridgeTicket.HasWorkOrder = true

		err = uc.weightBridgeRepo.UpdateWeightBridgeTicket(ctx, tX.DB(), weightBridgeTicket)
		if err != nil {
			return nil, err
		}
	}

	_, err = uc.storageUsecase.CreateAttachmentsPhotosV2(ctx, storageDtos.UpsertAttachmentReq{
		ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_WEIGHT_BRIDGE_TICKETS,
		SourceReferenceID: weightBridgeTicket.ID,
		ClientID:          claim.GetLoggedInClientID(),
		Photos:            req.Photos,
	})

	if err != nil {
		return nil, err
	}

	err = tX.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: weightBridgeTicket.ID,
		Data:        nil,
	}, nil
}

func (uc *WeightBridgeUseCase) ValidateWeightBridgeReference(ctx context.Context, referenceID string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	weightBridgeTicket, err := uc.weightBridgeRepo.GetWeightBridgeTicket(ctx, uc.DB.DB(), models.WeightBridgeTicketCondition{
		Where: models.WeightBridgeTicketWhere{
			ClientID:    claim.GetLoggedInClientID(),
			ReferenceID: referenceID,
		},
	})
	if err != nil && err.Error() != "WEIGHT_BRIDGE_TICKET_NOT_FOUND" {
		return nil, err
	}

	var ticketID *string
	if weightBridgeTicket != nil {
		ticketID = &weightBridgeTicket.ReferenceID
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: referenceID,
		Data:        ticketID,
	}, nil
}

func (uc *WeightBridgeUseCase) UpdateWeightBridgeTicket(ctx context.Context, req dtos.CreateWeightBridgeTicket, id string) (*commonmodel.CreateResponse, error) {
	tX, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	defer tX.Rollback()

	weightBridgeTicket, err := uc.weightBridgeRepo.GetWeightBridgeTicket(ctx, tX.DB(), models.WeightBridgeTicketCondition{
		Where: models.WeightBridgeTicketWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})

	if err != nil {
		return nil, err
	}

	weightBridgeTicket.ReferenceID = req.ReferenceID
	weightBridgeTicket.VehicleAssetID = req.VehicleAssetID
	weightBridgeTicket.LoadType = req.LoadType
	weightBridgeTicket.InboundAt = req.InboundAt
	weightBridgeTicket.InboundWeightKg = req.InboundWeightKg
	weightBridgeTicket.InboundWeightBridgeLocationID = req.InboundWeightBridgeLocationID
	weightBridgeTicket.InboundOperatorUserID = req.InboundOperatorUserID
	weightBridgeTicket.InboundOperatorUserFirstName = req.InboundOperatorUserFirstName
	weightBridgeTicket.InboundOperatorUserLastName = req.InboundOperatorUserLastName
	weightBridgeTicket.InboundLocationID = req.InboundLocationID
	weightBridgeTicket.OutboundAt = req.OutboundAt
	weightBridgeTicket.OutboundWeightKg = req.OutboundWeightKg
	weightBridgeTicket.OutboundWeightBridgeLocationID = req.OutboundWeightBridgeLocationID
	weightBridgeTicket.OutboundOperatorUserID = req.OutboundOperatorUserID
	weightBridgeTicket.OutboundOperatorUserFirstName = req.OutboundOperatorUserFirstName
	weightBridgeTicket.OutboundOperatorUserLastName = req.OutboundOperatorUserLastName
	weightBridgeTicket.OutboundLocationID = req.OutboundLocationID
	weightBridgeTicket.NetWeight = req.NetWeight
	weightBridgeTicket.Remark = req.Remark
	weightBridgeTicket.MaterialID = req.MaterialID
	weightBridgeTicket.TicketDate = req.OutboundAt

	if req.OutboundAt.Valid {
		shift, ticketDate, isSuccess := uc.GetShift(ctx, claim.GetLoggedInClientID(), req.OutboundAt.Time)
		if isSuccess {
			weightBridgeTicket.WorkShiftID = shift.ID
			weightBridgeTicket.WorkShiftAlias = shift.ShiftAlias
			weightBridgeTicket.WorkShiftName = shift.ShiftName
			weightBridgeTicket.ReconciliationDate = null.NewTime(ticketDate, true)
		}
	}

	if req.OutboundWeightKg > 94000 && !weightBridgeTicket.HasWorkOrder {
		if err := uc.CreateWeightBridgeTareWorkOrder(ctx, tX, req, weightBridgeTicket); err != nil {
			return nil, err
		}

		weightBridgeTicket.HasWorkOrder = true
	}

	err = uc.weightBridgeRepo.UpdateWeightBridgeTicket(ctx, tX.DB(), weightBridgeTicket)
	if err != nil {
		return nil, err
	}

	_, err = uc.storageUsecase.UpdateAttachmentPhotos(ctx, storageDtos.UpsertAttachmentReq{
		ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_WEIGHT_BRIDGE_TICKETS,
		SourceReferenceID: weightBridgeTicket.ID,
		ClientID:          claim.GetLoggedInClientID(),
		Photos:            req.Photos,
	})

	if err != nil {
		return nil, err
	}

	err = tX.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: weightBridgeTicket.ID,
		Data:        nil,
	}, nil
}

func (uc *WeightBridgeUseCase) DeleteWeightBridgeTicket(ctx context.Context, id string) (*commonmodel.CreateResponse, error) {

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	weightBridgeTicket, err := uc.weightBridgeRepo.GetWeightBridgeTicket(ctx, uc.DB.DB(), models.WeightBridgeTicketCondition{
		Where: models.WeightBridgeTicketWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})

	if err != nil {
		return nil, err
	}

	err = uc.weightBridgeRepo.DeleteWeightBridgeTicket(ctx, uc.DB.DB(), id)
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: weightBridgeTicket.ID,
		Data:        nil,
	}, nil
}

func (uc *WeightBridgeUseCase) GetWeightBridgeTicketInboundSummaries(ctx context.Context, req dtos.WeightBridgeTicketListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	count, tickets, err := uc.weightBridgeRepo.GetWeightBridgeTicketInboundSummaries(ctx, uc.DB.DB(), models.GetWeightBridgeTicketListParam{
		ListRequest: req.ListRequest,
		Cond: models.WeightBridgeTicketCondition{
			Where: models.WeightBridgeTicketWhere{
				ClientID:                claim.GetLoggedInClientID(),
				ReconciliationDateStart: req.ReconciliationDateStart,
				ReconciliationDateEnd:   req.ReconciliationDateEnd,
				TicketDateStart:         req.TicketDateStart,
				TicketDateEnd:           req.TicketDateEnd,
				WorkShiftID:             req.WorkShiftID,
				WorkShiftIDs:            req.WorkShiftIDs,
			},
			Preload: models.WeightBridgeTicketPreload{
				InboundLocation: true,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	respData := make([]dtos.WeightBridgeTicketInboundSummary, 0, len(tickets))

	for _, ticket := range tickets {
		weightBridgeTicket := dtos.WeightBridgeTicketInboundSummary{}
		weightBridgeTicket.TotalTrip = ticket.TotalTrip
		weightBridgeTicket.TotalWeight = ticket.TotalWeight
		weightBridgeTicket.AvgPayload = ticket.AvgPayload
		weightBridgeTicket.InboundLocationID = ticket.InboundLocationID
		weightBridgeTicket.InboundLocationLabel = ticket.InboundLocationName
		respData = append(respData, weightBridgeTicket)
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respData,
	}, nil
}

func (uc *WeightBridgeUseCase) GetWeightBridgeTicketOutboundSummaries(ctx context.Context, req dtos.WeightBridgeTicketListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	count, tickets, err := uc.weightBridgeRepo.GetWeightBridgeTicketOutboundSummaries(ctx, uc.DB.DB(), models.GetWeightBridgeTicketListParam{
		ListRequest: req.ListRequest,
		Cond: models.WeightBridgeTicketCondition{
			Where: models.WeightBridgeTicketWhere{
				ClientID:                claim.GetLoggedInClientID(),
				ReconciliationDateStart: req.ReconciliationDateStart,
				ReconciliationDateEnd:   req.ReconciliationDateEnd,
				TicketDateStart:         req.TicketDateStart,
				TicketDateEnd:           req.TicketDateEnd,
				WorkShiftID:             req.WorkShiftID,
				WorkShiftIDs:            req.WorkShiftIDs,
			},
			Preload: models.WeightBridgeTicketPreload{
				OutboundLocation: true,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	respData := make([]dtos.WeightBridgeTicketOutboundSummary, 0, len(tickets))

	for _, ticket := range tickets {
		weightBridgeTicket := dtos.WeightBridgeTicketOutboundSummary{}
		weightBridgeTicket.TotalTrip = ticket.TotalTrip
		weightBridgeTicket.TotalWeight = ticket.TotalWeight
		weightBridgeTicket.AvgPayload = ticket.AvgPayload
		weightBridgeTicket.OutboundLocationID = ticket.OutboundLocationID
		weightBridgeTicket.OutboundLocationLabel = ticket.OutboundLocationName
		respData = append(respData, weightBridgeTicket)
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respData,
	}, nil
}
