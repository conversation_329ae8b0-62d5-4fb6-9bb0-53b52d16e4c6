package usecase

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"
	financeConstants "assetfindr/internal/app/finance/constants"
	journalConstants "assetfindr/internal/app/finance/constants"
	journalDtos "assetfindr/internal/app/finance/dtos"
	financeUsecase "assetfindr/internal/app/finance/usecase"
	notifConstants "assetfindr/internal/app/notification/constants"
	notificationDtos "assetfindr/internal/app/notification/dtos"
	notificationUsecase "assetfindr/internal/app/notification/usecase"
	storageConstants "assetfindr/internal/app/storage/constants"
	storageDtos "assetfindr/internal/app/storage/dtos"
	storageUsecase "assetfindr/internal/app/storage/usecase"
	userIdentityModel "assetfindr/internal/app/user-identity/models"
	userIdentityModels "assetfindr/internal/app/user-identity/models"
	userIdentityRepository "assetfindr/internal/app/user-identity/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers"
	"assetfindr/pkg/common/helpers/authhelpers"
	"assetfindr/pkg/common/helpers/sqlhelpers"
	"assetfindr/pkg/common/helpers/tmplhelpers"
	"context"
	"database/sql"
	"fmt"
	"html/template"
	"time"

	"gopkg.in/guregu/null.v4"
)

type AssetTransactionUseCase struct {
	DB                   database.DBUsecase
	assetTransactionRepo repository.AssetTransactionRepository
	partnerRepo          userIdentityRepository.PartnerRepository
	userRepo             userIdentityRepository.UserRepository
	financeUsecase       financeUsecase.FinanceUseCase
	storageUsecase       *storageUsecase.AttachmentUseCase
	assetRepo            repository.AssetRepository
	assetTyreRepo        repository.AssetTyreRepository
	notifUseCase         *notificationUsecase.NotificationUseCase
}

func NewAssetTransactionUseCase(
	DB database.DBUsecase,
	assetTransactionRepo repository.AssetTransactionRepository,
	partnerRepo userIdentityRepository.PartnerRepository,
	userRepo userIdentityRepository.UserRepository,
	financeUsecase financeUsecase.FinanceUseCase,
	storageUsecase *storageUsecase.AttachmentUseCase,
	assetRepo repository.AssetRepository,
	assetTyreRepo repository.AssetTyreRepository,
	notifUseCase *notificationUsecase.NotificationUseCase,
) *AssetTransactionUseCase {
	return &AssetTransactionUseCase{
		DB:                   DB,
		assetTransactionRepo: assetTransactionRepo,
		partnerRepo:          partnerRepo,
		userRepo:             userRepo,
		financeUsecase:       financeUsecase,
		storageUsecase:       storageUsecase,
		assetRepo:            assetRepo,
		assetTyreRepo:        assetTyreRepo,
		notifUseCase:         notifUseCase,
	}
}

func (uc *AssetTransactionUseCase) constructAssetTransaction(req dtos.CreateUpdateAssetTransaction) (*models.AssetTransaction, error) {
	subTotal := 0
	isItemsPresent := false
	items := make([]models.AssetTransactionItem, 0, len(req.Items))
	for _, item := range req.Items {
		isItemsPresent = true
		subTotal += item.Quantity * int(item.UnitPrice)
		items = append(items, models.AssetTransactionItem{
			Name:      item.Name,
			UnitPrice: item.UnitPrice,
			Quantity:  item.Quantity,
		})
	}

	if isItemsPresent {
		req.Cost = subTotal + req.TaxCost + req.OtherCost - req.DiscountAmount
	}

	purchaseOrderDate := parseDate(req.PurchaseOrderDate)
	purchaseOrderDateNull := &null.Time{}

	if !purchaseOrderDate.IsZero() {
		purchaseOrderDateNull.Valid = true
		purchaseOrderDateNull.Time = purchaseOrderDate
	}

	invoiceDate := parseDate(req.InvoiceDate)
	if req.TypeCode == constants.ASSET_TRANSACTION_TYPE_EXPENSE {
		invoiceDate, _ = time.Parse(time.RFC3339, req.InvoiceDate)
	}

	partnerID := req.PartnerID
	if partnerID == "" {
		asset, err := uc.assetRepo.GetAssetByID(context.TODO(), uc.DB.DB(), req.AssetID)
		if err != nil {
			return nil, err
		}
		if asset != nil {
			partnerID = asset.PartnerOwnerID
		}
	}

	var expiryReminderDate *sql.NullTime
	if req.ExpiryReminderDate != "" {
		date := parseDate(req.ExpiryReminderDate)
		sqlDate := sqlhelpers.NullTime(date)
		if sqlDate.Valid {
			expiryReminderDate = &sqlDate
		}
	}
	isCreateWO := sqlhelpers.NullBool(req.IsCreateWorkOrderOnExpiry)

	assetTrasaction := &models.AssetTransaction{
		AssetID:               req.AssetID,
		PartnerID:             partnerID,
		PurchaseOrderDate:     purchaseOrderDateNull,
		PurchaseOrderNumber:   req.PurchaseOrderNumber,
		InvoiceDate:           invoiceDate,
		InvoiceNumber:         req.InvoiceNumber,
		ServiceStartDate:      parseDate(req.ServiceStartDate),
		ServiceEndDate:        parseDate(req.ServiceEndDate),
		ReferenceNumber:       req.ReferenceNumber,
		StatusCode:            req.StatusCode,
		TypeCode:              req.TypeCode,
		Cost:                  req.Cost,
		AssignedToUserID:      req.AssignedToUserID,
		TaxCost:               req.TaxCost,
		DiscountAmount:        req.DiscountAmount,
		OtherCost:             req.OtherCost,
		SubTotal:              req.SubTotal,
		Notes:                 req.Notes,
		AssetTransactionItems: items,
		CategoryCode:          req.CategoryCode,
		// PaymentMethodCode:     req.PaymentMethodCode,
		Location:                  req.Location,
		Odometer:                  req.Odometer,
		ExpiryReminderDate:        expiryReminderDate,
		IsCreateWorkOrderOnExpiry: &isCreateWO,
	}

	return assetTrasaction, nil
}

func (uc *AssetTransactionUseCase) constructUpdateAssetTransaction(req dtos.CreateUpdateAssetTransaction) (*models.AssetTransaction, error) {
	subTotal := 0
	isItemsPresent := false
	for _, item := range req.Items {
		isItemsPresent = true
		subTotal += item.Quantity * int(item.UnitPrice)
	}

	if isItemsPresent {
		req.Cost = subTotal + req.TaxCost + req.OtherCost - req.DiscountAmount
	}

	purchaseOrderDate := parseDate(req.PurchaseOrderDate)
	purchaseOrderDateNull := &null.Time{}

	if !purchaseOrderDate.IsZero() {
		purchaseOrderDateNull.Valid = true
		purchaseOrderDateNull.Time = purchaseOrderDate
	}

	var expiryReminderDate *sql.NullTime
	if req.ExpiryReminderDate != "" {
		date := parseDate(req.ExpiryReminderDate)
		sqlDate := sqlhelpers.NullTime(date)
		if sqlDate.Valid {
			expiryReminderDate = &sqlDate
		}
	}
	isCreateWO := sqlhelpers.NullBool(req.IsCreateWorkOrderOnExpiry)

	assetTransaction := &models.AssetTransaction{
		AssetID:             req.AssetID,
		PartnerID:           req.PartnerID,
		PurchaseOrderDate:   purchaseOrderDateNull,
		PurchaseOrderNumber: req.PurchaseOrderNumber,
		InvoiceDate:         parseDate(req.InvoiceDate),
		InvoiceNumber:       req.InvoiceNumber,
		ServiceStartDate:    parseDate(req.ServiceStartDate),
		ServiceEndDate:      parseDate(req.ServiceEndDate),
		ReferenceNumber:     req.ReferenceNumber,
		StatusCode:          req.StatusCode,
		TypeCode:            req.TypeCode,
		Cost:                req.Cost,
		Notes:               req.Notes,
		AssignedToUserID:    req.AssignedToUserID,
		CategoryCode:        req.CategoryCode,
		// PaymentMethodCode:   req.PaymentMethodCode,
		Location:                  req.Location,
		Odometer:                  req.Odometer,
		ExpiryReminderDate:        expiryReminderDate,
		IsCreateWorkOrderOnExpiry: &isCreateWO,
	}
	if req.TypeCode != "PURCHASE" {
		assetTransaction.InvoiceDate, _ = time.Parse(time.RFC3339, req.InvoiceDate)
	}

	return assetTransaction, nil
}

func (uc *AssetTransactionUseCase) validatePath(path string) (string, error) {
	typeCode := ""
	switch path {
	case "purchase":
		typeCode = constants.ASSET_TRANSACTION_TYPE_PURCHASE
	case "rental":
		typeCode = constants.ASSET_TRANSACTION_TYPE_RENT
	case "warranty":
		typeCode = constants.ASSET_TRANSACTION_TYPE_WARRANTY
	case "insurance":
		typeCode = constants.ASSET_TRANSACTION_TYPE_INSURANCE
	case "expense":
		typeCode = constants.ASSET_TRANSACTION_TYPE_EXPENSE
	default:
		return typeCode, errorhandler.ErrBadRequest("error route not found")
	}
	return typeCode, nil
}

func (uc *AssetTransactionUseCase) validateUser(ctx context.Context, req dtos.CreateUpdateAssetTransaction) error {
	user := userIdentityModels.User{}
	err := uc.userRepo.GetUserById(ctx, uc.DB.DB(), &user, req.AssignedToUserID)
	if err != nil {
		return errorhandler.ErrBadRequest("unknown user")
	}
	return nil
}
func (uc *AssetTransactionUseCase) validatePartner(ctx context.Context, req dtos.CreateUpdateAssetTransaction) error {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}
	_, err = uc.partnerRepo.GetPartner(ctx, uc.DB.DB(), userIdentityModels.PartnerCondition{
		Where: userIdentityModels.PartnerWhere{
			ID:       req.PartnerID,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return errorhandler.ErrBadRequest("unknown vendor")
	}
	return nil
}

func (uc *AssetTransactionUseCase) constructJournal(ctx context.Context, req models.AssetTransaction) (journalDtos.CreateJournalReq, error) {
	journal := journalDtos.CreateJournalReq{}
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return journal, err
	}

	journalReferences := []journalDtos.JournalReference{
		{
			ReferenceID: req.ID,
			SourceCode:  journalConstants.ACCOUNT_TYPE_CODE_ASSET,
		},
		{
			ReferenceID: req.AssetID,
			SourceCode:  journalConstants.ACCOUNT_TYPE_CODE_ASSET,
		},
	}
	asset, err := uc.assetRepo.GetAsset(ctx, uc.DB.DB(), models.AssetCondition{
		Where: models.AssetWhere{
			ID:       req.AssetID,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return journal, err
	}
	if asset.AssetCategoryCode == constants.ASSET_CATEGORY_TYRE_CODE {
		where := models.AssetTyreTreadWhere{
			AssetID: req.AssetID,
		}
		switch req.TypeCode {
		case constants.ASSET_TRANSACTION_TYPE_PURCHASE:
			where.Sequence = null.IntFrom(0)
		default:
			where.IsLastest = true
		}
		tread, err := uc.assetTyreRepo.GetAssetTyreTread(ctx, uc.DB.DB(), models.AssetTyreTreadCondition{
			Where: where,
		})
		if err == nil {
			journalReferences = append(journalReferences, journalDtos.JournalReference{
				ReferenceID: tread.ID,
				SourceCode:  financeConstants.JOURNAL_SOURCE_RETREAD_TYRE_CODE,
			})
		}
	}
	switch req.TypeCode {
	case constants.ASSET_TRANSACTION_TYPE_PURCHASE:
		journal.AccountTransactionType = journalConstants.ACCOUNT_TRANSACTION_TYPE_CREATE_ASSET_PURCHASE_COST
	case constants.ASSET_TRANSACTION_TYPE_RENT:
		journal.AccountTransactionType = journalConstants.ACCOUNT_TRANSACTION_TYPE_CREATE_ASSET_RENTAL_COST
	case constants.ASSET_TRANSACTION_TYPE_WARRANTY:
		journal.AccountTransactionType = journalConstants.ACCOUNT_TRANSACTION_TYPE_CREATE_ASSET_WARRANTY_COST
	case constants.ASSET_TRANSACTION_TYPE_INSURANCE:
		journal.AccountTransactionType = journalConstants.ACCOUNT_TRANSACTION_TYPE_CREATE_ASSET_INSURANCE_COST
	case constants.ASSET_TRANSACTION_TYPE_EXPENSE:
		journal.AccountTransactionType = journalConstants.ACCOUNT_TRANSACTION_TYPE_CREATE_ASSET_INSURANCE_COST
	default:
		return journal, errorhandler.ErrBadRequest("unknown transaction type")
	}
	journal.Amount = int64(req.Cost)
	journal.Notes = fmt.Sprintf("%s ASSET", req.TypeCode)
	journal.Date = time.Now()
	journal.ClientID = claim.GetLoggedInClientID()
	journal.UserID = claim.UserID
	journal.References = journalReferences
	return journal, nil
}

func (uc *AssetTransactionUseCase) CreateAssetTransaction(ctx context.Context, req dtos.CreateUpdateAssetTransaction) (*commonmodel.CreateResponse, error) {
	typeCode, err := uc.validatePath(req.TypeCode)
	if err != nil {
		return nil, err
	}
	req.TypeCode = typeCode

	if req.TypeCode != "PURCHASE" {
		err = uc.validatePartner(ctx, req)
		if err != nil {
			return nil, err
		}
	}
	assetTransaction, err := uc.constructAssetTransaction(req)
	if err != nil {
		return nil, err
	}
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}
	tX, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}
	defer tX.Rollback()
	err = uc.assetTransactionRepo.CreateAssetTransaction(ctx, tX.DB(), assetTransaction)
	if err != nil {
		return nil, err
	}
	journal, err := uc.constructJournal(ctx, *assetTransaction)
	if err != nil {
		return nil, err
	}
	err = uc.financeUsecase.CreateJournal(ctx, journal)
	if err != nil {
		return nil, err
	}
	_, err = uc.storageUsecase.CreateAttachmentsPhotosV2(ctx, storageDtos.UpsertAttachmentReq{
		ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_ASSET_TRANSACTION,
		SourceReferenceID: assetTransaction.ID,
		TargetReferenceID: assetTransaction.AssetID,
		ClientID:          claim.GetLoggedInClientID(),
		Photos:            req.Photos,
	})
	if err != nil {
		return nil, err
	}
	err = tX.Commit()
	if err != nil {
		return nil, err
	}

	if req.TypeCode == constants.ASSET_TRANSACTION_TYPE_WARRANTY || req.TypeCode == constants.ASSET_TRANSACTION_TYPE_INSURANCE {
		notifyReq := dtos.SendNearExpiryAssetTransactionNotificationsReq{
			Type: req.TypeCode,
		}
		uc.SendNearExpiryAssetTransactionNotifications(ctx, notifyReq, claim.GetLoggedInClientID())
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: assetTransaction.ID,
		Data:        nil,
	}, nil
}

func (uc *AssetTransactionUseCase) GetAssetTransaction(ctx context.Context, id string, path string) (*commonmodel.DetailResponse, error) {
	typeCode, err := uc.validatePath(path)
	if err != nil {
		return nil, err
	}
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}
	assetTransaction, err := uc.assetTransactionRepo.GetAssetTransaction(ctx, uc.DB.DB(), models.AssetTransactionCondition{
		Where: models.AssetTransactionWhere{
			ID:       id,
			Type:     typeCode,
			ClientID: claim.GetLoggedInClientID(),
		},
		Preload: models.AssetTransactionPreload{
			Status:                true,
			Type:                  true,
			AssetTransactionItems: true,
		},
	})
	if err != nil {
		return nil, err
	}
	user := userIdentityModels.User{}
	err = uc.userRepo.GetUserById(ctx, uc.DB.DB(), &user, assetTransaction.AssignedToUserID)
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return nil, err
	}
	partner, err := uc.partnerRepo.GetPartner(ctx, uc.DB.DB(), userIdentityModels.PartnerCondition{
		Where: userIdentityModels.PartnerWhere{
			ID:       assetTransaction.PartnerID,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	resp := dtos.ResponseAssetTransaction{
		ID:                     assetTransaction.ID,
		CreatedAt:              assetTransaction.CreatedAt,
		PartnerID:              assetTransaction.PartnerID,
		PartnerName:            partner.Name,
		PurchaseOrderDate:      assetTransaction.PurchaseOrderDate,
		PurchaseOrderNumber:    assetTransaction.PurchaseOrderNumber,
		InvoiceDate:            assetTransaction.InvoiceDate,
		InvoiceNumber:          assetTransaction.InvoiceNumber,
		StatusCode:             assetTransaction.StatusCode,
		Status:                 assetTransaction.Status,
		TypeCode:               assetTransaction.TypeCode,
		Type:                   assetTransaction.Type,
		Cost:                   assetTransaction.Cost,
		AssignedToUserID:       assetTransaction.AssignedToUserID,
		AssignedToUserFullname: user.GetName(),
		ServiceStartDate:       assetTransaction.ServiceStartDate,
		ServiceEndDate:         assetTransaction.ServiceEndDate,
		ReferenceNumber:        assetTransaction.ReferenceNumber,
		TaxCost:                assetTransaction.TaxCost,
		DiscountAmount:         assetTransaction.DiscountAmount,
		OtherCost:              assetTransaction.OtherCost,
		SubTotal:               assetTransaction.SubTotal,
		Notes:                  assetTransaction.Notes,
		Items:                  make([]dtos.AssetTransactionItemResp, 0, len(assetTransaction.AssetTransactionItems)),
		CategoryCode:           assetTransaction.CategoryCode,
		Location:               assetTransaction.Location,
		Odometer:               assetTransaction.Odometer,
		// PaymentMethodCode:      assetTransaction.PaymentMethodCode,
	}

	if assetTransaction.ExpiryReminderDate != nil && assetTransaction.ExpiryReminderDate.Valid {
		resp.ExpiryReminderDate = assetTransaction.ExpiryReminderDate.Time
	}
	if assetTransaction.IsCreateWorkOrderOnExpiry != nil && assetTransaction.IsCreateWorkOrderOnExpiry.Valid {
		resp.IsCreateWorkOrderOnExpiry = assetTransaction.IsCreateWorkOrderOnExpiry.Bool
	}

	for _, assetTransactionItem := range assetTransaction.AssetTransactionItems {
		resp.Items = append(resp.Items, dtos.AssetTransactionItemResp{
			ID:        assetTransactionItem.ID,
			Name:      assetTransactionItem.Name,
			UnitPrice: assetTransactionItem.UnitPrice,
			Quantity:  assetTransactionItem.Quantity,
		})
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        resp,
	}, nil

}

func (uc *AssetTransactionUseCase) GetAssetTransactions(ctx context.Context, path string, req dtos.AssetTransactionListReq) (*commonmodel.ListResponse, error) {
	typeCode, err := uc.validatePath(path)
	if err != nil {
		return nil, err
	}
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	preload := models.AssetTransactionPreload{
		Status: true,
		Type:   true,
	}
	if typeCode == "EXPENSE" {
		preload.Category = true
		// preload.PaymentMethod = true
	}
	count, assetTransactions, err := uc.assetTransactionRepo.GetAssetTransactionList(ctx, uc.DB.DB(), models.GetAssetTransactionListParam{
		ListRequest: req.ListRequest,
		Cond: models.AssetTransactionCondition{
			Where: models.AssetTransactionWhere{
				AssetID:  req.AssetID,
				Type:     typeCode,
				ClientID: claim.GetLoggedInClientID(),
			},
			Preload: preload,
		},
	})
	if err != nil {
		return nil, err
	}
	userIds := []string{}
	partnerIds := []string{}
	for _, val := range assetTransactions {
		userIds = append(userIds, val.AssignedToUserID)
		partnerIds = append(partnerIds, val.PartnerID)
	}
	users := []userIdentityModels.User{}
	err = uc.userRepo.GetUsersByIds(ctx, uc.DB.DB(), &users, userIds)
	if err != nil {
		return nil, err
	}
	mapsUser := map[string]userIdentityModels.User{}
	for _, val := range users {
		mapsUser[val.ID] = val
	}
	partners := []userIdentityModels.Partner{}
	err = uc.partnerRepo.GetPartnersByIds(ctx, uc.DB.DB(), &partners, partnerIds)
	if err != nil {
		return nil, err
	}
	mapsPartner := map[string]userIdentityModels.Partner{}
	for _, val := range partners {
		mapsPartner[val.ID] = val
	}
	response := []dtos.ResponseAssetTransaction{}
	for _, val := range assetTransactions {
		dto := dtos.ResponseAssetTransaction{}
		dto.Set(val, mapsUser[val.AssignedToUserID].GetName(), mapsPartner[val.PartnerID].Name)
		response = append(response, dto)
	}
	respData := response

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respData,
	}, nil

}

func (uc *AssetTransactionUseCase) UpdateAssetTransaction(ctx context.Context, id string, req dtos.CreateUpdateAssetTransaction) (*commonmodel.UpdateResponse, error) {
	typeCode, err := uc.validatePath(req.TypeCode)
	if err != nil {
		return nil, err
	}
	req.TypeCode = typeCode

	err = uc.validatePartner(ctx, req)
	if err != nil {
		return nil, err
	}

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	existingAssetTransaction, err := uc.assetTransactionRepo.GetAssetTransaction(ctx, uc.DB.DB(), models.AssetTransactionCondition{
		Where: models.AssetTransactionWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}
	assetTransaction, err := uc.constructUpdateAssetTransaction(req)
	if err != nil {
		return nil, err
	}
	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	deleteItemIDs := []string{}
	newItems := []models.AssetTransactionItem{}
	for _, item := range req.Items {
		switch {
		case item.IsNew():
			newItems = append(newItems, models.AssetTransactionItem{
				AssetTransactionID: id,
				Name:               item.Name,
				UnitPrice:          item.UnitPrice,
				Quantity:           item.Quantity,
			})
		case item.IsDelete:
			deleteItemIDs = append(deleteItemIDs, item.ID)
		default:
			err = uc.assetTransactionRepo.UpdateAssetTransactionItem(ctx, tx.DB(), item.ID,
				&models.AssetTransactionItem{
					Name:      item.Name,
					UnitPrice: item.UnitPrice,
					Quantity:  item.Quantity,
				},
			)
			if err != nil {
				return nil, err
			}
		}
	}

	err = uc.assetTransactionRepo.CreateAssetTransactionItems(ctx, tx.DB(), newItems)
	if err != nil {
		return nil, err
	}
	err = uc.assetTransactionRepo.DeleteAssetTransactionItemByIDs(ctx, tx.DB(), deleteItemIDs)
	if err != nil {
		return nil, err
	}

	err = uc.assetTransactionRepo.UpdateAssetTransaction(ctx, tx.DB(), id, assetTransaction)
	if err != nil {
		return nil, err
	}

	_, err = uc.storageUsecase.UpdateAttachmentPhotos(ctx, storageDtos.UpsertAttachmentReq{
		ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_ASSET_TRANSACTION,
		SourceReferenceID: id,
		TargetReferenceID: assetTransaction.AssetID,
		ClientID:          claim.GetLoggedInClientID(),
		Photos:            req.Photos,
	})
	if err != nil {
		return nil, err
	}
	if existingAssetTransaction.Cost != assetTransaction.Cost {
		err = uc.financeUsecase.UpdateCostByRefID(ctx, id, assetTransaction.Cost-existingAssetTransaction.Cost)
		if err != nil {
			commonlogger.Warnf("err update ref cost")
		}
	}
	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}, nil

}

func (uc *AssetTransactionUseCase) GetAssetTransactionCategories(ctx context.Context) (*commonmodel.DetailResponse, error) {
	atCategories, err := uc.assetTransactionRepo.GetAssetTransactionCategories(ctx, uc.DB.DB())
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        atCategories,
	}, nil

}

func (uc *AssetTransactionUseCase) GetAssetTransactionPaymentMethods(ctx context.Context) (*commonmodel.DetailResponse, error) {
	atPaymentMethods, err := uc.assetTransactionRepo.GetAssetTransactionPaymentMethods(ctx, uc.DB.DB())
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        atPaymentMethods,
	}, nil

}

func (uc *AssetTransactionUseCase) notifyNearExpiryAssetTransaction(ctx context.Context, assetTransactions []models.AssetTransaction, clientID string) {
	// CONSTRUCT ASSET IDS, USER IDS, AND PARTNER IDS
	assignedToUserIDs := []string{}
	assetIDs := []string{}
	partnerIDs := []string{}
	for _, assetTransaction := range assetTransactions {
		if assetTransaction.AssignedToUserID != "" {
			assignedToUserIDs = append(assignedToUserIDs, assetTransaction.AssignedToUserID)
		}
		assetIDs = append(assetIDs, assetTransaction.AssetID)
		partnerIDs = append(partnerIDs, assetTransaction.PartnerID)
	}

	// GET MAP OF USERS BY ID
	usersMapById := map[string]userIdentityModel.User{}
	err := uc.userRepo.GetUsersInMapByIds(ctx, uc.DB.DB(), &usersMapById, assignedToUserIDs)
	if err != nil {
		commonlogger.Warnf("error get users on notify near expired asset transaction", err)
		return
	}

	// GET CLIENT DATA
	client, err := uc.userRepo.GetClient(ctx, uc.DB.DB(), userIdentityModel.ClientCondition{
		Where: userIdentityModel.ClientWhere{
			ID: clientID,
		},
	})
	if err != nil {
		commonlogger.Warnf("failed to get client for email %v", err)
		return
	}

	// GET MAP OF ASSETS BY ID
	assetsMapById := map[string]models.Asset{}
	assets, err := uc.assetRepo.GetAssetsByIDs(ctx, uc.DB.DB(), assetIDs)
	if err != nil {
		commonlogger.Warnf("Error in getting assets by ids", err)
		return
	}
	for _, asset := range assets {
		assetsMapById[asset.ID] = asset
	}

	// GET MAP OF PARTNERS BY ID
	partners := []userIdentityModel.Partner{}
	partnersMapById := map[string]userIdentityModel.Partner{}
	err = uc.partnerRepo.GetPartnersByIds(ctx, uc.DB.DB(), &partners, partnerIDs)
	if err != nil {
		commonlogger.Warnf("error get partner on notify near expired asset transaction", err)
		return
	}
	for _, partner := range partners {
		partnersMapById[partner.ID] = partner
	}

	// LOOP TRX AND CONSTRUCT NOTIFICATION
	for _, assetTransaction := range assetTransactions {
		if assetTransaction.AssignedToUserID != "" {

			assetName := helpers.FormatDashedStringDisplay(assetsMapById[assetTransaction.AssetID].Name, assetsMapById[assetTransaction.AssetID].ReferenceNumber, assetsMapById[assetTransaction.AssetID].SerialNumber)

			href := uc.notifUseCase.GenerateTargetURL(ctx, client.ClientAlias, notifConstants.DESTINATION_TYPE_ASSET, assetTransaction.AssetID)

			subject := ""
			if assetTransaction.TypeCode == constants.ASSET_TRANSACTION_TYPE_WARRANTY {
				subject = fmt.Sprintf("Reminder: Warranty Expiration for %s", assetName)
			} else {
				subject = fmt.Sprintf("Reminder: Insurance Expiration for %s", assetName)
			}
			providerName := partnersMapById[assetTransaction.PartnerID].Name
			expirationDate := assetTransaction.ServiceEndDate.Format("02 January 2006")

			templateBod := tmplhelpers.NearExpiredAssetTransactionNotificationBody{
				Subject:         subject,
				AssetName:       assetName,
				TransactionType: assetTransaction.Type.Label,
				UserAssigned:    usersMapById[assetTransaction.AssignedToUserID].GetName(),
				RedirectLink:    template.URL(href),
				ProviderName:    providerName,
				ExpirationDate:  expirationDate,
			}
			err = tmplhelpers.ConstructNearExpiredAssetTransactionNotification(&templateBod)
			if err != nil {
				commonlogger.Warnf("error generate notify template body", err)
				return
			}

			notifItem := notificationDtos.CreateNotificationItem{
				SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_ASSET_TRANSACTION,
				SourceReferenceID: assetTransaction.ID,
				TargetReferenceID: assetTransaction.AssetID,
				TargetURL:         href,
				MessageHeader:     templateBod.TitleEmail,
				MessageBody:       templateBod.BodyEmail,
				MessageFirebase: notificationDtos.MessageFirebase{
					Title: templateBod.TitleFirebase,
					Body:  templateBod.BodyFirebase,
				},
				ClientID:        assetTransaction.ClientID,
				TypeCode:        "",
				ContentTypeCode: "",
				ReferenceCode:   notifConstants.NOTIF_REF_ASSET,
				ReferenceValue:  assetTransaction.AssetID,
				UserID:          assetTransaction.AssignedToUserID,
			}

			itemNotifications := []notificationDtos.CreateNotificationItem{}
			itemNotifications = append(itemNotifications, notifItem)

			uc.notifUseCase.CreateNotification(ctx, notificationDtos.CreateNotificationReq{
				Items:           itemNotifications,
				SendToEmail:     true,
				SendToPushNotif: true,
			})
		}
	}

}

func (uc *AssetTransactionUseCase) SendNearExpiryAssetTransactionNotifications(ctx context.Context, req dtos.SendNearExpiryAssetTransactionNotificationsReq, clientID string) (*commonmodel.DetailResponse, error) {
	notificationType := req.Type
	assetTransactions, _ := uc.assetTransactionRepo.GetAssetTransactions(ctx, uc.DB.DB(), models.GetAssetTransactionListParam{
		Cond: models.AssetTransactionCondition{
			Where: models.AssetTransactionWhere{
				Type:         notificationType,
				ClientID:     clientID,
				IsNearExpiry: true,
			},
			Preload: models.AssetTransactionPreload{
				Type: true,
			},
		},
	})

	uc.notifyNearExpiryAssetTransaction(ctx, assetTransactions, clientID)

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: notificationType,
		Data:        "",
	}, nil
}
