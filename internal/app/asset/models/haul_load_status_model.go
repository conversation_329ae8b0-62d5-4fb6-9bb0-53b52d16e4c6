package models

import (
	"assetfindr/pkg/common/helpers/authhelpers"
	"time"

	"gorm.io/gorm"
)

type HaulLoadStatus struct {
	AssetID   string    `json:"asset_id" gorm:"primaryKey;type:varchar(40)"`
	IsLoad    bool      `json:"is_load"`
	ClientID  string    `json:"client_id"`
	UpdatedAt time.Time `json:"updated_at"`
	UpdatedBy string    `json:"updated_by"`
}

func (hls *HaulLoadStatus) TableName() string {
	return "ams_haul_load_statuses"
}

func (hls *HaulLoadStatus) BeforeCreate(tx *gorm.DB) error {
	claim, err := authhelpers.GetClaimFromCtx(tx.Statement.Context)
	if err != nil {
		return nil
	}

	if hls.ClientID == "" {
		hls.ClientID = claim.GetLoggedInClientID()
	}

	if hls.UpdatedBy == "" {
		hls.UpdatedBy = claim.UserID
	}

	return nil
}

type HaulLoadStatusWhere struct {
	AssetID  string
	ClientID string
}

type HaulLoadStatusCondition struct {
	Where HaulLoadStatusWhere
}
