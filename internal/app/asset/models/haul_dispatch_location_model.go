package models

import (
	"assetfindr/pkg/common/commonmodel"

	"gopkg.in/guregu/null.v4"
)

type HaulDispatchLocationType struct {
	Code        string `json:"code" gorm:"primaryKey"`
	Label       string `json:"label"`
	Description string `json:"description"`
}

func (hdt *HaulDispatchLocationType) TableName() string {
	return "ams_HAUL_DISPATCH_LOCATION_TYPES"
}

type HaulDispatchLocation struct {
	commonmodel.ModelV2NoIDField
	AssetID                  string      `json:"asset_id" gorm:"primaryKey"`
	LoadLocationID           null.String `json:"load_location_id"`
	DumpLocationID           null.String `json:"dump_location_id"`
	DispatchLocationTypeCode string      `json:"dispatch_location_type_code"`

	// Relationships
	LoadLocation         *Location                `json:"load_location" gorm:"foreignKey:LoadLocationID;references:ID"`
	DumpLocation         *Location                `json:"dump_location" gorm:"foreignKey:DumpLocationID;references:ID"`
	DispatchLocationType HaulDispatchLocationType `json:"dispatch_location_type" gorm:"foreignKey:DispatchLocationTypeCode;references:Code"`
}

func (a *HaulDispatchLocation) TableName() string {
	return "ams_haul_dispatch_locations"
}

type HaulDispatchLocationWhere struct {
	AssetID  string
	ClientID string
}

type HaulDispatchLocationPreload struct {
	LoadLocation         bool
	DumpLocation         bool
	DispatchLocationType bool
}

type HaulDispatchLocationCondition struct {
	Where   HaulDispatchLocationWhere
	Preload HaulDispatchLocationPreload
}
