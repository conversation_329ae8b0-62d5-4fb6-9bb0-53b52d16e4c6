package repository

import (
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/infrastructure/database"
	"context"
)

type AssetTransactionRepository interface {
	CreateAssetTransaction(ctx context.Context, dB database.DBI, assetTransaction *models.AssetTransaction) error
	GetAssetTransactionList(ctx context.Context, dB database.DBI, param models.GetAssetTransactionListParam) (int, []models.AssetTransaction, error)
	GetAssetTransaction(ctx context.Context, dB database.DBI, cond models.AssetTransactionCondition) (*models.AssetTransaction, error)
	UpdateAssetTransaction(ctx context.Context, dB database.DBI, id string, assetTransaction *models.AssetTransaction) error
	UpdateAssetTransactionItem(ctx context.Context, dB database.DBI, id string, assetTransactionItem *models.AssetTransactionItem) error
	DeleteAssetTransactionItemByIDs(ctx context.Context, dB database.DBI, ids []string) error
	CreateAssetTransactionItems(ctx context.Context, dB database.DBI, assetTransactions []models.AssetTransactionItem) error
	GetAssetTransactions(ctx context.Context, dB database.DBI, param models.GetAssetTransactionListParam) ([]models.AssetTransaction, error)

	GetAssetTransactionCategories(ctx context.Context, dB database.DBI) ([]models.AssetTransactionCategory, error)
	GetAssetTransactionPaymentMethods(ctx context.Context, dB database.DBI) ([]models.AssetTransactionMethod, error)

	GetAssetWithTransactionExpiryReminderToday(ctx context.Context, dB database.DBI, where models.AssetTransactionWhere) ([]models.AssetTransaction, error)
}
