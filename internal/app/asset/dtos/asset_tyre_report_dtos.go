package dtos

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/internal/app/asset/models"
	financeDtos "assetfindr/internal/app/finance/dtos"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers"
	"assetfindr/pkg/common/helpers/calculationhelpers"
	"assetfindr/pkg/common/helpers/exceltmpl"
	"time"

	"gopkg.in/guregu/null.v4"
)

type GetAssetTyreStockReportReq struct {
	commonmodel.ListRequest
	BrandIDs                   []string `form:"brand_ids"`
	TyreSizes                  []string `form:"tyre_sizes"`
	PatternTypes               []string `form:"pattern_type"`
	UtilizationRateStatusCodes []string `form:"tur_codes"`
}

type GetAssetTyreStockReportResp struct {
	StatusCode                string      `json:"status_code"`
	StatusLabel               string      `json:"status_label"`
	LastStockDate             time.Time   `json:"last_stock_date"`
	TyreSize                  string      `json:"tyre_size"`
	PlyRating                 float64     `json:"ply_rating"`
	BrandName                 string      `json:"brand_name"`
	PatternType               string      `json:"pattern_type"`
	DateCode                  null.String `json:"date_code"`
	SerialNumber              string      `json:"serial_number"`
	Rfid                      string      `json:"rfid"`
	StartThreadDepth          null.Float  `json:"start_thread_depth"`
	OriginalTd                null.Float  `json:"original_td"`
	AverageRTD                null.Float  `json:"average_rtd"`
	UtilizationRatePercentage float64     `json:"utilization_rate_percentage"`
	UtilizationRateStatusCode string      `json:"utilization_rate_status_code"`
	MeterCalculationCode      null.String `json:"meter_calculation_code"`
	TotalKM                   int         `json:"total_km"`
	TotalHM                   float64     `json:"total_hm"`
	ProjectedLifeKm           int         `json:"projected_life_km"`
	ProjectedLifeHm           float64     `json:"projected_life_hm"`
	TreadWearRateKm           float64     `json:"tread_wear_rate_km"`
	TreadWearRateHm           float64     `json:"tread_wear_rate_hm"`
	Cost                      float64     `json:"cost"`
	CPK                       float64     `json:"cpk"`
	CPH                       float64     `json:"cph"`
	RemainingLifeKm           int         `json:"remaining_life_km"`
	RemainingLifeHm           float64     `json:"remaining_life_hm"`
}

func BuildGetAssetTyreStockReportResp(assetTyre models.AssetTyre, mapRefAmounts map[string]financeDtos.ReferenceTotalAmount) GetAssetTyreStockReportResp {
	treadCost := mapRefAmounts[assetTyre.LatestTread().ID].Amount
	runningTreadCost := helpers.CalculateTyreTreadRunningCost(assetTyre.OriginalTd, assetTyre.StartThreadDepth, assetTyre.AverageRTD, treadCost)
	runningCost := float64(mapRefAmounts[assetTyre.AssetID].Amount-treadCost) + runningTreadCost

	lifetimeKM := assetTyre.SumLifeTimeKM()
	lifetimeHM := assetTyre.SumLifeTimeHM()

	if assetTyre.PrevTotalKM.Valid {
		lifetimeKM = max(lifetimeKM-int(assetTyre.PrevTotalKM.Int64), 0)
	}

	if assetTyre.PrevTotalHm.Valid {
		lifetimeHM = max(lifetimeHM-calculationhelpers.Div100(int(assetTyre.PrevTotalHm.Int64)), 0)
	}

	cpk := 0.0
	if lifetimeKM > 0 {
		cpk = calculationhelpers.RoundToTwoDecimals(runningCost / float64(lifetimeKM))
	}
	cph := 0.0
	if lifetimeHM > 0 {
		cph = calculationhelpers.RoundToTwoDecimals(runningCost / lifetimeHM)
	}

	projectedLifeKm := assetTyre.LifetimeProjectedLifeKM()
	projectedLifeHm := assetTyre.LifetimeProjectedLifeHm()

	totalKM := assetTyre.SumLifeTimeKM()
	totalHM := assetTyre.SumLifeTimeHM()

	item := GetAssetTyreStockReportResp{
		StatusCode:                assetTyre.Asset.AssetStatusCode,
		StatusLabel:               assetTyre.Asset.AssetStatus.Label,
		LastStockDate:             assetTyre.LastStockDate,
		TyreSize:                  assetTyre.Tyre.GetTyreSize(),
		PlyRating:                 assetTyre.Tyre.PlyRating,
		BrandName:                 assetTyre.Asset.Brand.BrandName,
		PatternType:               assetTyre.Tyre.PatternType,
		DateCode:                  assetTyre.DateCode,
		SerialNumber:              assetTyre.Asset.SerialNumber,
		Rfid:                      assetTyre.Asset.Rfid,
		StartThreadDepth:          null.FloatFrom(assetTyre.StartThreadDepth),
		OriginalTd:                null.FloatFrom(assetTyre.OriginalTd),
		AverageRTD:                null.FloatFrom(assetTyre.AverageRTD),
		UtilizationRatePercentage: assetTyre.UtilizationRatePercentage,
		UtilizationRateStatusCode: assetTyre.UtilizationRatePercentageStatusCode,
		MeterCalculationCode:      assetTyre.MeterCalculationCode,
		TotalKM:                   totalKM,
		TotalHM:                   totalHM,
		ProjectedLifeKm:           projectedLifeKm,
		ProjectedLifeHm:           projectedLifeHm,
		Cost:                      float64(mapRefAmounts[assetTyre.AssetID].Amount),
		TreadWearRateKm:           assetTyre.TreadWearRateKm(),
		TreadWearRateHm:           assetTyre.TreadWearRateHm(),
		CPK:                       cpk,
		CPH:                       cph,
	}

	if projectedLifeKm > 0 {
		item.RemainingLifeKm = projectedLifeKm - totalKM
	}

	if projectedLifeHm > 0 {
		item.RemainingLifeHm = projectedLifeHm - totalHM
	}

	if assetTyre.HasNotSetRTD.Bool {
		item.AverageRTD = null.Float{}

		if assetTyre.PrevKmHmDataUnavailable.Bool {
			item.StartThreadDepth = null.Float{}
		}
	}

	return item
}

func ConvertGetAssetTyreStockReportRespListToExport(resp []GetAssetTyreStockReportResp, userName, clientName string, clientLogo []byte, logoExt string) exceltmpl.ReportTempl {
	templ := exceltmpl.ReportTempl{
		Title:         "Tyre Stock Report",
		SheetName:     "Tyre Stock Report",
		Logo:          clientLogo,
		LogoExtension: logoExt,
		DownloadBy:    userName,
		ClientName:    clientName,
		Description:   "This report is to show the current condition of tyre on hand available for use",
		Columns: []exceltmpl.ReportColumn{
			{Name: "No"},                 // 0
			{Name: "Status"},             // 1
			{Name: "Stock Date"},         // 2
			{Name: "Tyre Size"},          // 3
			{Name: "Ply Rating"},         // 4
			{Name: "Brand"},              // 5
			{Name: "Pattern / Type"},     // 6
			{Name: "Date Code"},          // 7
			{Name: "Tyre Serial Number"}, // 8
			{Name: "RFID / Other ID", Style: exceltmpl.TableValExcelizeStyle}, // 9
			{Name: "OTD (mm)"},         // 10
			{Name: "STD (mm)"},         // 11
			{Name: "Average RTD (mm)"}, // 12
			{Name: "TUR%", Style: exceltmpl.TableFloatNumberValueStyle},                    // 13
			{Name: "Total KM", Style: exceltmpl.TableFloatNumberValueStyle},                // 14
			{Name: "Total HM", Style: exceltmpl.TableFloatNumberValueStyle},                // 15
			{Name: "Remaining Life KM", Style: exceltmpl.TableFloatNumberValueStyle},       // 16
			{Name: "Remaining Life HM", Style: exceltmpl.TableFloatNumberValueStyle},       // 17
			{Name: "Projected Life KM", Style: exceltmpl.TableFloatNumberValueStyle},       // 18
			{Name: "Projected Life HM", Style: exceltmpl.TableFloatNumberValueStyle},       // 19
			{Name: "Tread Wear Rate (km/mm)", Style: exceltmpl.TableFloatNumberValueStyle}, // 20
			{Name: "Tread Wear Rate (h/mm)", Style: exceltmpl.TableFloatNumberValueStyle},  // 21
			{Name: "Tyre Lifetime Cost", Style: exceltmpl.TableFloatNumberValueStyle},      // 22
			{Name: "CPK", Style: exceltmpl.TableFloatNumberValueStyle},                     // 23
			{Name: "CPH", Style: exceltmpl.TableFloatNumberValueStyle},                     // 24
		},
		Rows: make([][]any, len(resp)),
	}

	for i, item := range resp {
		templ.Rows[i] = []any{
			i + 1,            // 0
			item.StatusLabel, // 1
			item.LastStockDate.Local().Format("02 Jan 2006"), // 2
			item.TyreSize,                  // 3
			item.PlyRating,                 // 4
			item.BrandName,                 // 5
			item.PatternType,               // 6
			item.DateCode.String,           // 7
			item.SerialNumber,              // 8
			item.Rfid,                      // 9
			item.OriginalTd.Float64,        // 10
			item.StartThreadDepth.Float64,  // 11
			item.AverageRTD.Float64,        // 12
			item.UtilizationRatePercentage, // 13
			item.TotalKM,                   // 14
			item.TotalHM,                   // 15
			item.RemainingLifeKm,           // 16
			item.RemainingLifeHm,           // 17
			item.ProjectedLifeKm,           // 18
			item.ProjectedLifeHm,           // 19
			item.TreadWearRateKm,           // 20
			item.TreadWearRateHm,           // 21
			item.Cost,                      // 22
			item.CPK,                       // 23
			item.CPH,                       // 24
		}

		if !item.OriginalTd.Valid {
			templ.Rows[i][10] = nil
		}

		if !item.StartThreadDepth.Valid {
			templ.Rows[i][11] = nil
		}

		if !item.AverageRTD.Valid {
			templ.Rows[i][12] = nil
		}

		isKM := item.MeterCalculationCode.String == constants.TYRE_METER_CALCULATION_CODE_KM
		isHM := item.MeterCalculationCode.String == constants.TYRE_METER_CALCULATION_CODE_HM
		isKMHM := item.MeterCalculationCode.String == constants.TYRE_METER_CALCULATION_CODE_KM_HM

		if !isKMHM {
			if isKM {
				templ.Rows[i][15] = nil
				templ.Rows[i][17] = nil
				templ.Rows[i][19] = nil
				templ.Rows[i][21] = nil
				templ.Rows[i][24] = nil
			} else if isHM {
				templ.Rows[i][14] = nil
				templ.Rows[i][16] = nil
				templ.Rows[i][18] = nil
				templ.Rows[i][20] = nil
				templ.Rows[i][23] = nil
			}
		}

	}

	return templ
}

type GetAssetTyreInstalledReportReq struct {
	commonmodel.ListRequest
	ParentAssetIDs             []string `form:"parent_asset_ids"`
	BrandIDs                   []string `form:"brand_ids"`
	TyreSizes                  []string `form:"tyre_sizes"`
	PatternTypes               []string `form:"pattern_type"`
	UtilizationRateStatusCodes []string `form:"tur_codes"`
}

type GetAssetTyreInstalledReportResp struct {
	ParentAssetID                       string      `json:"parent_asset_id"`
	ParentAssetReferenceNumber          string      `json:"parent_asset_reference_number"`
	PartnerOwnerName                    string      `json:"partner_owner_name"`
	ParentAssetSubcategoryName          string      `json:"parent_asset_subcategory_name"`
	TyrePosition                        int         `json:"tyre_position"`
	LinkedDatetime                      time.Time   `json:"linked_datetime"`
	SerialNumber                        string      `json:"serial_number"`
	Rfid                                string      `json:"rfid"`
	Brand                               string      `json:"brand"`
	PatternType                         string      `json:"pattern_type"`
	TyreSize                            string      `json:"tyre_size"`
	PlyRating                           float64     `json:"ply_rating"`
	DateCode                            null.String `json:"date_code"`
	StartThreadDepth                    null.Float  `json:"start_thread_depth"`
	OriginalTd                          null.Float  `json:"original_td"`
	AverageRtd                          null.Float  `json:"average_rtd"`
	UtilizationRatePercentage           float64     `json:"utilization_rate_percentage"`
	UtilizationRatePercentageStatusCode string      `json:"utilization_rate_percentage_status_code"`
	TotalKM                             int         `json:"total_km"`
	TotalHM                             float64     `json:"total_hm"`
	MeterCalculationCode                null.String `json:"meter_calculation_code"`
	LastInspectedAt                     null.Time   `json:"last_inspected_at"`
	ProjectedLifeKm                     int         `json:"projected_life_km"`
	ProjectedLifeHm                     float64     `json:"projected_life_hm"`
	TreadWearRateKm                     float64     `json:"tread_wear_rate_km"`
	TreadWearRateHm                     float64     `json:"tread_wear_rate_hm"`
	Cost                                float64     `json:"cost"`
	CPK                                 float64     `json:"cpk"`
	CPH                                 float64     `json:"cph"`
	RemainingLifeKm                     int         `json:"remaining_life_km"`
	RemainingLifeHm                     float64     `json:"remaining_life_hm"`
}

func BuildGetAssetTyreInstalledReportResp(assetTyre models.AssetTyre, mapRefAmounts map[string]financeDtos.ReferenceTotalAmount) GetAssetTyreInstalledReportResp {
	treadCost := mapRefAmounts[assetTyre.LatestTread().ID].Amount
	runningTreadCost := helpers.CalculateTyreTreadRunningCost(assetTyre.OriginalTd, assetTyre.StartThreadDepth, assetTyre.AverageRTD, treadCost)
	runningCost := float64(mapRefAmounts[assetTyre.AssetID].Amount-treadCost) + runningTreadCost

	lifetimeKM := assetTyre.SumLifeTimeKM()
	lifetimeHM := assetTyre.SumLifeTimeHM()

	if assetTyre.PrevTotalKM.Valid {
		lifetimeKM = max(lifetimeKM-int(assetTyre.PrevTotalKM.Int64), 0)
	}

	if assetTyre.PrevTotalHm.Valid {
		lifetimeHM = max(lifetimeHM-calculationhelpers.Div100(int(assetTyre.PrevTotalHm.Int64)), 0)
	}

	cpk := 0.0
	if lifetimeKM > 0 {
		cpk = calculationhelpers.RoundToTwoDecimals(runningCost / float64(lifetimeKM))
	}
	cph := 0.0
	if lifetimeHM > 0 {
		cph = calculationhelpers.RoundToTwoDecimals(runningCost / lifetimeHM)

	}

	lastInspectedAt := assetTyre.AverageRtdLastUpdatedAt
	if assetTyre.PressureLastUpdatedAt.Valid && (!assetTyre.AverageRtdLastUpdatedAt.Valid || assetTyre.PressureLastUpdatedAt.Time.After(lastInspectedAt.Time)) {
		lastInspectedAt = assetTyre.PressureLastUpdatedAt
	}

	if assetTyre.PressureLastUpdatedAt.Valid && (!assetTyre.AverageRtdLastUpdatedAt.Valid || assetTyre.TemperatureLastUpdatedAt.Time.After(lastInspectedAt.Time)) {
		lastInspectedAt = assetTyre.TemperatureLastUpdatedAt
	}

	projectedLifeKm := assetTyre.LifetimeProjectedLifeKM()
	projectedLifeHm := assetTyre.LifetimeProjectedLifeHm()

	item := GetAssetTyreInstalledReportResp{
		// ParentAssetID:                       assetTyre.AssetLinked.ParentAssetID,
		// ParentAssetReferenceNumber:          assetTyre.AssetLinked.AssetParent.ReferenceNumber,
		// PartnerOwnerName:                    assetTyre.AssetLinked.AssetParent.PartnerOwnerName,
		// ParentAssetSubcategoryName:          assetTyre.AssetLinked.AssetParent.CustomAssetSubCategory.Name,
		// TyrePosition:                        assetTyre.AssetLinked.AssetLinkedAssetVehicleTyre.TyrePosition,
		// LinkedDatetime:                      assetTyre.AssetLinked.LinkedDatetime,
		SerialNumber:                        assetTyre.Asset.SerialNumber,
		Rfid:                                assetTyre.Asset.Rfid,
		Brand:                               assetTyre.Asset.Brand.BrandName,
		PatternType:                         assetTyre.Tyre.PatternType,
		TyreSize:                            assetTyre.Tyre.GetTyreSize(),
		PlyRating:                           assetTyre.Tyre.PlyRating,
		DateCode:                            assetTyre.DateCode,
		StartThreadDepth:                    null.FloatFrom(assetTyre.StartThreadDepth),
		OriginalTd:                          null.FloatFrom(assetTyre.OriginalTd),
		AverageRtd:                          null.FloatFrom(assetTyre.AverageRTD),
		UtilizationRatePercentage:           assetTyre.UtilizationRatePercentage,
		UtilizationRatePercentageStatusCode: assetTyre.UtilizationRatePercentageStatusCode,
		MeterCalculationCode:                assetTyre.MeterCalculationCode,
		TotalKM:                             lifetimeKM,
		TotalHM:                             lifetimeHM,
		ProjectedLifeKm:                     projectedLifeKm,
		ProjectedLifeHm:                     projectedLifeHm,
		Cost:                                float64(mapRefAmounts[assetTyre.AssetID].Amount),
		TreadWearRateKm:                     assetTyre.TreadWearRateKm(),
		TreadWearRateHm:                     assetTyre.TreadWearRateHm(),
		CPK:                                 cpk,
		CPH:                                 cph,
		LastInspectedAt:                     lastInspectedAt,
	}

	if assetTyre.AssetLinked != nil {
		item.ParentAssetID = assetTyre.AssetLinked.ParentAssetID
		item.ParentAssetReferenceNumber = assetTyre.AssetLinked.AssetParent.ReferenceNumber
		item.PartnerOwnerName = assetTyre.AssetLinked.AssetParent.PartnerOwnerName
		item.ParentAssetSubcategoryName = assetTyre.AssetLinked.AssetParent.CustomAssetSubCategory.Name
		item.LinkedDatetime = assetTyre.AssetLinked.LinkedDatetime
		if assetTyre.AssetLinked.AssetLinkedAssetVehicleTyre != nil {
			item.TyrePosition = assetTyre.AssetLinked.AssetLinkedAssetVehicleTyre.TyrePosition
		}
	}

	if projectedLifeKm > 0 {
		item.RemainingLifeKm = projectedLifeKm - lifetimeKM
	}

	if projectedLifeHm > 0 {
		item.RemainingLifeHm = projectedLifeHm - lifetimeHM
	}

	if assetTyre.HasNotSetRTD.Bool {
		item.AverageRtd = null.Float{}

		if assetTyre.PrevKmHmDataUnavailable.Bool {
			item.StartThreadDepth = null.Float{}
		}
	}

	return item
}

func ConvertGetAssetTyreInstalledReportRespListToExport(resp []GetAssetTyreInstalledReportResp, userName, clientName string, clientLogo []byte, logoExt string, hideCustomer bool) exceltmpl.ReportTempl {
	templ := exceltmpl.ReportTempl{
		Title:         "Tyre Installed Report",
		SheetName:     "Tyre Installed Report",
		Logo:          clientLogo,
		LogoExtension: logoExt,
		DownloadBy:    userName,
		ClientName:    clientName,
		Description:   "This report is to show the current condition of tyre installed on vehicle",
		Columns: []exceltmpl.ReportColumn{
			{Name: "No"},                 // 0
			{Name: "Asset ID"},           // 1
			{Name: "Customer Name"},      // 2
			{Name: "Asset Subcategory"},  // 3
			{Name: "Tyre Position"},      // 4
			{Name: "Date Installed"},     // 5
			{Name: "Tyre Serial Number"}, // 6
			{Name: "RFID / Other ID"},    // 7
			{Name: "Brand"},              // 8
			{Name: "Pattern / Type"},     // 9
			{Name: "Tyre Size"},          // 10
			{Name: "Ply Rating "},        // 11
			{Name: "Date Code "},         // 12
			{Name: "OTD (mm)", Style: exceltmpl.TableFloatNumberValueStyle},                // 13
			{Name: "STD (mm)", Style: exceltmpl.TableFloatNumberValueStyle},                // 14
			{Name: "Average RTD (mm)", Style: exceltmpl.TableFloatNumberValueStyle},        // 15
			{Name: "TUR%", Style: exceltmpl.TableFloatNumberValueStyle},                    // 16
			{Name: "Total KM", Style: exceltmpl.TableFloatNumberValueStyle},                // 17
			{Name: "Total HM", Style: exceltmpl.TableFloatNumberValueStyle},                // 18
			{Name: "Remaining Life KM", Style: exceltmpl.TableFloatNumberValueStyle},       // 19
			{Name: "Remaining Life HM", Style: exceltmpl.TableFloatNumberValueStyle},       // 20
			{Name: "Projected Life KM", Style: exceltmpl.TableFloatNumberValueStyle},       // 21
			{Name: "Projected Life HM", Style: exceltmpl.TableFloatNumberValueStyle},       // 22
			{Name: "Tread Wear Rate (km/mm)", Style: exceltmpl.TableFloatNumberValueStyle}, // 23
			{Name: "Tread Wear Rate (h/mm)", Style: exceltmpl.TableFloatNumberValueStyle},  // 24
			{Name: "Date of last inspection"},                                              // 25
			{Name: "Tyre Lifetime Cost", Style: exceltmpl.TableFloatNumberValueStyle},      // 26
			{Name: "CPK", Style: exceltmpl.TableFloatNumberValueStyle},                     // 27
			{Name: "CPH", Style: exceltmpl.TableFloatNumberValueStyle},                     // 28
		},
		Rows: make([][]any, len(resp)),
	}

	if hideCustomer {
		templ.Columns = append(templ.Columns[:2], templ.Columns[3:]...)
	}

	for i, item := range resp {
		templ.Rows[i] = []any{
			i + 1,                           // 0
			item.ParentAssetReferenceNumber, // 1
			item.PartnerOwnerName,           // 2
			item.ParentAssetSubcategoryName, // 3
			item.TyrePosition,               // 4
			item.LinkedDatetime.Local().Format("02 Jan 2006"), // 5
			item.SerialNumber,              // 6
			item.Rfid,                      // 7
			item.Brand,                     // 8
			item.PatternType,               // 9
			item.TyreSize,                  // 10
			item.PlyRating,                 // 11
			item.DateCode.String,           // 12
			item.OriginalTd.Float64,        // 13
			item.StartThreadDepth.Float64,  // 14
			item.AverageRtd.Float64,        // 15
			item.UtilizationRatePercentage, // 16
			item.TotalKM,                   // 17
			item.TotalHM,                   // 18
			item.RemainingLifeKm,           // 19
			item.RemainingLifeHm,           // 20
			item.ProjectedLifeKm,           // 21
			item.ProjectedLifeHm,           // 22
			item.TreadWearRateKm,           // 23
			item.TreadWearRateHm,           // 24
			nil,                            // 25
			item.Cost,                      // 26
			item.CPK,                       // 27
			item.CPH,                       // 28
		}

		if hideCustomer {
			// Remove Customer Name column (index 2)
			templ.Rows[i] = append(templ.Rows[i][:2], templ.Rows[i][3:]...)
		}

		otdIdx := 13
		stdIdx := 14
		avgRtdIdx := 15
		lastInspectionIdx := 25

		totalHMIdx := 18
		remainingLifeHMIdx := 20
		projectedLifeHMIdx := 22
		treadWearRateHMIdx := 24
		cphIdx := 28

		totalKMIdx := 17
		remainingLifeKMIdx := 19
		projectedLifeKMIdx := 21
		treadWearRateKMIdx := 23
		cpkIdx := 27

		if hideCustomer {
			otdIdx = 12
			stdIdx = 13
			avgRtdIdx = 14
			lastInspectionIdx = 24

			totalHMIdx = 17
			remainingLifeHMIdx = 19
			projectedLifeHMIdx = 21
			treadWearRateHMIdx = 23
			cphIdx = 26

			totalKMIdx = 16
			remainingLifeKMIdx = 18
			projectedLifeKMIdx = 20
			treadWearRateKMIdx = 22
			cpkIdx = 26
		}

		if !item.OriginalTd.Valid {
			templ.Rows[i][otdIdx] = nil
		}

		if !item.StartThreadDepth.Valid {
			templ.Rows[i][stdIdx] = nil
		}

		if !item.AverageRtd.Valid {
			templ.Rows[i][avgRtdIdx] = nil
		}

		if item.LastInspectedAt.Valid {
			templ.Rows[i][lastInspectionIdx] = item.LastInspectedAt.Time.Local().Format("02 Jan 2006")
		}

		isKM := item.MeterCalculationCode.String == constants.TYRE_METER_CALCULATION_CODE_KM
		isHM := item.MeterCalculationCode.String == constants.TYRE_METER_CALCULATION_CODE_HM
		isKMHM := item.MeterCalculationCode.String == constants.TYRE_METER_CALCULATION_CODE_KM_HM

		if !isKMHM {
			if isKM {
				templ.Rows[i][totalHMIdx] = nil
				templ.Rows[i][remainingLifeHMIdx] = nil
				templ.Rows[i][projectedLifeHMIdx] = nil
				templ.Rows[i][treadWearRateHMIdx] = nil
				templ.Rows[i][cphIdx] = nil
			} else if isHM {
				templ.Rows[i][totalKMIdx] = nil
				templ.Rows[i][remainingLifeKMIdx] = nil
				templ.Rows[i][projectedLifeKMIdx] = nil
				templ.Rows[i][treadWearRateKMIdx] = nil
				templ.Rows[i][cpkIdx] = nil
			}
		}

	}

	return templ

}

type GetAssetTyreUninnstalledReportReq struct {
	commonmodel.ListRequest
	AssetStatusCodes          []string `form:"asset_status_codes"`
	BrandIDs                  []string `form:"brand_ids"`
	TyreSizes                 []string `form:"tyre_sizes"`
	PatternTypes              []string `form:"pattern_type"`
	CustomAssetSubCategoryIDs []string `form:"custom_asset_sub_category_ids"`
}

type GetAssetTyreUninstalledReportResp struct {
	AssetStatusLabel           string      `json:"asset_status_label"`
	AssetStatusCode            string      `json:"asset_status_code"`
	InitialConditionCode       string      `json:"initial_condition_code"`
	UnlinkedDatetime           *time.Time  `json:"unlinked_datetime"`
	ReasonCode                 string      `json:"reason_code"`
	ReasonLabel                string      `json:"reason_label"`
	Reason                     string      `json:"reason"`
	SubReasonCode              *string     `json:"sub_reason_code"`
	SubReason                  string      `json:"sub_reason"`
	SubReasonLabel             string      `json:"sub_reason_label"`
	SerialNumber               string      `json:"serial_number"`
	Rfid                       string      `json:"rfid"`
	BrandName                  string      `json:"brand_name"`
	PatternType                string      `json:"pattern_type"`
	TyreSize                   string      `json:"tyre_size"`
	PlyRating                  float64     `json:"ply_rating"`
	DateCode                   null.String `json:"date_code"`
	StartThreadDepth           null.Float  `json:"start_thread_depth"`
	OriginalTd                 null.Float  `json:"original_td"`
	AverageRTD                 null.Float  `json:"average_rtd"`
	UtilizationRatePercentage  float64     `json:"utilization_rate_percentage"`
	UtilizationRateStatusCode  string      `json:"utilization_rate_status_code"`
	TyrePosition               int         `json:"tyre_position"`
	ParentAssetReferenceNumber string      `json:"parent_asset_reference_number"`
	PartnerOwnerName           string      `json:"partner_owner_name"`
	ParentAssetSubcategoryName string      `json:"parent_asset_subcategory_name"`
	TotalKM                    int         `json:"total_km"`
	TotalHM                    float64     `json:"total_hm"`
	MeterCalculationCode       null.String `json:"meter_calculation_code"`
	LastInspectedAt            null.Time   `json:"last_inspected_at"`
	ProjectedLifeKm            int         `json:"projected_life_km"`
	ProjectedLifeHm            float64     `json:"projected_life_hm"`
	TreadWearRateKm            float64     `json:"tread_wear_rate_km"`
	TreadWearRateHm            float64     `json:"tread_wear_rate_hm"`
	Cost                       float64     `json:"cost"`
	CPK                        float64     `json:"cpk"`
	CPH                        float64     `json:"cph"`
	RemainingLifeKm            int         `json:"remaining_life_km"`
	RemainingLifeHm            float64     `json:"remaining_life_hm"`
}

func BuildGetAssetTyreUninstalledReportResp(assetLinked models.AssetLinked, refAmounts map[string]financeDtos.ReferenceTotalAmount) GetAssetTyreUninstalledReportResp {
	assetTyre := assetLinked.ChildAsset
	treadCost := refAmounts[assetTyre.LatestTread().ID].Amount
	runningTreadCost := helpers.CalculateTyreTreadRunningCost(assetTyre.OriginalTd, assetTyre.StartThreadDepth, assetTyre.AverageRTD, treadCost)
	runningCost := float64(refAmounts[assetTyre.AssetID].Amount-treadCost) + runningTreadCost

	lifetimeKM := assetTyre.SumLifeTimeKM()
	lifetimeHM := assetTyre.SumLifeTimeHM()

	if assetTyre.PrevTotalKM.Valid {
		lifetimeKM = max(lifetimeKM-int(assetTyre.PrevTotalKM.Int64), 0)
	}

	if assetTyre.PrevTotalHm.Valid {
		lifetimeHM = max(lifetimeHM-calculationhelpers.Div100(int(assetTyre.PrevTotalHm.Int64)), 0)
	}

	cpk := 0.0
	if lifetimeKM > 0 {
		cpk = calculationhelpers.RoundToTwoDecimals(runningCost / float64(lifetimeKM))
	}
	cph := 0.0
	if lifetimeHM > 0 {
		cph = calculationhelpers.RoundToTwoDecimals(runningCost / lifetimeHM)
	}

	lastInspectedAt := assetTyre.AverageRtdLastUpdatedAt
	if assetTyre.PressureLastUpdatedAt.Valid && (!assetTyre.AverageRtdLastUpdatedAt.Valid || assetTyre.PressureLastUpdatedAt.Time.After(lastInspectedAt.Time)) {
		lastInspectedAt = assetTyre.PressureLastUpdatedAt
	}

	if assetTyre.PressureLastUpdatedAt.Valid && (!assetTyre.AverageRtdLastUpdatedAt.Valid || assetTyre.TemperatureLastUpdatedAt.Time.After(lastInspectedAt.Time)) {
		lastInspectedAt = assetTyre.TemperatureLastUpdatedAt
	}

	projectedLifeKm := assetTyre.LifetimeProjectedLifeKM()
	projectedLifeHm := assetTyre.LifetimeProjectedLifeHm()

	resp := GetAssetTyreUninstalledReportResp{
		AssetStatusLabel:           assetTyre.Asset.AssetStatus.Label,
		InitialConditionCode:       assetTyre.Asset.InitialConditionCode,
		AssetStatusCode:            assetTyre.Asset.AssetStatusCode,
		UnlinkedDatetime:           assetLinked.UnlinkedDatetime,
		ParentAssetSubcategoryName: assetLinked.AssetParent.CustomAssetSubCategory.Name,
		// ReasonCode:                assetTyre.StatusRequestScrapped.ReasonCode,
		// Reason:                    assetTyre.StatusRequestScrapped.Reason,
		// SubReasonCode:             assetTyre.StatusRequestScrapped.SubReasonCode,
		// SubReason:                 assetTyre.StatusRequestScrapped.SubReason,
		SerialNumber:              assetTyre.Asset.SerialNumber,
		Rfid:                      assetTyre.Asset.Rfid,
		BrandName:                 assetTyre.Asset.Brand.BrandName,
		PatternType:               assetTyre.Tyre.PatternType,
		TyreSize:                  assetTyre.Tyre.GetTyreSize(),
		PlyRating:                 assetTyre.Tyre.PlyRating,
		DateCode:                  assetTyre.DateCode,
		StartThreadDepth:          null.FloatFrom(assetTyre.StartThreadDepth),
		OriginalTd:                null.FloatFrom(assetTyre.OriginalTd),
		AverageRTD:                null.FloatFrom(assetTyre.AverageRTD),
		UtilizationRatePercentage: assetTyre.UtilizationRatePercentage,
		UtilizationRateStatusCode: assetTyre.UtilizationRatePercentageStatusCode,
		// TyrePosition:               assetLinked.AssetLinkedAssetVehicleTyre.TyrePosition,
		ParentAssetReferenceNumber: assetLinked.AssetParent.ReferenceNumber,
		PartnerOwnerName:           assetLinked.AssetParent.PartnerOwnerName,
		TotalKM:                    lifetimeKM,
		TotalHM:                    lifetimeHM,
		MeterCalculationCode:       assetTyre.MeterCalculationCode,
		LastInspectedAt:            lastInspectedAt,
		ProjectedLifeKm:            projectedLifeKm,
		ProjectedLifeHm:            projectedLifeHm,
		TreadWearRateKm:            assetTyre.TreadWearRateKm(),
		TreadWearRateHm:            assetTyre.TreadWearRateHm(),
		Cost:                       float64(refAmounts[assetTyre.AssetID].Amount),
		CPK:                        cpk,
		CPH:                        cph,
	}

	if projectedLifeKm > 0 {
		resp.RemainingLifeKm = projectedLifeKm - lifetimeKM
	}

	if projectedLifeHm > 0 {
		resp.RemainingLifeHm = projectedLifeHm - lifetimeHM
	}

	if assetTyre.StatusRequestScrapped != nil {
		resp.ReasonCode = assetTyre.StatusRequestScrapped.ReasonCode
		resp.ReasonLabel = assetTyre.StatusRequestScrapped.StatusRequestReason.Label
		resp.Reason = assetTyre.StatusRequestScrapped.Reason
		resp.SubReasonCode = assetTyre.StatusRequestScrapped.SubReasonCode
		resp.SubReasonLabel = assetTyre.StatusRequestScrapped.StatusRequestSubReason.Label
		resp.SubReason = assetTyre.StatusRequestScrapped.SubReason
	}

	if assetLinked.AssetLinkedAssetVehicleTyre != nil {
		resp.TyrePosition = assetLinked.AssetLinkedAssetVehicleTyre.TyrePosition
	}

	if assetTyre.HasNotSetRTD.Bool {
		resp.AverageRTD = null.Float{}

		if assetTyre.PrevKmHmDataUnavailable.Bool {
			resp.StartThreadDepth = null.Float{}
		}
	}

	return resp
}

func ConvertGetAssetTyreUninstalledReportRespListToExport(resp []GetAssetTyreUninstalledReportResp, userName, clientName string, clientLogo []byte, logoExt string, hideCustomer bool) exceltmpl.ReportTempl {
	templ := exceltmpl.ReportTempl{
		Title:         "Tyre Need Action Report",
		SheetName:     "Tyre Need Action Report",
		Logo:          clientLogo,
		LogoExtension: logoExt,
		DownloadBy:    userName,
		ClientName:    clientName,
		Description:   "This report is to show the current condition of tyre uninstalled from vehicle",
		Columns: []exceltmpl.ReportColumn{
			{Name: "No"},                     //0
			{Name: "Status"},                 //1
			{Name: "Tyre Initial Condition"}, //2
			{Name: "Date Uninstalled"},       //3
			{Name: "Damaged Area"},           //4
			{Name: "Cause of Damage"},        //5
			{Name: "Tyre Serial Number"},     //6
			{Name: "RFID / Other ID"},        //7
			{Name: "Brand"},                  //8
			{Name: "Pattern / Type"},         //9
			{Name: "Tyre Size"},              //10
			{Name: "Ply Rating"},             //11
			{Name: "Date Code"},              //12
			{Name: "OTD (mm)", Style: exceltmpl.TableFloatNumberValueStyle},                //13
			{Name: "STD (mm)", Style: exceltmpl.TableFloatNumberValueStyle},                //14
			{Name: "Average RTD (mm)", Style: exceltmpl.TableFloatNumberValueStyle},        //15
			{Name: "TUR%", Style: exceltmpl.TableFloatNumberValueStyle},                    //16
			{Name: "Total KM", Style: exceltmpl.TableFloatNumberValueStyle},                //17
			{Name: "Total HM", Style: exceltmpl.TableFloatNumberValueStyle},                //18
			{Name: "Remaining Life KM", Style: exceltmpl.TableFloatNumberValueStyle},       //19
			{Name: "Remaining Life HM", Style: exceltmpl.TableFloatNumberValueStyle},       //20
			{Name: "Projected Life KM", Style: exceltmpl.TableFloatNumberValueStyle},       //21
			{Name: "Projected Life HM", Style: exceltmpl.TableFloatNumberValueStyle},       //22
			{Name: "Tread Wear Rate (km/mm)", Style: exceltmpl.TableFloatNumberValueStyle}, //23
			{Name: "Tread Wear Rate (h/mm)", Style: exceltmpl.TableFloatNumberValueStyle},  //24
			{Name: "Date of last inspection"},                                              //25
			{Name: "Uninstalled From"},                                                     //26
			{Name: "Customer Name"},                                                        //27
			{Name: "Asset Subcategory"},                                                    //28
			{Name: "Previous Tyre Position", Style: exceltmpl.TableFloatNumberValueStyle},  //29
			{Name: "Tyre Lifetime Cost", Style: exceltmpl.TableFloatNumberValueStyle},      //30
			{Name: "CPK", Style: exceltmpl.TableFloatNumberValueStyle},                     //31
			{Name: "CPH", Style: exceltmpl.TableFloatNumberValueStyle},                     //32
		},
		Rows: make([][]any, len(resp)),
	}

	if hideCustomer {
		templ.Columns = append(templ.Columns[:27], templ.Columns[28:]...)
	}

	for i, item := range resp {
		row := []any{
			i + 1,                 // 0
			item.AssetStatusLabel, // 1
			constants.GetInitialConditionLabelFromCode(item.InitialConditionCode), // 2
			item.UnlinkedDatetime.Local().Format("02 Jan 2006"),                   // 3
			item.ReasonLabel,                // 4
			item.SubReasonLabel,             // 5
			item.SerialNumber,               // 6
			item.Rfid,                       // 7
			item.BrandName,                  // 8
			item.PatternType,                // 9
			item.TyreSize,                   // 10
			item.PlyRating,                  // 11
			item.DateCode.String,            // 12
			item.OriginalTd.Float64,         // 13
			item.StartThreadDepth.Float64,   // 14
			item.AverageRTD.Float64,         // 15
			item.UtilizationRatePercentage,  // 16
			item.TotalKM,                    // 17
			item.TotalHM,                    // 18
			item.RemainingLifeKm,            // 19
			item.RemainingLifeHm,            // 20
			item.ProjectedLifeKm,            // 21
			item.ProjectedLifeHm,            // 22
			item.TreadWearRateKm,            // 23
			item.TreadWearRateHm,            // 24
			nil,                             // 25
			item.ParentAssetReferenceNumber, // 26
			item.PartnerOwnerName,           // 27
			item.ParentAssetSubcategoryName, // 28
			item.TyrePosition,               // 29
			item.Cost,                       // 30
			item.CPK,                        // 31
			item.CPH,                        // 32
		}

		if hideCustomer {
			// Remove Customer Name column (index 27)
			row = append(row[:27], row[28:]...)
		}

		templ.Rows[i] = row

		if !item.OriginalTd.Valid {
			templ.Rows[i][13] = nil
		}

		if !item.StartThreadDepth.Valid {
			templ.Rows[i][14] = nil
		}

		isKM := item.MeterCalculationCode.String == constants.TYRE_METER_CALCULATION_CODE_KM
		isHM := item.MeterCalculationCode.String == constants.TYRE_METER_CALCULATION_CODE_HM
		isKMHM := item.MeterCalculationCode.String == constants.TYRE_METER_CALCULATION_CODE_KM_HM

		if isKM || isKMHM {
			templ.Rows[i][16] = nil
			templ.Rows[i][18] = nil
			templ.Rows[i][20] = nil
			templ.Rows[i][26] = nil
		}

		if !item.AverageRTD.Valid {
			templ.Rows[i][15] = nil
		}

		if item.LastInspectedAt.Valid {
			templ.Rows[i][25] = item.LastInspectedAt.Time.Local().Format("02 Jan 2006")
		}

		// Adjust column indices based on whether customer column is hidden
		totalHMIdx := 18
		remainingLifeHMIdx := 20
		projectedLifeHMIdx := 22
		treadWearRateHMIdx := 24
		cphIdx := 32
		totalKMIdx := 17
		remainingLifeKMIdx := 19
		projectedLifeKMIdx := 21
		treadWearRateKMIdx := 23
		cpkIdx := 31

		if hideCustomer {
			// Shift indices down by 1 for columns after Customer Name
			cphIdx = 31
			cpkIdx = 30
		}

		if !isKMHM {
			if isKM {
				templ.Rows[i][totalHMIdx] = nil
				templ.Rows[i][remainingLifeHMIdx] = nil
				templ.Rows[i][projectedLifeHMIdx] = nil
				templ.Rows[i][treadWearRateHMIdx] = nil
				templ.Rows[i][cphIdx] = nil
			} else if isHM {
				templ.Rows[i][totalKMIdx] = nil
				templ.Rows[i][remainingLifeKMIdx] = nil
				templ.Rows[i][projectedLifeKMIdx] = nil
				templ.Rows[i][treadWearRateKMIdx] = nil
				templ.Rows[i][cpkIdx] = nil
			}
		}
	}

	return templ
}

type GetAssetTyreReplacementForecastReportReq struct {
	commonmodel.ListRequest
}

type GetAssetTyreReplacementForecastReportResp struct {
	ParentAssetReferenceNumber          string      `json:"parent_asset_reference_number"`
	PartnerOwnerName                    string      `json:"partner_owner_name"`
	ParentAssetSubcategoryName          string      `json:"parent_asset_subcategory_name"`
	TyrePosition                        int         `json:"tyre_position"`
	LinkedDatetime                      time.Time   `json:"linked_datetime"`
	SerialNumber                        string      `json:"serial_number"`
	Rfid                                string      `json:"rfid"`
	Brand                               string      `json:"brand"`
	PatternType                         string      `json:"pattern_type"`
	TyreSize                            string      `json:"tyre_size"`
	PlyRating                           float64     `json:"ply_rating"`
	DateCode                            null.String `json:"date_code"`
	StartThreadDepth                    float64     `json:"start_thread_depth"`
	AverageRtd                          float64     `json:"average_rtd"`
	UtilizationRatePercentage           float64     `json:"utilization_rate_percentage"`
	UtilizationRatePercentageStatusCode string      `json:"utilization_rate_status_code"`
	MeterCalculationCode                null.String `json:"meter_calculation_code"`
	TotalKM                             int         `json:"total_km"`
	TotalHM                             float64     `json:"total_hm"`
	ProjectedLifeKm                     int         `json:"projected_life_km"`
	ProjectedLifeHm                     float64     `json:"projected_life_hm"`
	TreadWearRateKm                     float64     `json:"tread_wear_rate_km"`
	TreadWearRateHm                     float64     `json:"tread_wear_rate_hm"`
	LastInspectedAt                     null.Time   `json:"last_inspected_at"`
	Cost                                float64     `json:"cost"`
	CPK                                 float64     `json:"cpk"`
	CPH                                 float64     `json:"cph"`
	FirstUsedDate                       time.Time   `json:"first_used_date"`
	RunningDays                         int         `json:"running_days"`
	AvgKmPerDay                         float64     `json:"avg_km_per_day"`
	AvgHmPerDay                         float64     `json:"avg_hm_per_day"`
	EstRemainingDays                    int         `json:"est_remaining_days"`
	EstReplacementDate                  null.Time   `json:"est_replacement_date"`
}

func ConvertGetAssetTyreReplacementForecastReportRespListToExport(resp []GetAssetTyreReplacementForecastReportResp, userName, clientName string, clientLogo []byte, logoExt string, hideCustomer bool) exceltmpl.ReportTempl {
	templ := exceltmpl.ReportTempl{
		Title:         "Tyre Replacement Forecast Report",
		SheetName:     "Tyre Replacement Forecast",
		Logo:          clientLogo,
		LogoExtension: logoExt,
		DownloadBy:    userName,
		ClientName:    clientName,
		Description:   "This report shows the forecast for tyre replacements based on current usage patterns",
		Columns: []exceltmpl.ReportColumn{
			{Name: "No"},                 // 0
			{Name: "Asset ID"},           // 1
			{Name: "Customer Name"},      // 2
			{Name: "Asset Subcategory"},  // 3
			{Name: "Tyre Position"},      // 4
			{Name: "Date Installed"},     // 5
			{Name: "Tyre Serial Number"}, // 6
			{Name: "RFID / Other ID"},    // 7
			{Name: "Brand"},              // 8
			{Name: "Pattern / Type"},     // 9
			{Name: "Tyre Size"},          // 10
			{Name: "Ply Rating"},         // 11
			{Name: "Date Code"},          // 12
			{Name: "OTD (mm)", Style: exceltmpl.TableFloatNumberValueStyle},                    // 13
			{Name: "Average RTD (mm)", Style: exceltmpl.TableFloatNumberValueStyle},            // 14
			{Name: "TUR%", Style: exceltmpl.TableFloatNumberValueStyle},                        // 15
			{Name: "Total KM", Style: exceltmpl.TableFloatNumberValueStyle},                    // 16
			{Name: "Total HM", Style: exceltmpl.TableFloatNumberValueStyle},                    // 17
			{Name: "Projected Life KM", Style: exceltmpl.TableFloatNumberValueStyle},           // 18
			{Name: "Projected Life HM", Style: exceltmpl.TableFloatNumberValueStyle},           // 19
			{Name: "Tread Wear Rate (km/mm)", Style: exceltmpl.TableFloatNumberValueStyle},     // 20
			{Name: "Tread Wear Rate (h/mm)", Style: exceltmpl.TableFloatNumberValueStyle},      // 21
			{Name: "Date of last inspection"},                                                  // 22
			{Name: "Tyre Lifetime Cost", Style: exceltmpl.TableFloatNumberValueStyle},          // 23
			{Name: "CPK", Style: exceltmpl.TableFloatNumberValueStyle},                         // 24
			{Name: "CPH", Style: exceltmpl.TableFloatNumberValueStyle},                         // 25
			{Name: "Date Tyre was first Used"},                                                 // 26
			{Name: "No. of Running days", Style: exceltmpl.TableFloatNumberValueStyle},         // 27
			{Name: "Est.Avg. km per day", Style: exceltmpl.TableFloatNumberValueStyle},         // 28
			{Name: "Est. remaining running days", Style: exceltmpl.TableFloatNumberValueStyle}, // 29
			{Name: "Est. Date of Replacement"},                                                 // 30
		},
		Rows: make([][]any, len(resp)),
	}

	if hideCustomer {
		templ.Columns = append(templ.Columns[:2], templ.Columns[3:]...)
	}

	for i, item := range resp {
		row := []any{
			i + 1,
			item.ParentAssetReferenceNumber,
			item.PartnerOwnerName,
			item.ParentAssetSubcategoryName,
			item.TyrePosition,
			item.LinkedDatetime.Local().Format("02 Jan 2006"),
			item.SerialNumber,
			item.Rfid,
			item.Brand,
			item.PatternType,
			item.TyreSize,
			item.PlyRating,
			item.DateCode.String,
			item.StartThreadDepth,
			item.AverageRtd,
			item.UtilizationRatePercentage,
			item.TotalKM,
			item.TotalHM,
			item.ProjectedLifeKm,
			item.ProjectedLifeHm,
			item.TreadWearRateKm,
			item.TreadWearRateHm,
			nil, // Last inspection date
			item.Cost,
			item.CPK,
			item.CPH,
			item.FirstUsedDate.Local().Format("02 Jan 2006"),
			item.RunningDays,
			item.AvgKmPerDay,
			item.EstRemainingDays,
			item.EstReplacementDate.Time.Local().Format("02 Jan 2006"),
		}

		if hideCustomer {
			// Remove Customer Name column (index 2)
			row = append(row[:2], row[3:]...)
		}

		templ.Rows[i] = row

		isKM := item.MeterCalculationCode.String == constants.TYRE_METER_CALCULATION_CODE_KM
		isHM := item.MeterCalculationCode.String == constants.TYRE_METER_CALCULATION_CODE_HM
		isKMHM := item.MeterCalculationCode.String == constants.TYRE_METER_CALCULATION_CODE_KM_HM

		// Adjust column indices based on whether customer column is hidden
		totalKMIdx := 16
		totalHMIdx := 17
		projectedLifeKMIdx := 18
		projectedLifeHMIdx := 19
		treadWearRateKMIdx := 20
		treadWearRateHMIdx := 21
		lastInspectionIdx := 22
		cpkIdx := 24
		cphIdx := 25

		if hideCustomer {
			// Shift indices down by 1 for columns after Customer Name
			totalKMIdx = 15
			totalHMIdx = 16
			projectedLifeKMIdx = 17
			projectedLifeHMIdx = 18
			treadWearRateKMIdx = 19
			treadWearRateHMIdx = 20
			lastInspectionIdx = 21
			cpkIdx = 23
			cphIdx = 24
		}

		if !isKMHM {
			if isKM {
				templ.Rows[i][totalHMIdx] = nil
				templ.Rows[i][projectedLifeHMIdx] = nil
				templ.Rows[i][treadWearRateHMIdx] = nil
				templ.Rows[i][cphIdx] = nil
			} else if isHM {
				templ.Rows[i][totalKMIdx] = nil
				templ.Rows[i][projectedLifeKMIdx] = nil
				templ.Rows[i][treadWearRateKMIdx] = nil
				templ.Rows[i][cpkIdx] = nil
			}
		}

		if item.LastInspectedAt.Valid {
			templ.Rows[i][lastInspectionIdx] = item.LastInspectedAt.Time.Local().Format("02 Jan 2006")
		}
	}

	return templ
}

func BuildGetAssetTyreReplacementForecastReportResp(assetTyre models.AssetTyre, mapRefAmounts map[string]financeDtos.ReferenceTotalAmount) GetAssetTyreReplacementForecastReportResp {

	treadCost := mapRefAmounts[assetTyre.LatestTread().ID].Amount
	runningCost := helpers.CalculateTyreTreadRunningCost(assetTyre.OriginalTd, assetTyre.StartThreadDepth, assetTyre.AverageRTD, treadCost)
	cost := float64(mapRefAmounts[assetTyre.AssetID].Amount-treadCost) + runningCost

	lifetimeKM := assetTyre.SumLifeTimeKM()
	lifetimeHM := assetTyre.SumLifeTimeHM()

	if assetTyre.PrevTotalKM.Valid {
		lifetimeKM = max(lifetimeKM-int(assetTyre.PrevTotalKM.Int64), 0)
	}

	if assetTyre.PrevTotalHm.Valid {
		lifetimeHM = max(lifetimeHM-calculationhelpers.Div100(int(assetTyre.PrevTotalHm.Int64)), 0)
	}

	cpk := 0.0
	if lifetimeKM > 0 {
		cpk = calculationhelpers.RoundToTwoDecimals(runningCost / float64(lifetimeKM))
	}
	cph := 0.0
	if lifetimeHM > 0 {
		cph = calculationhelpers.RoundToTwoDecimals(runningCost / lifetimeHM)
	}

	lastInspectedAt := assetTyre.AverageRtdLastUpdatedAt
	if assetTyre.PressureLastUpdatedAt.Valid && (!assetTyre.AverageRtdLastUpdatedAt.Valid || assetTyre.PressureLastUpdatedAt.Time.After(lastInspectedAt.Time)) {
		lastInspectedAt = assetTyre.PressureLastUpdatedAt
	}

	if assetTyre.PressureLastUpdatedAt.Valid && (!assetTyre.AverageRtdLastUpdatedAt.Valid || assetTyre.TemperatureLastUpdatedAt.Time.After(lastInspectedAt.Time)) {
		lastInspectedAt = assetTyre.TemperatureLastUpdatedAt
	}

	startTime := lastInspectedAt.Time
	if !lastInspectedAt.Valid ||
		(lastInspectedAt.Valid && lastInspectedAt.Time.Before(assetTyre.AssetLinked.LinkedDatetime)) {
		startTime = assetTyre.AssetLinked.LinkedDatetime
	}

	projectedLifeKm := assetTyre.LifetimeProjectedLifeKM()
	projectedLifeHm := assetTyre.LifetimeProjectedLifeHm()

	runningDays := int(time.Since(startTime).Hours() / 24)
	// runningDays = assetTyre.TotalLifetime / 3600 * 24

	avgKmPerDay := 0.0
	avgHmPerDay := 0.0

	if runningDays > 0 {
		avgKmPerDay = float64(assetTyre.TotalKM) / float64(runningDays)
		avgHmPerDay = calculationhelpers.Div100(assetTyre.TotalHm) / float64(runningDays)
	}

	estRemainingDays := 0
	if assetTyre.MeterCalculationCode.String == constants.TYRE_METER_CALCULATION_CODE_HM && avgHmPerDay > 0 {
		estRemainingDays = int(float64(projectedLifeHm-calculationhelpers.Div100(assetTyre.TotalHm)) / avgHmPerDay)
	} else if avgKmPerDay > 0 {
		estRemainingDays = int(float64(projectedLifeKm-assetTyre.TotalKM) / avgKmPerDay)
	}

	estReplacementDate := null.Time{}
	if estRemainingDays > 0 {
		estReplacementDate = null.TimeFrom(startTime.AddDate(0, 0, estRemainingDays))
	}

	return GetAssetTyreReplacementForecastReportResp{
		ParentAssetReferenceNumber:          assetTyre.AssetLinked.AssetParent.ReferenceNumber,
		PartnerOwnerName:                    assetTyre.AssetLinked.AssetParent.PartnerOwnerName,
		ParentAssetSubcategoryName:          assetTyre.AssetLinked.AssetParent.CustomAssetSubCategory.Name,
		TyrePosition:                        assetTyre.AssetLinked.AssetLinkedAssetVehicleTyre.TyrePosition,
		LinkedDatetime:                      assetTyre.AssetLinked.LinkedDatetime,
		SerialNumber:                        assetTyre.Asset.SerialNumber,
		Rfid:                                assetTyre.Asset.Rfid,
		Brand:                               assetTyre.Asset.Brand.BrandName,
		PatternType:                         assetTyre.Tyre.PatternType,
		TyreSize:                            assetTyre.Tyre.GetTyreSize(),
		PlyRating:                           assetTyre.Tyre.PlyRating,
		DateCode:                            assetTyre.DateCode,
		StartThreadDepth:                    assetTyre.StartThreadDepth,
		AverageRtd:                          assetTyre.AverageRTD,
		UtilizationRatePercentage:           assetTyre.UtilizationRatePercentage,
		UtilizationRatePercentageStatusCode: assetTyre.UtilizationRatePercentageStatusCode,
		MeterCalculationCode:                assetTyre.MeterCalculationCode,
		TotalKM:                             assetTyre.TotalKM,
		TotalHM:                             calculationhelpers.Div100(assetTyre.TotalHm),
		ProjectedLifeKm:                     projectedLifeKm,
		ProjectedLifeHm:                     projectedLifeHm,
		Cost:                                cost,
		TreadWearRateKm:                     assetTyre.TreadWearRateKm(),
		TreadWearRateHm:                     assetTyre.TreadWearRateHm(),
		CPK:                                 cpk,
		CPH:                                 cph,
		LastInspectedAt:                     lastInspectedAt,
		FirstUsedDate:                       assetTyre.AssetLinked.LinkedDatetime,
		RunningDays:                         runningDays,
		AvgKmPerDay:                         avgKmPerDay,
		AvgHmPerDay:                         avgHmPerDay,
		EstRemainingDays:                    estRemainingDays,
		EstReplacementDate:                  estReplacementDate,
	}
}

type GetAssetTyreUsageReportReq struct {
	commonmodel.ListRequest
	StartDate                 time.Time `form:"start_date"`
	EndDate                   time.Time `form:"end_date"`
	BrandIDs                  []string  `form:"brand_ids"`
	TyreSizes                 []string  `form:"tyre_sizes"`
	PatternTypes              []string  `form:"pattern_type"`
	CustomAssetSubCategoryIDs []string  `form:"custom_asset_sub_category_ids"`
}

type GetAssetTyreUsageReportResp struct {
	SerialNumber               string      `json:"serial_number"`
	Rfid                       string      `json:"rfid"`
	Brand                      string      `json:"brand"`
	PatternType                string      `json:"pattern_type"`
	TyreSize                   string      `json:"tyre_size"`
	PlyRating                  float64     `json:"ply_rating"`
	DateCode                   null.String `json:"date_code"`
	Cost                       float64     `json:"cost"`
	StartThreadDepth           float64     `json:"start_thread_depth"`
	ParentAssetReferenceNumber string      `json:"parent_asset_reference_number"`
	ParentAssetSerialNumber    string      `json:"parent_asset_serial_number"`
	PartnerOwnerName           string      `json:"partner_owner_name"`
	ParentAssetSubcategoryName string      `json:"parent_asset_subcategory_name"`
	TyrePosition               int         `json:"tyre_position"`
	FirstInstalledDatetime     null.Time   `json:"first_installed_datetime"`
}

func BuildGetAssetTyreUsageReportResp(assetTyre models.AssetTyre, mapRefAmounts map[string]financeDtos.ReferenceTotalAmount) GetAssetTyreUsageReportResp {
	item := GetAssetTyreUsageReportResp{
		SerialNumber:     assetTyre.Asset.SerialNumber,
		Rfid:             assetTyre.Asset.Rfid,
		Brand:            assetTyre.Asset.Brand.BrandName,
		PatternType:      assetTyre.Tyre.PatternType,
		TyreSize:         assetTyre.Tyre.GetTyreSize(),
		PlyRating:        assetTyre.Tyre.PlyRating,
		DateCode:         assetTyre.DateCode,
		Cost:             float64(mapRefAmounts[assetTyre.AssetID].AmountFixedAsset),
		StartThreadDepth: assetTyre.StartThreadDepth,
		// ParentAssetReferenceNumber: assetTyre.AssetLinked.AssetParent.ReferenceNumber,
		// PartnerOwnerName:           assetTyre.AssetLinked.AssetParent.PartnerOwnerName,
		// ParentAssetSubcategoryName: assetTyre.AssetLinked.AssetParent.CustomAssetSubCategory.Name,
		// TyrePosition:               assetTyre.AssetLinked.AssetLinkedAssetVehicleTyre.TyrePosition,
		FirstInstalledDatetime: assetTyre.FirstInstalledDatetime,
	}

	if assetTyre.AssetLinked != nil {
		item.ParentAssetReferenceNumber = assetTyre.AssetLinked.AssetParent.ReferenceNumber
		item.ParentAssetSerialNumber = assetTyre.AssetLinked.AssetParent.SerialNumber
		item.PartnerOwnerName = assetTyre.AssetLinked.AssetParent.PartnerOwnerName
		item.ParentAssetSubcategoryName = assetTyre.AssetLinked.AssetParent.CustomAssetSubCategory.Name
		if assetTyre.AssetLinked.AssetLinkedAssetVehicleTyre != nil {
			item.TyrePosition = assetTyre.AssetLinked.AssetLinkedAssetVehicleTyre.TyrePosition
		}
	}

	return item
}

func ConvertGetAssetTyreUsageReportRespListToExport(resp []GetAssetTyreUsageReportResp, userName, clientName string, clientLogo []byte, logoExt string, hideCustomer bool, req GetAssetTyreUsageReportReq) exceltmpl.ReportTempl {
	templ := exceltmpl.ReportTempl{
		Title:         "Tyre Usage Report",
		SheetName:     "Tyre Usage Report",
		Logo:          clientLogo,
		LogoExtension: logoExt,
		DownloadBy:    userName,
		ClientName:    clientName,
		Description:   "This report is to show how many tyres are installed for the first time",
		Period: &exceltmpl.ReportPeriod{
			Start: req.StartDate,
			End:   req.EndDate,
		},
		Columns: []exceltmpl.ReportColumn{
			{Name: "No"},                 //0
			{Name: "Tyre Serial Number"}, //1
			{Name: "RFID / Other ID"},    //2
			{Name: "Brand"},              //3
			{Name: "Pattern / Type"},     //4
			{Name: "Tyre Size"},          //5
			{Name: "Ply Rating"},         //6
			{Name: "Date Code"},          //7
			{Name: "OTD (mm)", Style: exceltmpl.TableFloatNumberValueStyle}, //8
			{Name: "Attached With"},           // 9
			{Name: "Customer Name"},           // 10
			{Name: "Tyre Position"},           // 11
			{Name: "Asset Subcategory"},       // 12
			{Name: "Date of First Installed"}, // 13
			{Name: "Tyre Purchase Cost", Style: exceltmpl.TableFloatNumberValueStyle}, // 14
		},
		Rows: make([][]any, len(resp)),
	}

	customerIdx := 10

	if hideCustomer {
		templ.Columns = append(templ.Columns[:customerIdx], templ.Columns[customerIdx+1:]...)
	}

	for i, item := range resp {
		parentIdent := item.ParentAssetReferenceNumber
		if parentIdent == "" {
			parentIdent = item.ParentAssetSerialNumber
		} else if item.ParentAssetSerialNumber != "" {
			parentIdent += "/" + item.ParentAssetSerialNumber
		}

		row := []any{
			i + 1,
			item.SerialNumber,
			item.Rfid,
			item.Brand,
			item.PatternType,
			item.TyreSize,
			item.PlyRating,
			item.DateCode.String,
			item.StartThreadDepth,
			parentIdent,
			item.PartnerOwnerName,
			item.TyrePosition,
			item.ParentAssetSubcategoryName,
			nil,
			item.Cost,
		}

		if hideCustomer {
			row = append(row[:customerIdx], row[customerIdx+1:]...)
		}

		templ.Rows[i] = row

		// Adjust index for FirstInstalledDatetime based on whether customer column is hidden
		firstInstalledIdx := 13
		if hideCustomer {
			firstInstalledIdx = 12
		}

		if item.FirstInstalledDatetime.Valid {
			templ.Rows[i][firstInstalledIdx] = item.FirstInstalledDatetime.Time.Local().Format("02 Jan 2006")
		}
	}

	return templ
}

type GetAssetTyreInspectionReportReq struct {
	commonmodel.ListRequest
	StartDate                  time.Time `form:"start_date"`
	EndDate                    time.Time `form:"end_date"`
	BrandIDs                   []string  `form:"brand_ids"`
	TyreSizes                  []string  `form:"tyre_sizes"`
	PatternTypes               []string  `form:"pattern_type"`
	AssetStatusCodes           []string  `form:"asset_status_codes"`
	UtilizationRateStatusCodes []string  `form:"tur_codes"`
}

type GetAssetTyreInspectionReportResp struct {
	LastInspectedAt            null.Time   `json:"last_inspected_at"`
	AssetID                    string      `json:"asset_id"`
	MeterCalculationCode       null.String `json:"meter_calculation_code"`
	SerialNumber               string      `json:"serial_number"`
	Rfid                       string      `json:"rfid"`
	Brand                      string      `json:"brand"`
	PatternType                string      `json:"pattern_type"`
	TyreSize                   string      `json:"tyre_size"`
	PlyRating                  float64     `json:"ply_rating"`
	DateCode                   null.String `json:"date_code"`
	StartThreadDepth           float64     `json:"start_thread_depth"`
	AverageRtd                 float64     `json:"average_rtd"`
	UtilizationRatePercentage  float64     `json:"utilization_rate_percentage"`
	UtilizationRateStatusCode  string      `json:"utilization_rate_status_code"`
	TotalKM                    int         `json:"total_km"`
	TotalHM                    float64     `json:"total_hm"`
	ProjectedLifeKm            int         `json:"projected_life_km"`
	ProjectedLifeHm            float64     `json:"projected_life_hm"`
	TreadWearRateKm            float64     `json:"tread_wear_rate_km"`
	TreadWearRateHm            float64     `json:"tread_wear_rate_hm"`
	ParentAssetReferenceNumber string      `json:"parent_asset_reference_number"`
	ParentAssetSerialNumber    string      `json:"parent_asset_serial_number"`
	PartnerOwnerName           string      `json:"partner_owner_name"`
	ParentAssetSubcategoryName string      `json:"parent_asset_subcategory_name"`
	TyrePosition               int         `json:"tyre_position"`
	FirstInstalledDatetime     null.Time   `json:"first_installed_datetime"`
}

func BuildGetAssetTyreInspectionReportResp(assetTyre models.AssetTyre) GetAssetTyreInspectionReportResp {
	item := GetAssetTyreInspectionReportResp{
		LastInspectedAt:           assetTyre.LastInspectedAt,
		MeterCalculationCode:      assetTyre.MeterCalculationCode,
		AssetID:                   assetTyre.AssetID,
		SerialNumber:              assetTyre.Asset.SerialNumber,
		Rfid:                      assetTyre.Asset.Rfid,
		Brand:                     assetTyre.Asset.Brand.BrandName,
		PatternType:               assetTyre.Tyre.PatternType,
		TyreSize:                  assetTyre.Tyre.GetTyreSize(),
		PlyRating:                 assetTyre.Tyre.PlyRating,
		DateCode:                  assetTyre.DateCode,
		StartThreadDepth:          assetTyre.StartThreadDepth,
		AverageRtd:                assetTyre.AverageRTD,
		UtilizationRatePercentage: assetTyre.UtilizationRatePercentage,
		UtilizationRateStatusCode: assetTyre.UtilizationRatePercentageStatusCode,
		TotalKM:                   assetTyre.TotalKM,
		TotalHM:                   calculationhelpers.Div100(assetTyre.TotalHm),
		ProjectedLifeKm:           assetTyre.LifetimeProjectedLifeKM(),
		ProjectedLifeHm:           assetTyre.LifetimeProjectedLifeHm(),
		TreadWearRateKm:           assetTyre.TreadWearRateKm(),
		TreadWearRateHm:           assetTyre.TreadWearRateHm(),
		FirstInstalledDatetime:    assetTyre.FirstInstalledDatetime,
	}

	if assetTyre.AssetLinked != nil {
		item.ParentAssetReferenceNumber = assetTyre.AssetLinked.AssetParent.ReferenceNumber
		item.ParentAssetSerialNumber = assetTyre.AssetLinked.AssetParent.SerialNumber
		item.PartnerOwnerName = assetTyre.AssetLinked.AssetParent.PartnerOwnerName
		item.ParentAssetSubcategoryName = assetTyre.AssetLinked.AssetParent.CustomAssetSubCategory.Name
		if assetTyre.AssetLinked.AssetLinkedAssetVehicleTyre != nil {
			item.TyrePosition = assetTyre.AssetLinked.AssetLinkedAssetVehicleTyre.TyrePosition
		}
	}

	return item
}

func ConvertGetAssetTyreInspectionReportRespListToExport(resp []GetAssetTyreInspectionReportResp, userName, clientName string, clientLogo []byte, logoExt string, hideCustomer bool, req GetAssetTyreInspectionReportReq) exceltmpl.ReportTempl {
	templ := exceltmpl.ReportTempl{
		Title:         "Tyre Inspection Report",
		SheetName:     "Tyre Inspection Report",
		Logo:          clientLogo,
		LogoExtension: logoExt,
		DownloadBy:    userName,
		ClientName:    clientName,
		Description:   "This report is to show the current condition of tyre installed on vehicle",
		Period: &exceltmpl.ReportPeriod{
			Start: req.StartDate,
			End:   req.EndDate,
		},
		Columns: []exceltmpl.ReportColumn{
			{Name: "No"},                      // 0
			{Name: "Date of last inspection"}, // 1
			{Name: "Tyre Serial Number"},      // 2
			{Name: "RFID / Other ID"},         // 3
			{Name: "Brand"},                   // 4
			{Name: "Pattern / Type"},          // 5
			{Name: "Tyre Size"},               // 6
			{Name: "Ply Rating"},              // 7
			{Name: "Date Code"},               // 8
			{Name: "OTD (mm)", Style: exceltmpl.TableFloatNumberValueStyle},                // 9
			{Name: "Average RTD (mm)", Style: exceltmpl.TableFloatNumberValueStyle},        // 10
			{Name: "TUR%", Style: exceltmpl.TableFloatNumberValueStyle},                    // 11
			{Name: "Total KM", Style: exceltmpl.TableFloatNumberValueStyle},                // 12
			{Name: "Total HM", Style: exceltmpl.TableFloatNumberValueStyle},                // 13
			{Name: "Projected Life KM", Style: exceltmpl.TableFloatNumberValueStyle},       // 14
			{Name: "Projected Life HM", Style: exceltmpl.TableFloatNumberValueStyle},       // 15
			{Name: "Tread Wear Rate (km/mm)", Style: exceltmpl.TableFloatNumberValueStyle}, // 16
			{Name: "Tread Wear Rate (h/mm)", Style: exceltmpl.TableFloatNumberValueStyle},  // 17
			{Name: "Attached With"},              // 18
			{Name: "Customer Name"},              // 19
			{Name: "Attached Asset Subcategory"}, // 20
			{Name: "Attached Asset "},            // 21
			{Name: "Tyre Position"},              // 22
			{Name: "Date Installed"},             // 23
		},
		Rows: make([][]any, len(resp)),
	}

	if hideCustomer {
		templ.Columns = append(templ.Columns[:19], templ.Columns[20:]...)
	}

	for i, item := range resp {
		parentIdent := item.ParentAssetReferenceNumber
		if parentIdent == "" {
			parentIdent = item.ParentAssetSerialNumber
		} else if item.ParentAssetSerialNumber != "" {
			parentIdent += "/" + item.ParentAssetSerialNumber
		}

		templ.Rows[i] = []any{
			i + 1,                           // 0
			nil,                             // 1
			item.SerialNumber,               // 2
			item.Rfid,                       // 3
			item.Brand,                      // 4
			item.PatternType,                // 5
			item.TyreSize,                   // 6
			item.PlyRating,                  // 7
			item.DateCode.String,            // 8
			item.StartThreadDepth,           // 9
			item.AverageRtd,                 // 10
			item.UtilizationRatePercentage,  // 11
			item.TotalKM,                    // 12
			item.TotalHM,                    // 13
			item.ProjectedLifeKm,            // 14
			item.ProjectedLifeHm,            // 15
			item.TreadWearRateKm,            // 16
			item.TreadWearRateHm,            // 17
			parentIdent,                     // 18
			item.PartnerOwnerName,           // 19
			item.ParentAssetSubcategoryName, // 20
			nil,                             // 21
			item.TyrePosition,               // 22
			nil,                             // 23
		}

		if hideCustomer {
			// Remove Customer Name column (index 19)
			templ.Rows[i] = append(templ.Rows[i][:19], templ.Rows[i][20:]...)
		}

		if item.LastInspectedAt.Valid {
			templ.Rows[i][1] = item.LastInspectedAt.Time.Local().Format("02 Jan 2006")
		}

		if item.FirstInstalledDatetime.Valid {
			templ.Rows[i][21] = item.FirstInstalledDatetime.Time.Local().Format("02 Jan 2006")
		}

		isKM := item.MeterCalculationCode.String == constants.TYRE_METER_CALCULATION_CODE_KM
		isHM := item.MeterCalculationCode.String == constants.TYRE_METER_CALCULATION_CODE_HM
		isKMHM := item.MeterCalculationCode.String == constants.TYRE_METER_CALCULATION_CODE_KM_HM

		if !isKMHM {
			if isKM {
				templ.Rows[i][13] = nil
				templ.Rows[i][15] = nil
				templ.Rows[i][17] = nil
			} else if isHM {
				templ.Rows[i][12] = nil
				templ.Rows[i][14] = nil
				templ.Rows[i][16] = nil
			}
		}

	}

	return templ
}

type GetAssetTyreRotationReportReq struct {
	commonmodel.ListRequest
	StartDate time.Time `form:"start_date"`
	EndDate   time.Time `form:"end_date"`
}

type GetAssetTyreRotationReportResp struct {
	RotationDate               time.Time   `json:"rotation_date"`
	AssetID                    string      `json:"asset_id"`
	MeterCalculationCode       null.String `json:"meter_calculation_code"`
	SerialNumber               string      `json:"serial_number"`
	Rfid                       string      `json:"rfid"`
	Brand                      string      `json:"brand"`
	PatternType                string      `json:"pattern_type"`
	TyreSize                   string      `json:"tyre_size"`
	PlyRating                  float64     `json:"ply_rating"`
	DateCode                   null.String `json:"date_code"`
	StartThreadDepth           float64     `json:"start_thread_depth"`
	AverageRtd                 float64     `json:"average_rtd"`
	UtilizationRatePercentage  float64     `json:"utilization_rate_percentage"`
	UtilizationRateStatusCode  string      `json:"utilization_rate_status_code"`
	TotalKM                    int         `json:"total_km"`
	TotalHM                    float64     `json:"total_hm"`
	ProjectedLifeKm            int         `json:"projected_life_km"`
	ProjectedLifeHm            float64     `json:"projected_life_hm"`
	TreadWearRateKm            float64     `json:"tread_wear_rate_km"`
	TreadWearRateHm            float64     `json:"tread_wear_rate_hm"`
	ParentAssetReferenceNumber string      `json:"parent_asset_reference_number"`
	ParentAssetSerialNumber    string      `json:"parent_asset_serial_number"`
	PartnerOwnerName           string      `json:"partner_owner_name"`
	ParentAssetSubcategoryName string      `json:"parent_asset_subcategory_name"`
	PrevTyrePosition           int         `json:"prev_tyre_position"`
	NewTyrePosition            int         `json:"new_tyre_position"`
}

func BuildGetAssetTyreRotationReportResp(tyreRotation models.TyreRotationReportModel) GetAssetTyreRotationReportResp {
	return GetAssetTyreRotationReportResp{
		RotationDate:               tyreRotation.UnlinkedDatetime,
		AssetID:                    tyreRotation.ChildAssetID,
		MeterCalculationCode:       tyreRotation.ChildAsset.MeterCalculationCode,
		SerialNumber:               tyreRotation.ChildAsset.Asset.SerialNumber,
		Rfid:                       tyreRotation.ChildAsset.Asset.Rfid,
		Brand:                      tyreRotation.ChildAsset.Asset.Brand.BrandName,
		PatternType:                tyreRotation.ChildAsset.Tyre.PatternType,
		TyreSize:                   tyreRotation.ChildAsset.Tyre.GetTyreSize(),
		PlyRating:                  tyreRotation.ChildAsset.Tyre.PlyRating,
		DateCode:                   tyreRotation.ChildAsset.DateCode,
		StartThreadDepth:           tyreRotation.ChildAsset.StartThreadDepth,
		AverageRtd:                 tyreRotation.ChildAsset.AverageRTD,
		UtilizationRatePercentage:  tyreRotation.ChildAsset.UtilizationRatePercentage,
		UtilizationRateStatusCode:  tyreRotation.ChildAsset.UtilizationRatePercentageStatusCode,
		TotalKM:                    tyreRotation.ChildAsset.TotalKM,
		TotalHM:                    calculationhelpers.Div100(tyreRotation.ChildAsset.TotalHm),
		ProjectedLifeKm:            tyreRotation.ChildAsset.LifetimeProjectedLifeKM(),
		ProjectedLifeHm:            tyreRotation.ChildAsset.LifetimeProjectedLifeHm(),
		TreadWearRateKm:            tyreRotation.ChildAsset.TreadWearRateKm(),
		TreadWearRateHm:            tyreRotation.ChildAsset.TreadWearRateHm(),
		ParentAssetReferenceNumber: tyreRotation.AssetParent.ReferenceNumber,
		ParentAssetSerialNumber:    tyreRotation.AssetParent.SerialNumber,
		PartnerOwnerName:           tyreRotation.AssetParent.PartnerOwnerName,
		ParentAssetSubcategoryName: tyreRotation.AssetParent.CustomAssetSubCategory.Name,
		PrevTyrePosition:           tyreRotation.PrevTyrePosition,
		NewTyrePosition:            tyreRotation.NewTyrePosition,
	}
}

func ConvertGetAssetTyreRotationReportRespListToExport(resp []GetAssetTyreRotationReportResp, userName, clientName string, clientLogo []byte, logoExt string, hideCustomer bool, req GetAssetTyreRotationReportReq) exceltmpl.ReportTempl {
	templ := exceltmpl.ReportTempl{
		Title:         "Tyre Rotation Report",
		SheetName:     "Tyre Rotation Report",
		Logo:          clientLogo,
		LogoExtension: logoExt,
		DownloadBy:    userName,
		ClientName:    clientName,
		Description:   "This report is to show the current condition of tyre installed on vehicle",
		Period: &exceltmpl.ReportPeriod{
			Start: req.StartDate,
			End:   req.EndDate,
		},
		Columns: []exceltmpl.ReportColumn{
			{Name: "No"},                     // 0
			{Name: "Date of Rotation"},       // 1
			{Name: "Asset ID"},               // 2
			{Name: "Customer Name"},          // 3
			{Name: "Asset Subcategory"},      // 4
			{Name: "Tyre Serial Number"},     // 5
			{Name: "RFID / Other ID"},        // 6
			{Name: "Brand"},                  // 7
			{Name: "Pattern / Type"},         // 8
			{Name: "Tyre Size"},              // 9
			{Name: "Ply Rating"},             // 10
			{Name: "Date Code"},              // 11
			{Name: "Current Tyre Position"},  // 12
			{Name: "Previous Tyre Position"}, // 13
			{Name: "OTD (mm)", Style: exceltmpl.TableFloatNumberValueStyle},                // 14
			{Name: "Average RTD (mm)", Style: exceltmpl.TableFloatNumberValueStyle},        // 15
			{Name: "TUR%", Style: exceltmpl.TableFloatNumberValueStyle},                    // 16
			{Name: "Total KM", Style: exceltmpl.TableFloatNumberValueStyle},                // 17
			{Name: "Total HM", Style: exceltmpl.TableFloatNumberValueStyle},                // 18
			{Name: "Projected Life KM", Style: exceltmpl.TableFloatNumberValueStyle},       // 19
			{Name: "Projected Life HM", Style: exceltmpl.TableFloatNumberValueStyle},       // 20
			{Name: "Tread Wear Rate (km/mm)", Style: exceltmpl.TableFloatNumberValueStyle}, // 21
			{Name: "Tread Wear Rate (h/mm)", Style: exceltmpl.TableFloatNumberValueStyle},  // 22
		},
		Rows: make([][]any, len(resp)),
	}

	if hideCustomer {
		templ.Columns = append(templ.Columns[:3], templ.Columns[4:]...)
	}

	for i, item := range resp {

		parentIdent := item.ParentAssetReferenceNumber
		if parentIdent == "" {
			parentIdent = item.ParentAssetSerialNumber
		} else if item.ParentAssetSerialNumber != "" {
			parentIdent += "/" + item.ParentAssetSerialNumber
		}

		templ.Rows[i] = []any{
			i + 1, // 0
			item.RotationDate.Local().Format("02 Jan 2006"), // 1
			parentIdent,                     // 2
			item.PartnerOwnerName,           // 3
			item.ParentAssetSubcategoryName, // 4
			item.SerialNumber,               // 5
			item.Rfid,                       // 6
			item.Brand,                      // 7
			item.PatternType,                // 8
			item.TyreSize,                   // 9
			item.PlyRating,                  // 10
			item.DateCode.String,            // 11
			item.PrevTyrePosition,           // 12
			item.NewTyrePosition,            // 13
			item.StartThreadDepth,           // 14
			item.AverageRtd,                 // 15
			item.UtilizationRatePercentage,  // 16
			item.TotalKM,                    // 17
			item.TotalHM,                    // 18
			item.ProjectedLifeKm,            // 19
			item.ProjectedLifeHm,            // 20
			item.TreadWearRateKm,            // 21
			item.TreadWearRateHm,            // 22
		}

		if hideCustomer {
			templ.Rows[i] = append(templ.Rows[i][:3], templ.Rows[i][4:]...)
		}

		isKM := item.MeterCalculationCode.String == constants.TYRE_METER_CALCULATION_CODE_KM
		isHM := item.MeterCalculationCode.String == constants.TYRE_METER_CALCULATION_CODE_HM
		isKMHM := item.MeterCalculationCode.String == constants.TYRE_METER_CALCULATION_CODE_KM_HM

		totalHMIdx := 18
		projectedLifeHMIdx := 20
		treadWearRateHMIdx := 22
		totalKMIdx := 17
		projectedLifeKMIdx := 19
		treadWearRateKMIdx := 21

		if hideCustomer {
			totalHMIdx = 17
			projectedLifeHMIdx = 19
			treadWearRateHMIdx = 21
			totalKMIdx = 16
			projectedLifeKMIdx = 18
			treadWearRateKMIdx = 20
		}

		if !isKMHM {
			if isKM {
				templ.Rows[i][totalHMIdx] = nil
				templ.Rows[i][projectedLifeHMIdx] = nil
				templ.Rows[i][treadWearRateHMIdx] = nil
			} else if isHM {
				templ.Rows[i][totalKMIdx] = nil
				templ.Rows[i][projectedLifeKMIdx] = nil
				templ.Rows[i][treadWearRateKMIdx] = nil
			}
		}

	}

	return templ
}
