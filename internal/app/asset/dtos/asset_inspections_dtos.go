package dtos

import (
	"assetfindr/pkg/common/commonmodel"

	"github.com/jackc/pgtype"
	"gopkg.in/guregu/null.v4"
)

type AssetInspectionDTO struct {
	ID                    string  `json:"id"`
	AssetID               string  `json:"asset_id"`
	Remark                string  `json:"remark"`
	AssetAssignmentID     string  `json:"asset_assignment_id"`
	VehicleKM             float64 `json:"vehicle_km"`
	VehicleHm             float64 `json:"vehicle_hm"`
	Pressure              float64 `json:"pressure"`
	RDT1                  float64 `json:"rdt1"`
	RDT2                  float64 `json:"rdt2"`
	RDT3                  float64 `json:"rdt3"`
	RDT4                  float64 `json:"rdt4"`
	TyrePosition          float64 `json:"tyre_position"`
	AverageRTD            float64 `json:"average_rtd"`
	AssetInspectionID     string  `json:"asset_inspections_id"`
	AssetInspectionTyreID string  `json:"asset_inspections_tyre_id"`
	PressureStatusCode    string  `json:"pressure_status_code"`

	Temperature null.Float `json:"temperature"`

	FailedVisualChecking   null.Bool `json:"failed_visual_checking"`
	RequireRotationTyre    null.Bool `json:"require_rotation_tyre"`
	RequireSpooringVehicle null.Bool `json:"require_spooring_vehicle"`
	CustomSerialNumber     string    `json:"custom_serial_number"`

	CustomBrandName null.String `json:"custom_brand_name"`
	CustomTyreSize  string      `json:"custom_tyre_size"`

	Photos             []commonmodel.PhotoReq `json:"photos"`
	DigispectVehicleID string                 `json:"digispect_vehicle_id"`
	PartnerOwnerName   string                 `json:"partner_owner_name"`

	AssetInspectionVehicleID string      `json:"asset_inspection_vehicle_id"`
	TireTreadAndRimDamage    null.Bool   `json:"tire_tread_and_rim_damage"`
	DeviceID                 string      `json:"device_id"`
	SourceTypeCode           null.String `json:"source_type_code"`

	AssetCategoryCode string `json:"asset_category_code"`

	AxleConfiguration       pgtype.JSONB `json:"axle_configuration"`
	MaxRtdDiffTolerance     null.Int     `json:"max_rtd_diff_tolerance"`
	CustomModelName         null.String  `json:"custom_model_name"`
	CustomReferenceNumber   null.String  `json:"custom_reference_number"`
	NumberOfInspectionPoint int          `json:"number_of_inspection_point"`
	PressureSensorRef       string       `json:"pressure_sensor_ref"`
	TemperatureSensorRef    string       `json:"temperature_sensor_ref"`
	CustomRFID              string       `json:"custom_rfid"`
	DigispectConfigID       string       `json:"digispect_config_id"`
	Findings                []string     `json:"findings"`
	CustomPatternID         string       `json:"custom_pattern_id"`
}
