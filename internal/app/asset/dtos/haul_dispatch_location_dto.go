package dtos

import (
	"assetfindr/internal/app/asset/models"

	"gopkg.in/guregu/null.v4"
)

type HaulDispatchLocation struct {
	AssetID                  string      `json:"asset_id"`
	LoadLocationID           null.String `json:"load_location_id"`
	DumpLocationID           null.String `json:"dump_location_id"`
	DispatchLocationTypeCode string      `json:"dispatch_location_type_code"`

	LoadLocation *DispatchLocation `json:"load_location"`
	DumpLocation *DispatchLocation `json:"dump_location"`
}

func BuildHaulDispatchLocation(haulDispatchLocation *models.HaulDispatchLocation) *HaulDispatchLocation {
	if haulDispatchLocation == nil {
		return nil
	}

	resp := HaulDispatchLocation{
		AssetID:                  haulDispatchLocation.AssetID,
		LoadLocationID:           haulDispatchLocation.LoadLocationID,
		DumpLocationID:           haulDispatchLocation.DumpLocationID,
		DispatchLocationTypeCode: haulDispatchLocation.DispatchLocationTypeCode,
	}

	if haulDispatchLocation.LoadLocation != nil {
		resp.LoadLocation = &DispatchLocation{
			ID:   haulDispatchLocation.LoadLocation.ID,
			Name: haulDispatchLocation.LoadLocation.Name,
		}
	}

	if haulDispatchLocation.DumpLocation != nil {
		resp.DumpLocation = &DispatchLocation{
			ID:   haulDispatchLocation.DumpLocation.ID,
			Name: haulDispatchLocation.DumpLocation.Name,
		}
	}

	return &resp
}

type DispatchLocation struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type UpsertHaulDispatchLocationReq struct {
	LoadLocationID           null.String `json:"load_location_id"`
	DumpLocationID           null.String `json:"dump_location_id"`
	DispatchLocationTypeCode string      `json:"dispatch_location_type_code"`
}
