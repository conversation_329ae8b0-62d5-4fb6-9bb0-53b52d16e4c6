package dtos

import (
	"assetfindr/internal/app/asset/models"
	"assetfindr/pkg/common/commonmodel"
	"time"

	"gopkg.in/guregu/null.v4"
)

type CreateUpdateAssetTransaction struct {
	AssetID             string                    `json:"asset_id" binding:"required"`
	PartnerID           string                    `json:"vendor_id"`
	PurchaseOrderDate   string                    `json:"purchase_order_date"`
	PurchaseOrderNumber null.String               `json:"purchase_order_number"`
	InvoiceDate         string                    `json:"invoice_date"`
	InvoiceNumber       null.String               `json:"invoice_number"`
	ServiceStartDate    string                    `json:"service_start_date"`
	ServiceEndDate      string                    `json:"service_end_date"`
	ReferenceNumber     string                    `json:"reference_number"`
	StatusCode          string                    `json:"status_code"`
	TypeCode            string                    `json:"type_code"`
	Cost                int                       `json:"cost"`
	AssignedToUserID    string                    `json:"assigned_to_user_id"`
	TaxCost             int                       `json:"tax_cost"`
	DiscountAmount      int                       `json:"discount_amount"`
	OtherCost           int                       `json:"other_cost"`
	SubTotal            int                       `json:"sub_total"`
	Notes               null.String               `json:"notes"`
	Photos              []commonmodel.PhotoReq    `json:"photos"`
	Items               []AssetTransactionItemReq `json:"items"`
	CategoryCode        string                    `json:"category_code"`
	Location            string                    `json:"location"`
	Odometer            float64                   `json:"odometer"`
	// PaymentMethodCode   string                    `json:"payment_method_code"`
	ExpiryReminderDate        string `json:"expiry_reminder_date"`
	IsCreateWorkOrderOnExpiry bool   `json:"is_create_work_order_on_expiry"`
}

type AssetTransactionItemReq struct {
	ID        string `json:"id"`
	Name      string `json:"name"`
	UnitPrice int    `json:"unit_price"`
	Quantity  int    `json:"quantity"`
	IsDelete  bool   `json:"is_delete"`
}

func (r *AssetTransactionItemReq) IsNew() bool {
	return r.ID == ""
}

type AssetTransactionItemResp struct {
	ID        string `json:"id"`
	Name      string `json:"name"`
	UnitPrice int    `json:"unit_price"`
	Quantity  int    `json:"quantity"`
}

type ResponseAssetTransaction struct {
	ID                     string                          `json:"id"`
	CreatedAt              time.Time                       `json:"created_at"`
	PartnerID              string                          `json:"vendor_id"`
	PartnerName            string                          `json:"vendor_name"`
	PurchaseOrderDate      *null.Time                      `json:"purchase_order_date"`
	PurchaseOrderNumber    null.String                     `json:"purchase_order_number"`
	InvoiceDate            time.Time                       `json:"invoice_date"`
	InvoiceNumber          null.String                     `json:"invoice_number"`
	ServiceStartDate       time.Time                       `json:"service_start_date,omitempty"`
	ServiceEndDate         time.Time                       `json:"service_end_date,omitempty"`
	ReferenceNumber        string                          `json:"reference_number,omitempty"`
	StatusCode             string                          `json:"status_code"`
	Status                 models.AssetTransactionStatus   `json:"status"`
	TypeCode               string                          `json:"type_code"`
	Type                   models.AssetTransactionType     `json:"type"`
	Cost                   int                             `json:"cost"`
	AssignedToUserID       string                          `json:"assigned_to_user_id"`
	AssignedToUserFullname string                          `json:"assigned_to_user_fullname,omitempty"`
	TaxCost                int                             `json:"tax_cost"`
	DiscountAmount         int                             `json:"discount_amount"`
	OtherCost              int                             `json:"other_cost"`
	SubTotal               int                             `json:"sub_total"`
	Notes                  null.String                     `json:"notes"`
	Items                  []AssetTransactionItemResp      `json:"items"`
	Quantity               int                             `json:"quantity"`
	CategoryCode           string                          `json:"category_code"`
	Category               models.AssetTransactionCategory `json:"category"`
	Location               string                          `json:"location"`
	Odometer               float64                         `json:"odometer"`
	// PaymentMethodCode      string                          `json:"payment_method_code"`
	// PaymentMethod          models.AssetTransactionMethod   `json:"payment_method"`
	ExpiryReminderDate        time.Time `json:"expiry_reminder_date"`
	IsCreateWorkOrderOnExpiry bool      `json:"is_create_work_order_on_expiry"`
}

func (r *ResponseAssetTransaction) Set(assetTransaction models.AssetTransaction, userName string, partnerName string) {
	r.ID = assetTransaction.ID
	r.CreatedAt = assetTransaction.CreatedAt
	r.PartnerID = assetTransaction.PartnerID
	r.PartnerName = partnerName
	r.PurchaseOrderDate = assetTransaction.PurchaseOrderDate
	r.PurchaseOrderNumber = assetTransaction.PurchaseOrderNumber
	r.InvoiceDate = assetTransaction.InvoiceDate
	r.InvoiceNumber = assetTransaction.InvoiceNumber
	if !assetTransaction.ServiceStartDate.IsZero() {
		r.ServiceStartDate = assetTransaction.ServiceStartDate
	}
	if !assetTransaction.ServiceEndDate.IsZero() {
		r.ServiceEndDate = assetTransaction.ServiceEndDate
	}
	if assetTransaction.ReferenceNumber != "" {
		r.ReferenceNumber = assetTransaction.ReferenceNumber
	}
	r.StatusCode = assetTransaction.StatusCode
	r.Status = assetTransaction.Status
	r.TypeCode = assetTransaction.TypeCode
	r.Type = assetTransaction.Type
	r.Cost = assetTransaction.Cost
	r.AssignedToUserID = assetTransaction.AssignedToUserID
	r.AssignedToUserFullname = userName
	r.TaxCost = assetTransaction.TaxCost
	r.DiscountAmount = assetTransaction.DiscountAmount
	r.OtherCost = assetTransaction.OtherCost
	r.SubTotal = assetTransaction.SubTotal
	r.Notes = assetTransaction.Notes
	r.CategoryCode = assetTransaction.CategoryCode
	// r.PaymentMethodCode = assetTransaction.PaymentMethodCode
	r.Location = assetTransaction.Location
	r.Odometer = assetTransaction.Odometer

	if r.TypeCode == "EXPENSE" {
		for _, atItems := range assetTransaction.AssetTransactionItems {
			r.Quantity += atItems.Quantity
		}

		r.Category = assetTransaction.Category
		// r.PaymentMethod = assetTransaction.PaymentMethod
	}

	if assetTransaction.ExpiryReminderDate != nil && assetTransaction.ExpiryReminderDate.Valid {
		r.ExpiryReminderDate = assetTransaction.ExpiryReminderDate.Time
	}
	if assetTransaction.IsCreateWorkOrderOnExpiry != nil && assetTransaction.IsCreateWorkOrderOnExpiry.Valid {
		r.IsCreateWorkOrderOnExpiry = assetTransaction.IsCreateWorkOrderOnExpiry.Bool
	}
}

type AssetTransactionListReq struct {
	commonmodel.ListRequest
	AssetID string `form:"asset_id"`
}

type SendNearExpiryAssetTransactionNotificationsReq struct {
	Type string `form:"type"`
}
