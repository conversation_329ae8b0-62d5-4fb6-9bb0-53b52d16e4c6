package dtos

import (
	"assetfindr/pkg/common/commonmodel"
	"strconv"
	"time"
)

type UpdateAssetLinkedTyrePositionItem struct {
	AssetLinkedID string `json:"asset_linked_id"`
	TyrePosition  int    `json:"tyre_position"`
}

type UpdateAssetLinkedTyrePositionReq struct {
	LinkedAssets []UpdateAssetLinkedTyrePositionItem `json:"linked_assets"`

	// For Past Linked
	Datetime  time.Time `json:"datetime"`
	VehicleKM int       `json:"vehicle_km"`
	VehicleHm float64   `json:"vehicle_hm"`

	// For Next Linked with Custom Datetime
	IsCustomStateDateTime bool `json:"is_custom_state_date_time"`
}

type AssetLinkedListReq struct {
	commonmodel.ListRequest
	SerialNumber      string   `form:"serial_number"`
	BrandIDs          []string `form:"brand_ids"`
	LinkedStartDate   string   `form:"linked_start_date"`
	LinkedEndDate     string   `form:"linked_end_date"`
	UnlinkedStartDate string   `form:"unlinked_start_date"`
	UnlinkedEndDate   string   `form:"unlinked_end_date"`
	LinkedByUserIDs   []string `form:"linked_by_user_ids"`
	UnlinkedByUserIDs []string `form:"unlinked_by_user_ids"`
}

type BulkUploadAssetLinkedVehicleTyreReq struct {
	ReferenceID               string `csv:"reference_id"`
	IsSuccess                 bool   `csv:"is_success"`
	FailedReason              string `csv:"failed_reason"`
	IsValidToProcess          bool   `csv:"-"`
	AssetVehicleID            string `csv:"-"`
	VehicleRegistrationNumber string `csv:"vehicle_registration_number"`
	AssetTyreID               string `csv:"-"`
	TyreSerialNumber          string `csv:"tyre_serial_number"`
	TyrePositionString        string `csv:"tyre_position"`
	TyrePosition              int    `csv:"-"`
}

func (r *BulkUploadAssetLinkedVehicleTyreReq) ValidateTyrePosition(data map[string][]int) {
	var err error
	r.TyrePosition, err = strconv.Atoi(r.TyrePositionString)
	if err != nil {
		r.TyrePositionString += " (invalid number, must be integer)"
		r.IsValidToProcess = false
		return
	}

	_, ok := data[r.AssetVehicleID]
	if ok {
		for _, val := range data[r.AssetVehicleID] {
			if val == r.TyrePosition {
				r.TyrePositionString += " (duplicate)"
				r.IsValidToProcess = false
				return
			}
		}

		data[r.AssetVehicleID] = append(data[r.AssetVehicleID], r.TyrePosition)
		return
	}

	data[r.AssetVehicleID] = []int{r.TyrePosition}
}

func (r *BulkUploadAssetLinkedVehicleTyreReq) ValidateVehicle(data map[string]string) {
	id, ok := data[r.VehicleRegistrationNumber]
	if !ok {
		r.VehicleRegistrationNumber += " (not found)"
		r.IsValidToProcess = false
	} else {
		r.AssetVehicleID = id
	}
}

func (r *BulkUploadAssetLinkedVehicleTyreReq) ValidateVehicleAlreadyLinked(data map[string]bool) {
	if r.AssetVehicleID == "" {
		return
	}

	ok := data[r.AssetVehicleID]
	if ok {
		r.VehicleRegistrationNumber += " (vehicle already linked before)"
		r.IsValidToProcess = false
	}
}

func (r *BulkUploadAssetLinkedVehicleTyreReq) ValidateTyre(data map[string]string) {
	id, ok := data[r.TyreSerialNumber]
	if !ok {
		r.TyreSerialNumber += " (not found, or status not valid)"
		r.IsValidToProcess = false
	} else {
		r.AssetTyreID = id
	}
}

func (r *BulkUploadAssetLinkedVehicleTyreReq) ValidateTyreAlreadyLinked(data map[string]bool) {
	if r.AssetTyreID == "" {
		return
	}

	ok := data[r.AssetTyreID]
	if ok {
		r.TyreSerialNumber += " (tyre already linked before, or duplicated)"
		r.IsValidToProcess = false
		return
	}

	data[r.AssetTyreID] = true
}
