package persistence

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonmodel"
	"context"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type AssetInspectionVehicleRepository struct {
}

func NewAssetInspectionVehicleRepository() repository.AssetInspectionVehicleRepository {
	return &AssetInspectionVehicleRepository{}
}

func enrichAssetInspectionVehicleQueryWithWhere(query *gorm.DB, where models.AssetInspectionVehicleWhere) {
	if where.ClientID != "" {
		query.Where("ams_asset_inspection_vehicle.client_id = ?", where.ClientID)
	} // ClientID

	if where.StartDate != "" {
		query.Where("ams_asset_inspection_vehicle.created_at >= ?", where.StartDate)
	} // StartDate

	if where.EndDate != "" {
		query.Where("ams_asset_inspection_vehicle.created_at < ?", where.EndDate)
	} // EndDate

	if where.ReferenceCode != "" {
		query.Where("ams_asset_inspections.reference_code = ? AND ams_asset_inspections.reference_id = ?", where.ReferenceCode, where.ReferenceID)
	} // ReferenceCode

	if where.InspectionID != "" {
		query.Where("ams_asset_inspection_vehicle.asset_inspection_id = ?", where.InspectionID)
	} // InspectionID

	if where.AssetID != "" {
		query.Where("ams_asset_inspection_vehicle.asset_vehicle_id = ?", where.AssetID)
	} // AssetID

	if len(where.AssetIDs) > 0 {
		query.Where("ams_asset_inspection_vehicle.asset_vehicle_id IN ?", where.AssetIDs)
	} // AssetIDs

	if len(where.InspectByUserIDs) > 0 {
		query.Where("ams_asset_inspections.inspect_by_user_id IN ?", where.InspectByUserIDs)
	} // InspectByUserIDs

	if where.HasPartnerOwnerID {
		query.Where("ams_assets.partner_owner_id IS NOT NULL")
	}

	if len(where.DigispectConfIDs) > 0 {
		query.Where("ams_asset_inspection_vehicle.digispect_config_id IN ?", where.DigispectConfIDs)
	}

	if where.VehicleStateID != "" {
		query.Where("ams_asset_inspection_vehicle.vehicle_state_id = ?", where.VehicleStateID)
	} // VehicleStateID

}

func enrichAssetInspectionVehicleQueryWithPreload(query *gorm.DB, preload models.AssetInspectionVehiclePreload) {
	if preload.AssetInspection {
		query.Preload("AssetInspection")
	}

	if preload.AssetVehicle {
		query.Preload("AssetVehicle")
	}

	if preload.Asset {
		query.Preload("Asset")
	} // Asset

	if preload.Asset {
		query.Preload("Asset.Brand")
	} // Asset Brand

	if preload.AssetLocation {
		query.Preload("Asset.Location")
	} // Asset Location

	if preload.AssetVehicleAsset {
		query.Preload("AssetVehicle.Asset")
	}

	if preload.AssetVehicleVehicle {
		query.Preload("AssetVehicle.Vehicle")
	}

	if preload.AssetInspectionTyres {
		query.Preload("AssetInspectionTyres")
	} // AssetInspectionTyres

	if preload.AssetInspectionTyresAssetTyre {
		query.Preload("AssetInspectionTyres.AssetTyre")
	} // AssetInspectionTyresAssetTyre

	if preload.AssetVehicleVehicleBrand {
		query.Preload("AssetVehicle.Vehicle.Brand")
	}

	if preload.DigispectVehicle {
		query.Preload("DigispectVehicle")
	}

	if preload.AssetInspectionFindingMappings {
		query.Preload("AssetInspectionFindingMappings")
	}
}

func (r *AssetInspectionVehicleRepository) GetAssetInspectionVehicleList(ctx context.Context, dB database.DBI, param models.GetAssetInspectionVehicleListParam) (int, []models.AssetInspectionVehicle, error) {
	var totalRecords int64
	assetInspectionVehicles := []models.AssetInspectionVehicle{}
	query := dB.GetTx().Model(&assetInspectionVehicles)

	query.
		Joins("JOIN ams_asset_inspections ON ams_asset_inspections.id=ams_asset_inspection_vehicle.asset_inspection_id").
		Joins("LEFT JOIN ams_assets ON ams_assets.id=ams_asset_inspection_vehicle.asset_vehicle_id")

	enrichAssetInspectionVehicleQueryWithWhere(query, param.Cond.Where)
	enrichAssetInspectionVehicleQueryWithPreload(query, param.Cond.Preload)

	if param.SearchKeyword != "" {
		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().
				Where("LOWER(ams_asset_inspections.inspection_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_assets.serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_assets.reference_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
		)
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), assetInspectionVehicles, nil
	}

	query.Order("ams_asset_inspection_vehicle.updated_at DESC")

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&assetInspectionVehicles).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), assetInspectionVehicles, nil
}

func (r *AssetInspectionVehicleRepository) GetAssetInspectionVehicle(ctx context.Context, dB database.DBI, cond models.AssetInspectionVehicleCondition) (*models.AssetInspectionVehicle, error) {
	assetInspectionVehicle := models.AssetInspectionVehicle{}
	query := dB.GetTx().Model(&assetInspectionVehicle)

	query.
		Joins("JOIN ams_asset_inspections ON ams_asset_inspections.id=ams_asset_inspection_vehicle.asset_inspection_id").
		Joins("LEFT JOIN ams_assets ON ams_assets.id=ams_asset_inspection_vehicle.asset_vehicle_id")

	enrichAssetInspectionVehicleQueryWithWhere(query, cond.Where)
	enrichAssetInspectionVehicleQueryWithPreload(query, cond.Preload)

	err := query.First(&assetInspectionVehicle).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("inspection vehicle")
		}
		return nil, err
	}

	return &assetInspectionVehicle, nil
}

func (r *AssetInspectionVehicleRepository) CreateAssetInspectionVehicle(ctx context.Context, dB database.DBI, assetInspectionVehicle *models.AssetInspectionVehicle) error {
	return dB.GetTx().Create(assetInspectionVehicle).Error
}

func (r *AssetInspectionVehicleRepository) UpdateAssetInspectionVehicle(ctx context.Context, dB database.DBI, id string, assetInspectionVehicle *models.AssetInspectionVehicle) error {
	return dB.GetTx().
		Model(&models.AssetInspectionVehicle{}).
		Where("id = ?", id).
		Updates(assetInspectionVehicle).
		Error
}

func (r *AssetInspectionVehicleRepository) GetAssetInspectionVehiclesByIds(ctx context.Context, dB database.DBI, vehicles *[]models.AssetInspectionVehicle, ids []string) error {
	query := dB.GetTx().Model(&models.AssetInspectionVehicle{})
	if err := query.Where("asset_inspection_id IN ?", ids).Preload("AssetVehicle").Preload("AssetVehicle.Asset").Find(&vehicles).Error; err != nil {
		return err
	}

	return nil
}

func (r *AssetInspectionVehicleRepository) GetLatestAssetInspectionVehicle(ctx context.Context, dB database.DBI, cond models.AssetInspectionVehicleCondition) (*models.AssetInspectionVehicle, error) {
	assetInspectionVehicle := models.AssetInspectionVehicle{}
	query := dB.GetTx().Model(&models.AssetInspectionVehicle{})

	query.
		Joins("JOIN ams_asset_inspections ON ams_asset_inspections.id=ams_asset_inspection_vehicle.asset_inspection_id").
		Joins("LEFT JOIN ams_assets ON ams_assets.id=ams_asset_inspection_vehicle.asset_vehicle_id")

	enrichAssetInspectionVehicleQueryWithWhere(query, cond.Where)
	enrichAssetInspectionVehicleQueryWithPreload(query, cond.Preload)

	query.Order("ams_asset_inspection_vehicle.created_at DESC")
	err := query.First(&assetInspectionVehicle).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("ASSET_INSPECTION_VEHICLE")
		}

		return nil, err
	}

	return &assetInspectionVehicle, nil
}

func (r *AssetInspectionVehicleRepository) ChartVehicleInspectionPerDate(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.AssetInspectionVehicle{}).
		Joins("JOIN ams_asset_inspections ON ams_asset_inspections.id = ams_asset_inspection_vehicle.asset_inspection_id")

	enrichAssetInspectionQueryWithWhere(query, models.AssetInspectionWhere{
		ClientID:   req.ClientID,
		StartDate1: req.StartDatetime,
		EndDate1:   req.EndDatetime,
	})

	if req.IsFromDigiSpect {
		query.Where("ams_asset_inspection_vehicle.source_type_code IN (?)", []string{
			constants.INSPECTION_SOURCE_TRANSLOGIC_APP,
			constants.INSPECTION_SOURCE_ASSETFINDR_APP,
		})
	}

	query.Select(
		"DATE(ams_asset_inspections.created_at) AS name",
		"COUNT(DISTINCT ams_asset_inspection_vehicle.custom_reference_number) AS y",
	)

	query.Group("DATE(ams_asset_inspections.created_at)")
	query.Order("name ASC")

	var charts []commonmodel.Chart
	err := query.Scan(&charts).Error
	if err != nil {
		return nil, err
	}

	return charts, nil
}

func (r *AssetInspectionVehicleRepository) ChartAssetVehicleAndCustomerInspectionsPerDate(ctx context.Context, dB database.DBI, req models.GetChartAssetVehicleCustomerInspectionPerDateReq) ([]models.ChartAssetVehicleCustomerInspectionPerDate, error) {
	query := dB.GetTx().Model(&models.AssetInspectionVehicle{}).
		Joins("JOIN ams_asset_inspections ON ams_asset_inspections.id = ams_asset_inspection_vehicle.asset_inspection_id")

	enrichAssetInspectionQueryWithWhere(query, models.AssetInspectionWhere{
		ClientID:   req.ClientID,
		StartDate1: req.StartDatetime,
		EndDate1:   req.EndDatetime,
	})

	if len(req.PartnerOwnerIDs) > 0 {
		query.Where("ams_asset_inspection_vehicle.partner_owner_id IN ?", req.PartnerOwnerIDs)
	}

	query.Where("ams_asset_inspection_vehicle.asset_vehicle_id IS NOT NULL")

	query.Select(
		`DATE(ams_asset_inspections.created_at) AS "date"`,
		"COUNT(DISTINCT ams_asset_inspections.id) AS num_inspections",
		"COUNT(DISTINCT ams_asset_inspection_vehicle.partner_owner_id) AS num_customers",
	)

	query.Group("DATE(ams_asset_inspections.created_at)")
	query.Order(`"date" ASC`)

	var charts []models.ChartAssetVehicleCustomerInspectionPerDate
	err := query.Scan(&charts).Error
	if err != nil {
		return nil, err
	}

	return charts, nil
}

func (r *AssetInspectionVehicleRepository) ChartTop5VehicleBrands(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.AssetInspectionVehicle{}).
		Joins("JOIN ams_asset_inspections ON ams_asset_inspections.id = ams_asset_inspection_vehicle.asset_inspection_id")

	enrichAssetInspectionQueryWithWhere(query, models.AssetInspectionWhere{
		ClientID:   req.ClientID,
		StartDate1: req.StartDatetime,
		EndDate1:   req.EndDatetime,
	})

	if req.IsFromDigiSpect {
		query.Where("ams_asset_inspection_vehicle.source_type_code IN (?)", []string{
			constants.INSPECTION_SOURCE_TRANSLOGIC_APP,
			constants.INSPECTION_SOURCE_ASSETFINDR_APP,
		})
	}

	// Filter out empty brand names
	query.Where("ams_asset_inspection_vehicle.custom_brand_name IS NOT NULL AND ams_asset_inspection_vehicle.custom_brand_name != ''")

	// Group by brand name, count occurrences
	query.Select(
		"ams_asset_inspection_vehicle.custom_brand_name AS name",
		"COUNT(*) AS y",
	)

	query.Group("ams_asset_inspection_vehicle.custom_brand_name")
	query.Order("y DESC")
	query.Limit(5)

	var charts []commonmodel.Chart
	err := query.Scan(&charts).Error
	if err != nil {
		return nil, err
	}

	return charts, nil
}

func (r *AssetInspectionVehicleRepository) ChartNumbersOfVehicleInspectedAssetVsDigispect(ctx context.Context, dB database.DBI, req models.ChartNumbersOfVehicleInspectedAssetVsDigispectReq) ([]models.ChartNumbersOfVehicleInspectedAssetVsDigispect, error) {
	// First, get all vehicles with their inspection counts for asset vehicles
	assetVehicleQuery := dB.GetTx().Model(&models.AssetInspectionVehicle{}).
		Joins("JOIN ams_asset_inspections ON ams_asset_inspections.id = ams_asset_inspection_vehicle.asset_inspection_id")

	enrichAssetInspectionQueryWithWhere(assetVehicleQuery, models.AssetInspectionWhere{
		ClientID:   req.ClientID,
		StartDate1: req.StartDatetime,
		EndDate1:   req.EndDatetime,
	})

	// Filter for asset vehicles (non-digispect)
	assetVehicleQuery.Where("ams_asset_inspection_vehicle.asset_vehicle_id IS NOT NULL")

	assetVehicleQuery.Select(
		"ams_asset_inspection_vehicle.asset_vehicle_id AS vehicle_id",
		"COUNT(*) AS inspection_count",
	)
	assetVehicleQuery.Group("ams_asset_inspection_vehicle.asset_vehicle_id")

	// Get all vehicles with their inspection counts for digispect vehicles
	digispectVehicleQuery := dB.GetTx().Model(&models.AssetInspectionVehicle{}).
		Joins("JOIN ams_asset_inspections ON ams_asset_inspections.id = ams_asset_inspection_vehicle.asset_inspection_id")

	enrichAssetInspectionQueryWithWhere(digispectVehicleQuery, models.AssetInspectionWhere{
		ClientID:   req.ClientID,
		StartDate1: req.StartDatetime,
		EndDate1:   req.EndDatetime,
	})

	// Filter for digispect vehicles
	digispectVehicleQuery.Where("ams_asset_inspection_vehicle.digispect_vehicle_id IS NOT NULL")

	digispectVehicleQuery.Select(
		"ams_asset_inspection_vehicle.digispect_vehicle_id AS vehicle_id",
		"COUNT(*) AS inspection_count",
	)
	digispectVehicleQuery.Group("ams_asset_inspection_vehicle.digispect_vehicle_id")

	// Create frequency categorization query for asset vehicles
	assetFrequencyQuery := dB.GetTx().Table("(?) AS asset_counts", assetVehicleQuery)
	assetFrequencyQuery.Select(
		`CASE
			WHEN inspection_count = 1 THEN '1'
			WHEN inspection_count = 2 THEN '2'
			WHEN inspection_count >= 3 AND inspection_count <= 5 THEN '3-5'
			ELSE '>5'
		END AS name`,
		`CASE
			WHEN inspection_count = 1 THEN 'ONE'
			WHEN inspection_count = 2 THEN 'TWO'
			WHEN inspection_count >= 3 AND inspection_count <= 5 THEN 'THREE_TO_FIVE'
			ELSE 'MORE_THAN_FIVE'
		END AS code`,
		"COUNT(*) AS num_asset_vehicles",
		"0 AS num_digispect_vehicles",
	)
	assetFrequencyQuery.Group("name, code")

	// Create frequency categorization query for digispect vehicles
	digispectFrequencyQuery := dB.GetTx().Table("(?) AS digispect_counts", digispectVehicleQuery)
	digispectFrequencyQuery.Select(
		`CASE
			WHEN inspection_count = 1 THEN '1'
			WHEN inspection_count = 2 THEN '2'
			WHEN inspection_count >= 3 AND inspection_count <= 5 THEN '3-5'
			ELSE '>5'
		END AS name`,
		`CASE
			WHEN inspection_count = 1 THEN 'ONE'
			WHEN inspection_count = 2 THEN 'TWO'
			WHEN inspection_count >= 3 AND inspection_count <= 5 THEN 'THREE_TO_FIVE'
			ELSE 'MORE_THAN_FIVE'
		END AS code`,
		"0 AS num_asset_vehicles",
		"COUNT(*) AS num_digispect_vehicles",
	)
	digispectFrequencyQuery.Group("name, code")

	// Union both queries and aggregate
	unionQuery := dB.GetTx().Raw("(?) UNION ALL (?)", assetFrequencyQuery, digispectFrequencyQuery)

	finalQuery := dB.GetTx().Table("(?) AS combined", unionQuery)
	finalQuery.Select(
		"name",
		"code",
		"SUM(num_asset_vehicles) AS num_asset_vehicles",
		"SUM(num_digispect_vehicles) AS num_digispect_vehicles",
	)
	finalQuery.Group("name, code")
	finalQuery.Order("CASE code WHEN 'ONE' THEN 1 WHEN 'TWO' THEN 2 WHEN 'THREE_TO_FIVE' THEN 3 WHEN 'MORE_THAN_FIVE' THEN 4 END")

	var results []models.ChartNumbersOfVehicleInspectedAssetVsDigispect
	err := finalQuery.Scan(&results).Error
	if err != nil {
		return nil, err
	}

	// Add default entries for missing frequency categories to ensure all categories are present
	frequencyMap := make(map[string]*models.ChartNumbersOfVehicleInspectedAssetVsDigispect)
	for i := range results {
		frequencyMap[results[i].Code] = &results[i]
	}

	// Define all expected frequency categories
	expectedCategories := []models.ChartNumbersOfVehicleInspectedAssetVsDigispect{
		{Name: "Never", Code: "NEVER", NumAssetVehicles: 0, NumDigispectVehicles: 0},
		{Name: "1", Code: "ONE", NumAssetVehicles: 0, NumDigispectVehicles: 0},
		{Name: "2", Code: "TWO", NumAssetVehicles: 0, NumDigispectVehicles: 0},
		{Name: "3-5", Code: "THREE_TO_FIVE", NumAssetVehicles: 0, NumDigispectVehicles: 0},
		{Name: ">5", Code: "MORE_THAN_FIVE", NumAssetVehicles: 0, NumDigispectVehicles: 0},
	}

	// Merge results with expected categories
	finalResults := make([]models.ChartNumbersOfVehicleInspectedAssetVsDigispect, 0, len(expectedCategories))
	for _, expected := range expectedCategories {
		if existing, exists := frequencyMap[expected.Code]; exists {
			finalResults = append(finalResults, *existing)
		} else {
			finalResults = append(finalResults, expected)
		}
	}

	return finalResults, nil
}

func (r *AssetInspectionVehicleRepository) ChartInspectionsOnDigispectVehiclePerDate(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.AssetInspectionVehicle{}).
		Joins("JOIN ams_asset_inspections ON ams_asset_inspections.id = ams_asset_inspection_vehicle.asset_inspection_id")

	enrichAssetInspectionQueryWithWhere(query, models.AssetInspectionWhere{
		ClientID:   req.ClientID,
		StartDate1: req.StartDatetime,
		EndDate1:   req.EndDatetime,
	})

	query.Where("ams_asset_inspection_vehicle.digispect_vehicle_id IS NOT NULL")

	query.Select(
		"DATE(ams_asset_inspections.created_at) AS name",
		"COUNT(ams_asset_inspections.id) AS y",
	)

	query.Group("DATE(ams_asset_inspections.created_at)")
	query.Order("name ASC")

	var charts []commonmodel.Chart
	err := query.Scan(&charts).Error
	if err != nil {
		return nil, err
	}

	return charts, nil
}

func (r *AssetInspectionVehicleRepository) ChartNumberOfCustomersByInspectionFreq(ctx context.Context, dB database.DBI, req models.ChartNumberOfCustomersByInspectionFreqReq) ([]commonmodel.Chart, error) {
	subQuery := dB.GetTx().Model(&models.AssetInspectionVehicle{}).
		Joins("JOIN ams_asset_inspections ON ams_asset_inspections.id = ams_asset_inspection_vehicle.asset_inspection_id")

	enrichAssetInspectionQueryWithWhere(subQuery, models.AssetInspectionWhere{
		ClientID:   req.ClientID,
		StartDate1: req.StartDatetime,
		EndDate1:   req.EndDatetime,
	})

	if len(req.PartnerOwnerIDs) > 0 {
		subQuery.Where("ams_asset_inspection_vehicle.partner_owner_id IN (?)", req.PartnerOwnerIDs)
	}

	subQuery.Where("ams_asset_inspection_vehicle.partner_owner_id IS NOT NULL AND ams_asset_inspection_vehicle.partner_owner_id != ''")

	subQuery.Select(
		"ams_asset_inspection_vehicle.partner_owner_id",
		"COUNT(DISTINCT ams_asset_inspections.id) AS inspection_count",
	)
	subQuery.Group("ams_asset_inspection_vehicle.partner_owner_id")

	mainQuery := dB.GetTx().Table("(?) AS customer_counts", subQuery)

	mainQuery.Select(
		`CASE
			WHEN inspection_count = 0 THEN 'Never'
			WHEN inspection_count = 1 THEN '1'
			WHEN inspection_count = 2 THEN '2'
			WHEN inspection_count >= 3 AND inspection_count <= 5 THEN '3-5'
			ELSE '>5'
		END AS name`,
		`CASE
			WHEN inspection_count = 0 THEN 'NEVER'
			WHEN inspection_count = 1 THEN 'ONE'
			WHEN inspection_count = 2 THEN 'TWO'
			WHEN inspection_count >= 3 AND inspection_count <= 5 THEN 'THREE_TO_FIVE'
			ELSE 'MORE_THAN_FIVE'
		END AS code`,
		"COUNT(*) AS y",
	)

	mainQuery.Group("name, code")

	var charts []commonmodel.Chart
	err := mainQuery.Scan(&charts).Error
	if err != nil {
		return nil, err
	}

	// Ensure all categories are represented
	existingCodes := map[string]bool{}
	for _, chart := range charts {
		existingCodes[chart.Code.String] = true
	}

	requiredCodes := map[string]string{
		"NEVER":          "Never",
		"ONE":            "1",
		"TWO":            "2",
		"THREE_TO_FIVE":  "3-5",
		"MORE_THAN_FIVE": ">5",
	}

	finalCharts := []commonmodel.Chart{}
	// Add charts in the correct order
	for _, code := range []string{"NEVER", "ONE", "TWO", "THREE_TO_FIVE", "MORE_THAN_FIVE"} {
		found := false
		for _, chart := range charts {
			if chart.Code.String == code {
				finalCharts = append(finalCharts, chart)
				found = true
				break
			}
		}
		if !found {
			finalCharts = append(finalCharts, commonmodel.Chart{
				Name: requiredCodes[code],
				Code: null.StringFrom(code),
				Y:    0,
			})
		}
	}

	return finalCharts, nil
}

func (r *AssetInspectionVehicleRepository) CountChartInspectionVehicles(ctx context.Context, dB database.DBI, req models.ChartTotalInspectionsReq) (int, error) {
	query := dB.GetTx().Model(&models.AssetInspectionVehicle{}).
		Joins("JOIN ams_asset_inspections ON ams_asset_inspections.id = ams_asset_inspection_vehicle.asset_inspection_id")

	enrichAssetInspectionQueryWithWhere(query, models.AssetInspectionWhere{
		ClientID:   req.ClientID,
		StartDate1: req.StartDatetime,
		EndDate1:   req.EndDatetime,
	})

	if req.IsHasDigispectVehicleID {
		query.Where("ams_asset_inspection_vehicle.digispect_vehicle_id IS NOT NULL")
	}

	if req.IsHasAssetVehicleID {
		query.Where("ams_asset_inspection_vehicle.asset_vehicle_id IS NOT NULL")
	}

	var count int64
	err := query.Count(&count).Error
	if err != nil {
		return 0, err
	}

	return int(count), nil
}
