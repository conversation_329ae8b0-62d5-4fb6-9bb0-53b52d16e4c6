package persistence

import (
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type haulRepository struct{}

func NewHaulRepository() *haulRepository {
	return &haulRepository{}
}

func (r *haulRepository) GetDriverLoginSession(ctx context.Context, dB database.DBI, cond models.DriverLoginSessionCondition) (*models.DriverLoginSession, error) {
	driverLoginSession := &models.DriverLoginSession{}
	query := dB.GetTx().Model(driverLoginSession)

	enrichDriverLoginSessionQueryWithWhere(query, cond.Where)

	err := query.First(driverLoginSession).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("driver login session")
		}

		return nil, err
	}

	return driverLoginSession, nil
}

func (r *haulRepository) GetDriverLoginSessionList(ctx context.Context, dB database.DBI, param models.GetDriverLoginSessionListParam) (int, []models.DriverLoginSession, error) {
	var totalRecords int64
	driverLoginSessions := []models.DriverLoginSession{}
	query := dB.GetTx().Model(&driverLoginSessions)

	enrichDriverLoginSessionQueryWithWhere(query, param.Cond.Where)
	enrichDriverLoginSessionQueryWithPreload(query, param.Cond.Preload)

	if param.SearchKeyword != "" {
		// Join to assets
		query.Joins("LEFT JOIN ams_assets ON ams_assets.id = ams_driver_login_sessions.asset_id")

		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().
				Where("LOWER(ams_driver_login_sessions.user_full_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_assets.reference_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_assets.serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
		)
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&driverLoginSessions).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), driverLoginSessions, nil
}

func enrichDriverLoginSessionQueryWithWhere(query *gorm.DB, where models.DriverLoginSessionWhere) {
	if where.ID != "" {
		query.Where("ams_driver_login_sessions.id = ?", where.ID)
	} // ID

	if where.AssetID != "" {
		query.Where("ams_driver_login_sessions.asset_id = ?", where.AssetID)
	} // AssetID

	if where.UserID != "" {
		query.Where("ams_driver_login_sessions.user_id = ?", where.UserID)
	} // UserID

	if where.ClientID != "" {
		query.Where("ams_driver_login_sessions.client_id = ?", where.ClientID)
	} // ClientID

	if where.IsActive.Valid {
		if where.IsActive.Bool {
			query.Where("ams_driver_login_sessions.logged_out_time IS NULL")
		} else {
			query.Where("ams_driver_login_sessions.logged_out_time IS NOT NULL")
		}
	} // IsActive
}

func enrichDriverLoginSessionQueryWithPreload(query *gorm.DB, preload models.DriverLoginSessionPreload) {
	if preload.Asset {
		query.Preload("Asset")
	}
}

func (r *haulRepository) CreateDriverLoginSession(ctx context.Context, dB database.DBI, driverLoginSession *models.DriverLoginSession) error {
	return dB.GetTx().Create(driverLoginSession).Error
}

func (r *haulRepository) UpdateDriverLoginSession(ctx context.Context, dB database.DBI, id string, driverLoginSession *models.DriverLoginSession) error {
	return dB.GetTx().Where("id = ?", id).Updates(driverLoginSession).Error
}

func (r *haulRepository) GetHaulStatuses(ctx context.Context, dB database.DBI, cond models.HaulStatusCondition) ([]models.HaulStatus, error) {
	haulStatuses := []models.HaulStatus{}
	query := dB.GetTx().Model(&haulStatuses)

	enrichHaulStatusQueryWithWhere(query, cond.Where)
	enrichHaulStatusQueryWithPreload(query, cond.Preload)

	err := query.Find(&haulStatuses).Error
	if err != nil {
		return nil, err
	}

	return haulStatuses, nil
}

func enrichHaulStatusQueryWithWhere(query *gorm.DB, where models.HaulStatusWhere) {
	if where.Code != "" {
		query.Where("code = ?", where.Code)
	} // Code

	if len(where.Codes) > 0 {
		query.Where("code IN ?", where.Codes)
	} // Codes
}

func enrichHaulStatusQueryWithPreload(query *gorm.DB, preload models.HaulStatusPreload) {
	if preload.HaulActivities {
		query.Preload("HaulActivities")
	}
}

func (r *haulRepository) GetHaulActivities(ctx context.Context, dB database.DBI, cond models.HaulActivityCondition) ([]models.HaulActivity, error) {
	haulActivities := []models.HaulActivity{}
	query := dB.GetTx().Model(&haulActivities)

	enrichHaulActivityQueryWithWhere(query, cond.Where)
	enrichHaulActivityQueryWithPreload(query, cond.Preload)

	err := query.Find(&haulActivities).Error
	if err != nil {
		return nil, err
	}

	return haulActivities, nil
}

func (r *haulRepository) GetHaulActivity(ctx context.Context, dB database.DBI, cond models.HaulActivityCondition) (*models.HaulActivity, error) {
	haulActivity := &models.HaulActivity{}
	query := dB.GetTx().Model(haulActivity)

	enrichHaulActivityQueryWithWhere(query, cond.Where)
	enrichHaulActivityQueryWithPreload(query, cond.Preload)

	err := query.First(haulActivity).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("haul activity")
		}

		return nil, err
	}

	return haulActivity, nil
}

func enrichHaulActivityQueryWithWhere(query *gorm.DB, where models.HaulActivityWhere) {
	if where.Code != "" {
		query.Where("code = ?", where.Code)
	} // Code

	if len(where.Codes) > 0 {
		query.Where("code IN ?", where.Codes)
	} // Codes

	if where.HaulStatusCode != "" {
		query.Where("haul_status_code = ?", where.HaulStatusCode)
	} // HaulStatusCode

	if where.RankLargerThanZero {
		query.Where("rank > 0")
	} // RankLargerThanZero
}

func enrichHaulActivityQueryWithPreload(query *gorm.DB, preload models.HaulActivityPreload) {
	if preload.HaulSubActivities {
		query.Preload("HaulSubActivities")
	}

	if preload.HaulStatus {
		query.Preload("HaulStatus")
	}
}

func (r *haulRepository) GetHaulSubActivities(ctx context.Context, dB database.DBI, cond models.HaulSubActivityCondition) ([]models.HaulSubActivity, error) {
	haulSubActivities := []models.HaulSubActivity{}
	query := dB.GetTx().Model(&haulSubActivities)

	enrichHaulSubActivityQueryWithWhere(query, cond.Where)
	enrichHaulSubActivityQueryWithPreload(query, cond.Preload)

	err := query.Find(&haulSubActivities).Error
	if err != nil {
		return nil, err
	}

	return haulSubActivities, nil
}

func (r *haulRepository) GetHaulSubActivity(ctx context.Context, dB database.DBI, cond models.HaulSubActivityCondition) (*models.HaulSubActivity, error) {
	haulSubActivity := &models.HaulSubActivity{}
	query := dB.GetTx().Model(haulSubActivity)

	enrichHaulSubActivityQueryWithWhere(query, cond.Where)
	enrichHaulSubActivityQueryWithPreload(query, cond.Preload)

	err := query.First(haulSubActivity).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("haul sub activity")
		}

		return nil, err
	}

	return haulSubActivity, nil
}

func enrichHaulSubActivityQueryWithWhere(query *gorm.DB, where models.HaulSubActivityWhere) {
	if where.Code != "" {
		query.Where("code = ?", where.Code)
	} // Code

	if len(where.Codes) > 0 {
		query.Where("code IN ?", where.Codes)
	} // Codes

	if where.MainActivityCode != "" {
		query.Where("main_activity_code = ?", where.MainActivityCode)
	} // MainActivityCode
}

func enrichHaulSubActivityQueryWithPreload(query *gorm.DB, preload models.HaulSubActivityPreload) {
	if preload.MainActivity {
		query.Preload("MainActivity")
	}
}

func (r *haulRepository) CreateHauling(ctx context.Context, dB database.DBI, hauling *models.Hauling) error {
	return dB.GetTx().Create(hauling).Error
}

func (r *haulRepository) UpdateHauling(ctx context.Context, dB database.DBI, id string, hauling *models.Hauling) error {
	return dB.GetTx().
		Where("id = ?", id).
		Updates(hauling).
		Error
}

func (r *haulRepository) GetHauling(ctx context.Context, dB database.DBI, cond models.HaulingCondition) (*models.Hauling, error) {
	hauling := &models.Hauling{}
	query := dB.GetTx().Model(hauling)

	enrichHaulingQueryWithWhere(query, cond.Where)
	enrichHaulingQueryWithPreload(query, cond.Preload)

	query.Order("start_time DESC")
	err := query.First(hauling).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("hauling")
		}

		return nil, err
	}

	return hauling, nil
}

func enrichHaulingQueryWithWhere(query *gorm.DB, where models.HaulingWhere) {
	if where.ID != "" {
		query.Where("ams_haulings.id = ?", where.ID)
	} // ID

	if where.AssetID != "" {
		query.Where("ams_haulings.asset_id = ?", where.AssetID)
	} // AssetID

	if len(where.AssetIDs) > 0 {
		query.Where("ams_haulings.asset_id IN ?", where.AssetIDs)
	} // AssetIDs

	if where.OperatorUserID != "" {
		query.Where("ams_haulings.operator_user_id = ?", where.OperatorUserID)
	} // OperatorUserID

	if where.HaulStatusCode != "" {
		query.Where("ams_haulings.haul_status_code = ?", where.HaulStatusCode)
	} // HaulStatusCode

	if where.HaulActivityCode != "" {
		query.Where("ams_haulings.haul_activity_code = ?", where.HaulActivityCode)
	} // HaulActivityCode

	if where.HaulSubActivityCode != "" {
		query.Where("ams_haulings.haul_sub_activity_code = ?", where.HaulSubActivityCode)
	} // HaulSubActivityCode

	if where.IsActive.Valid {
		if where.IsActive.Bool {
			query.Where("ams_haulings.end_time IS NULL")
		} else {
			query.Where("ams_haulings.end_time IS NOT NULL")
		}
	} // IsActive

	if where.ClientID != "" {
		query.Where("ams_haulings.client_id = ?", where.ClientID)
	} // ClientID

	if len(where.HaulStatusCodes) > 0 {
		query.Where("ams_haulings.haul_status_code IN ?", where.HaulStatusCodes)
	} // HaulStatusCodes

	if len(where.HaulActivityCodes) > 0 {
		query.Where("ams_haulings.haul_activity_code IN ?", where.HaulActivityCodes)
	} // HaulActivityCodes

	if len(where.HaulSubActivityCodes) > 0 {
		query.Where("ams_haulings.haul_sub_activity_code IN ?", where.HaulSubActivityCodes)
	} // HaulSubActivityCodes

	if where.IsLoad.Valid {
		query.Joins("LEFT JOIN ams_haul_load_statuses ahls ON ahls.asset_id = ams_haulings.asset_id").
			Where("ahls.is_load = ?", where.IsLoad.Bool)
	} // IsLoad

	if where.IsOver12HLoginSession {
		query.Joins("JOIN ams_driver_login_sessions dls ON dls.id = ams_haulings.current_driver_login_session_id AND dls.logged_out_time IS NULL").
			Where("dls.logged_in_time < NOW() - INTERVAL '12 hours'")
	} // IsOver12HLoginSession
}

func enrichHaulingQueryWithPreload(query *gorm.DB, preload models.HaulingPreload) {
	if preload.HaulStatus {
		query.Preload("HaulStatus")
	} // HaulStatus

	if preload.HaulActivity {
		query.Preload("HaulActivity")
	} // HaulActivity

	if preload.HaulActivityWithNext {
		query.Preload("HaulActivity.NextActivityCycle.NextActivity")
	} // HaulActivityWithNext

	if preload.HaulActivityWithPrev {
		query.Preload("HaulActivity.PrevActivityCycle.PrevActivity")
	} // HaulActivityWithPrev

	if preload.HaulSubActivity {
		query.Preload("HaulSubActivity")
	} // HaulSubActivity

	if preload.CurrentDriverLoginSession {
		query.Preload("CurrentDriverLoginSession", func(db *gorm.DB) *gorm.DB {
			return db.Where("logged_out_time IS NULL")
		})
	} // CurrentDriverLoginSession

	if preload.Asset {
		query.Preload("Asset")
	} // Asset

	if preload.LoadStatus {
		query.Preload("LoadStatus")
	} // LoadStatus

	if preload.DispacthLocation {
		query.Preload("DispacthLocation")
	} // DispacthLocation
}

func (r *haulRepository) GetHaulingStatusBoard(ctx context.Context, dB database.DBI, param models.GetHaulingStatusBoardParam) ([]models.Hauling, error) {
	haulings := []models.Hauling{}
	query := dB.GetTx().Model(&haulings)

	query.Where("ams_haulings.end_time IS NULL")

	query.
		Joins("LEFT JOIN \"ams_HAUL_SUB_ACTIVITIES\" ahsc ON ahsc.code = ams_haulings.haul_sub_activity_code")

	if param.SearchKeyword != "" {
		query.Joins("LEFT JOIN ams_assets aas ON aas.id = ams_haulings.asset_id").
			Joins("LEFT JOIN ams_driver_login_sessions dlss ON dlss.id = ams_haulings.current_driver_login_session_id").
			Where(query.Session(&gorm.Session{NewDB: true}).
				Unscoped().
				Where("LOWER(aas.reference_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(aas.serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(dlss.user_full_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"))
	}

	enrichHaulingQueryWithWhere(query, param.Cond.Where)
	enrichHaulingQueryWithPreload(query, param.Cond.Preload)

	query.Order("ahsc.rank ASC")
	err := query.Find(&haulings).Error
	if err != nil {
		return nil, err
	}

	return haulings, nil
}

func (r *haulRepository) UpsertHaulDispatchLocation(ctx context.Context, dB database.DBI, haulDispatchLocation *models.HaulDispatchLocation) error {
	return dB.GetTx().Clauses(clause.OnConflict{
		Columns: []clause.Column{{Name: "asset_id"}},
		DoUpdates: clause.AssignmentColumns([]string{
			"load_location_id",
			"dump_location_id",
			"dispatch_location_type_code",
			"updated_at",
			"updated_by",
		}),
	}).Create(haulDispatchLocation).Error
}

func (r *haulRepository) GetHaulDispatchLocation(ctx context.Context, dB database.DBI, cond models.HaulDispatchLocationCondition) (*models.HaulDispatchLocation, error) {
	haulDispatchLocation := &models.HaulDispatchLocation{}
	query := dB.GetTx().Model(haulDispatchLocation)

	enrichHaulDispatchLocationQueryWithWhere(query, cond.Where)
	enrichHaulDispatchLocationQueryWithPreload(query, cond.Preload)

	err := query.First(haulDispatchLocation).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("haul dispatch location")
		}

		return nil, err
	}

	return haulDispatchLocation, nil
}

func enrichHaulDispatchLocationQueryWithWhere(query *gorm.DB, where models.HaulDispatchLocationWhere) {
	if where.AssetID != "" {
		query.Where("asset_id = ?", where.AssetID)
	} // AssetID

	if where.ClientID != "" {
		query.Where("client_id = ?", where.ClientID)
	} // ClientID
}

func enrichHaulDispatchLocationQueryWithPreload(query *gorm.DB, preload models.HaulDispatchLocationPreload) {
	if preload.LoadLocation {
		query.Preload("LoadLocation")
	}

	if preload.DumpLocation {
		query.Preload("DumpLocation")
	}

	if preload.DispatchLocationType {
		query.Preload("DispatchLocationType")
	}
}

func (r *haulRepository) InitHaulLoadStatus(ctx context.Context, dB database.DBI, haulLoadStatus *models.HaulLoadStatus) error {
	return dB.GetTx().
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "asset_id"}},
			DoNothing: true,
		}).
		Create(haulLoadStatus).Error
}
