package persistence

import (
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"context"
	"strings"

	"gorm.io/gorm"
)

type weightBridgeRepository struct{}

func NewWeightBridgeRepository() repository.WeightBridgeRepository {
	return &weightBridgeRepository{}
}

func enrichWeightBridgeTicketQueryWithWhere(query *gorm.DB, where models.WeightBridgeTicketWhere) {
	if where.ID != "" {
		query.Where("ams_weight_bridge_tickets.id = ?", where.ID)
	} // ID

	if where.ClientID != "" {
		query.Where("ams_weight_bridge_tickets.client_id = ?", where.ClientID)
	} // ClientID

	if !where.ReconciliationDateStart.IsZero() {
		query.Where("ams_weight_bridge_tickets.reconciliation_date >= ?", where.ReconciliationDateStart)
	} // ReconciliationDateStart

	if !where.ReconciliationDateEnd.IsZero() {
		query.Where("ams_weight_bridge_tickets.reconciliation_date <= ?", where.ReconciliationDateEnd)
	} // ReconciliationDateEnd

	if !where.TicketDateStart.IsZero() {
		query.Where("ams_weight_bridge_tickets.ticket_date >= ?", where.TicketDateStart)
	} // TicketDateStart

	if !where.TicketDateEnd.IsZero() {
		query.Where("ams_weight_bridge_tickets.ticket_date <= ?", where.TicketDateEnd)
	} // TicketDateEnd

	if where.LocationID != "" {
		query.Where(
			`ams_weight_bridge_tickets.inbound_location_id = ?
     		OR ams_weight_bridge_tickets.outbound_location_id = ?
			OR ams_weight_bridge_tickets.inbound_weight_bridge_location_id = ?
			OR ams_weight_bridge_tickets.outbound_weight_bridge_location_id = ?`,
			where.LocationID,
			where.LocationID,
			where.LocationID,
			where.LocationID,
		)
	}

	if where.VehicleAssetID != "" {
		query.Where("ams_weight_bridge_tickets.vehicle_asset_id = ?", where.VehicleAssetID)
	} // VehicleAssetID

	if len(where.VehicleAssetIDs) > 0 {
		query.Where("ams_weight_bridge_tickets.vehicle_asset_id IN ?", where.VehicleAssetIDs)
	} // VehicleAssetIDs

	if where.WorkShiftID != "" {
		query.Where("ams_weight_bridge_tickets.work_shift_id = ?", where.WorkShiftID)
	} // WorkShiftID

	if len(where.WorkShiftIDs) > 0 {
		query.Where("ams_weight_bridge_tickets.work_shift_id IN ?", where.WorkShiftIDs)
	} // WorkShiftIDs

	if len(where.MaterialIDs) > 0 {
		query.Where("ams_weight_bridge_tickets.material_id IN ?", where.MaterialIDs)
	}

	if where.RouteStartLocationID != "" {
		query.Where("r_pick.start_location_id = ?", where.RouteStartLocationID)
	}

	if where.RouteEndLocationID != "" {
		query.Where("r_pick.end_location_id = ?", where.RouteEndLocationID)
	}

	if where.ReferenceID != "" {
		query.Where("ams_weight_bridge_tickets.reference_id = ?", where.ReferenceID)
	} // ReferenceID
}

func enrichWeightBridgeTicketQueryWithPreload(query *gorm.DB, preload models.WeightBridgeTicketPreload) {

	if preload.AssetVehicle {
		query.Preload("AssetVehicle")
		query.Preload("AssetVehicle.Asset")
	}

	if preload.InboundWeightBridgeLocation {
		query.Preload("InboundWeightBridgeLocation")
	}

	if preload.InboundLocation {
		query.Preload("InboundLocation")
	}

	if preload.OutboundWeightBridgeLocation {
		query.Preload("OutboundWeightBridgeLocation")
	}

	if preload.OutboundLocation {
		query.Preload("OutboundLocation")
	}
}

func (r *weightBridgeRepository) GetWeightBridgeTicket(ctx context.Context, dB database.DBI, cond models.WeightBridgeTicketCondition) (*models.WeightBridgeTicket, error) {
	ticket := models.WeightBridgeTicket{}
	query := dB.GetOrm().Model(&ticket)

	enrichWeightBridgeTicketQueryWithWhere(query, cond.Where)

	enrichWeightBridgeTicketQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	err := query.First(&ticket).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("weight bridge ticket")
		}

		return nil, err
	}

	return &ticket, nil
}

func (r *weightBridgeRepository) GetWeightBridgeTicketWithLocationRoute(ctx context.Context, dB database.DBI, cond models.WeightBridgeTicketCondition) (*models.WeightBridgeTicketWithLocationRoute, error) {
	ticket := models.WeightBridgeTicketWithLocationRoute{}
	weightBridgeTicket := models.WeightBridgeTicket{}
	query := dB.GetOrm().Model(&weightBridgeTicket).
		Joins("LEFT JOIN ams_locations inloc ON inloc.id = ams_weight_bridge_tickets.inbound_location_id").
		Joins("LEFT JOIN ams_locations outloc ON outloc.id = ams_weight_bridge_tickets.outbound_location_id").
		Joins(`
			LEFT JOIN LATERAL (
				SELECT r.*, end_locations.name as end_location_name
				FROM ams_location_routes r
				LEFT JOIN ams_locations AS end_locations ON end_locations.id = r.end_location_id
				WHERE r.deleted_at IS NULL
				AND r.client_id = ams_weight_bridge_tickets.client_id
				AND r.start_location_id = ams_weight_bridge_tickets.inbound_location_id
				AND r.end_location_id = outloc.parent_location_id
				AND r.operation_start_date <= ams_weight_bridge_tickets.outbound_at
				ORDER BY r.operation_start_date DESC
				LIMIT 1
			) AS r_pick ON TRUE
		`).
		Joins("JOIN ams_assets ON ams_assets.id = ams_weight_bridge_tickets.vehicle_asset_id").
		Select("ams_weight_bridge_tickets.*, r_pick.id AS location_route_id, r_pick.start_location_id, r_pick.end_location_id, r_pick.operation_start_date, r_pick.distance_km, r_pick.is_estimation_distance_km, inloc.name AS start_location_name, r_pick.end_location_name AS end_location_name")

	enrichWeightBridgeTicketQueryWithWhere(query, cond.Where)

	enrichWeightBridgeTicketQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	err := query.First(&ticket).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("weight bridge ticket")
		}

		return nil, err
	}

	return &ticket, nil
}

func (r *weightBridgeRepository) GetWeightBridgeTickets(ctx context.Context, dB database.DBI, param models.GetWeightBridgeTicketListParam) ([]models.WeightBridgeTicket, error) {
	tickets := []models.WeightBridgeTicket{}
	query := dB.GetTx().Model(&tickets).
		Joins("JOIN ams_assets ON ams_assets.id = ams_weight_bridge_tickets.vehicle_asset_id")

	if param.SearchKeyword != "" {
		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().
				Where("LOWER(ams_assets.name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_assets.reference_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_assets.serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_weight_bridge_tickets.reference_id) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_weight_bridge_tickets.vehicle_asset_id) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_weight_bridge_tickets.inbound_operator_user_first_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_weight_bridge_tickets.inbound_operator_user_last_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_weight_bridge_tickets.outbound_operator_user_first_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_weight_bridge_tickets.outbound_operator_user_last_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
		)
	}

	enrichWeightBridgeTicketQueryWithWhere(query, param.Cond.Where)
	enrichWeightBridgeTicketQueryWithPreload(query, param.Cond.Preload)

	query.Order("updated_at DESC")
	err := query.Find(&tickets).Error
	if err != nil {
		return nil, err
	}

	return tickets, nil
}

func (r *weightBridgeRepository) GetWeightBridgeTicketList(ctx context.Context, dB database.DBI, param models.GetWeightBridgeTicketListParam) (int, []models.WeightBridgeTicket, error) {
	var totalRecords int64
	tickets := []models.WeightBridgeTicket{}
	query := dB.GetTx().Model(&tickets).
		Joins("JOIN ams_assets ON ams_assets.id = ams_weight_bridge_tickets.vehicle_asset_id")

	if param.SearchKeyword != "" {
		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().
				Where("LOWER(ams_assets.name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_assets.reference_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_assets.serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_weight_bridge_tickets.reference_id) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_weight_bridge_tickets.vehicle_asset_id) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_weight_bridge_tickets.inbound_operator_user_first_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_weight_bridge_tickets.inbound_operator_user_last_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_weight_bridge_tickets.outbound_operator_user_first_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_weight_bridge_tickets.outbound_operator_user_last_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
		)
	}

	enrichWeightBridgeTicketQueryWithWhere(query, param.Cond.Where)
	enrichWeightBridgeTicketQueryWithPreload(query, param.Cond.Preload)

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	query.Order("updated_at DESC")
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&tickets).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), tickets, nil

}

func (r *weightBridgeRepository) GetWeightBridgeTicketListWithLocationRoute(ctx context.Context, dB database.DBI, param models.GetWeightBridgeTicketListParam) (int, []models.WeightBridgeTicketWithLocationRoute, error) {
	var totalRecords int64
	tickets := []models.WeightBridgeTicketWithLocationRoute{}
	weightBridgeTickets := []models.WeightBridgeTicket{}
	query := dB.GetTx().Model(&weightBridgeTickets).
		Joins("LEFT JOIN ams_locations inloc ON inloc.id = ams_weight_bridge_tickets.inbound_location_id").
		Joins("LEFT JOIN ams_locations outloc ON outloc.id = ams_weight_bridge_tickets.outbound_location_id").
		Joins(`
			LEFT JOIN LATERAL (
				SELECT r.*, end_locations.name as end_location_name
				FROM ams_location_routes r
				LEFT JOIN ams_locations AS end_locations ON end_locations.id = r.end_location_id
				WHERE r.deleted_at IS NULL
				AND r.client_id = ams_weight_bridge_tickets.client_id
				AND r.start_location_id = ams_weight_bridge_tickets.inbound_location_id
				AND r.end_location_id = outloc.parent_location_id
				AND r.operation_start_date <= ams_weight_bridge_tickets.outbound_at
				ORDER BY r.operation_start_date DESC
				LIMIT 1
			) AS r_pick ON TRUE
		`).
		Joins("JOIN ams_assets ON ams_assets.id = ams_weight_bridge_tickets.vehicle_asset_id").
		Select("ams_weight_bridge_tickets.*, r_pick.id AS location_route_id, r_pick.start_location_id, r_pick.end_location_id, r_pick.operation_start_date, r_pick.distance_km, r_pick.is_estimation_distance_km, inloc.name AS start_location_name, r_pick.end_location_name AS end_location_name")

	if param.SearchKeyword != "" {
		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().
				Where("LOWER(ams_assets.name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_assets.reference_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_assets.serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_weight_bridge_tickets.reference_id) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_weight_bridge_tickets.vehicle_asset_id) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_weight_bridge_tickets.inbound_operator_user_first_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_weight_bridge_tickets.inbound_operator_user_last_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_weight_bridge_tickets.outbound_operator_user_first_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_weight_bridge_tickets.outbound_operator_user_last_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
		)
	}

	enrichWeightBridgeTicketQueryWithWhere(query, param.Cond.Where)
	enrichWeightBridgeTicketQueryWithPreload(query, param.Cond.Preload)

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	if len(param.Sorts) > 0 {
		for _, sort := range param.Sorts {
			s := strings.Split(sort, ":")
			if len(s) < 2 {
				continue
			}

			if _, ok := models.WeightBridgeTicketSorts[s[0]]; !ok {
				continue
			}

			query.Order(s[0] + " " + s[1])
		}
	} else {
		query.Order("ams_weight_bridge_tickets.updated_at DESC")
	}

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Debug().Offset(offset).Limit(param.PageSize).Find(&tickets).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), tickets, nil

}

func (r *weightBridgeRepository) CreateWeightBridgeTicket(ctx context.Context, dB database.DBI, weightBridgeTicket *models.WeightBridgeTicket) error {
	return dB.GetTx().Create(weightBridgeTicket).Error
}
func (r *weightBridgeRepository) UpdateWeightBridgeTicket(ctx context.Context, dB database.DBI, weightBridgeTicket *models.WeightBridgeTicket) error {
	return dB.GetTx().Save(weightBridgeTicket).Error
}

func (r *weightBridgeRepository) DeleteWeightBridgeTicket(ctx context.Context, dB database.DBI, ticketId string) error {
	query := dB.GetTx()

	return query.Delete(&models.WeightBridgeTicket{}, "id = ?", ticketId).Error

}
func (r *weightBridgeRepository) GetWeightBridgeTicketSummary(
	ctx context.Context,
	dB database.DBI,
	cond models.WeightBridgeTicketCondition,
) (*models.WeightBridgeTicketSummary, error) {
	summary := models.WeightBridgeTicketSummary{}
	weightBridgeTicket := models.WeightBridgeTicket{}

	query := dB.GetOrm().Model(&weightBridgeTicket).
		Select(`
			SUM(ams_weight_bridge_tickets.inbound_weight_kg - ams_weight_bridge_tickets.outbound_weight_kg) AS total_weight,
			COUNT(*) AS total_trip,
			AVG(ams_weight_bridge_tickets.inbound_weight_kg - ams_weight_bridge_tickets.outbound_weight_kg) AS avg_payload,
			MAX(ams_weight_bridge_tickets.inbound_weight_kg - ams_weight_bridge_tickets.outbound_weight_kg) AS max_net_weight,
			MIN(ams_weight_bridge_tickets.inbound_weight_kg - ams_weight_bridge_tickets.outbound_weight_kg) AS min_net_weight,
			SUM(ams_weight_bridge_tickets.inbound_weight_kg) AS total_gross,
			AVG(ams_weight_bridge_tickets.inbound_weight_kg) AS avg_gross,
			MAX(ams_weight_bridge_tickets.inbound_weight_kg) AS max_gross,
			MIN(ams_weight_bridge_tickets.inbound_weight_kg) AS min_gross,
			SUM(ams_weight_bridge_tickets.outbound_weight_kg) AS total_tare,
			AVG(ams_weight_bridge_tickets.outbound_weight_kg) AS avg_tare,
			MAX(ams_weight_bridge_tickets.outbound_weight_kg) AS max_tare,
			MIN(ams_weight_bridge_tickets.outbound_weight_kg) AS min_tare
		`)

	// Apply dynamic WHERE conditions if present
	enrichWeightBridgeTicketQueryWithWhere(query, cond.Where)

	err := query.Scan(&summary).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("weight bridge ticket summary")
		}
		return nil, err
	}

	return &summary, nil
}

func (r *weightBridgeRepository) GetWeightBridgeTicketInboundSummaries(
	ctx context.Context,
	dB database.DBI,
	param models.GetWeightBridgeTicketListParam,
) (int, []models.WeightBridgeTicketInboundSummary, error) {
	var totalRecords int64
	summaries := []models.WeightBridgeTicketInboundSummary{}
	weightBridgeTickets := []models.WeightBridgeTicket{}

	query := dB.GetTx().Model(&weightBridgeTickets).
		Joins("INNER JOIN ams_locations inloc ON inloc.id = ams_weight_bridge_tickets.inbound_location_id").
		Select(`
			SUM(ams_weight_bridge_tickets.inbound_weight_kg - ams_weight_bridge_tickets.outbound_weight_kg) AS total_weight,
			COUNT(*) AS total_trip,
			AVG(ams_weight_bridge_tickets.inbound_weight_kg - ams_weight_bridge_tickets.outbound_weight_kg) AS avg_payload,
			ams_weight_bridge_tickets.inbound_location_id,
			inloc.id AS inbound_location_id,
			inloc.name AS inbound_location_name
		`).
		Group("ams_weight_bridge_tickets.inbound_location_id, inloc.id, inloc.name")

	// ✅ Support search keyword (focus inbound + total_weight)
	if param.SearchKeyword != "" {
		query = query.Having(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().
				Where("LOWER(inloc.name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("CAST(SUM(ams_weight_bridge_tickets.inbound_weight_kg - ams_weight_bridge_tickets.outbound_weight_kg) AS TEXT) LIKE ?", "%"+param.SearchKeyword+"%"),
		)
	}

	// Apply dynamic conditions
	enrichWeightBridgeTicketQueryWithWhere(query, param.Cond.Where)

	// Count total grouped records
	countQuery := dB.GetTx().Model(&weightBridgeTickets).
		Select("inbound_location_id").
		Group("inbound_location_id")

	enrichWeightBridgeTicketQueryWithWhere(countQuery, param.Cond.Where)

	err := countQuery.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}
	if totalRecords <= 0 {
		return 0, nil, nil
	}

	// ✅ Order by inbound_location_name
	query.Order("inloc.name ASC")

	// Apply pagination
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Scan(&summaries).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), summaries, nil
}

func (r *weightBridgeRepository) GetWeightBridgeTicketOutboundSummaries(
	ctx context.Context,
	dB database.DBI,
	param models.GetWeightBridgeTicketListParam,
) (int, []models.WeightBridgeTicketOutboundSummary, error) {
	var totalRecords int64
	summaries := []models.WeightBridgeTicketOutboundSummary{}
	weightBridgeTickets := []models.WeightBridgeTicket{}

	query := dB.GetTx().Model(&weightBridgeTickets).
		Joins("INNER JOIN ams_locations outloc ON outloc.id = ams_weight_bridge_tickets.outbound_location_id").
		Select(`
			SUM(ams_weight_bridge_tickets.inbound_weight_kg - ams_weight_bridge_tickets.outbound_weight_kg) AS total_weight,
			COUNT(*) AS total_trip,
			AVG(ams_weight_bridge_tickets.inbound_weight_kg - ams_weight_bridge_tickets.outbound_weight_kg) AS avg_payload,
			ams_weight_bridge_tickets.outbound_location_id,
			outloc.id AS outbound_location_id,
			outloc.name AS outbound_location_name
		`).
		Group("ams_weight_bridge_tickets.outbound_location_id, outloc.id, outloc.name")

	// ✅ Support search keyword (focus outbound + total_weight)
	if param.SearchKeyword != "" {
		query = query.Having(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().
				Where("LOWER(outloc.name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("CAST(SUM(ams_weight_bridge_tickets.inbound_weight_kg - ams_weight_bridge_tickets.outbound_weight_kg) AS TEXT) LIKE ?", "%"+param.SearchKeyword+"%"),
		)
	}

	// Apply dynamic conditions
	enrichWeightBridgeTicketQueryWithWhere(query, param.Cond.Where)

	// Count total grouped records
	countQuery := dB.GetTx().Model(&weightBridgeTickets).
		Select("outbound_location_id").
		Group("outbound_location_id")

	enrichWeightBridgeTicketQueryWithWhere(countQuery, param.Cond.Where)

	err := countQuery.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}
	if totalRecords <= 0 {
		return 0, nil, nil
	}

	// ✅ Order by outbound_location_name
	query.Order("outloc.name ASC")

	// Apply pagination
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Scan(&summaries).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), summaries, nil
}
