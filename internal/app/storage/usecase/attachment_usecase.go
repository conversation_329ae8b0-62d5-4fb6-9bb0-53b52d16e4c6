package usecase

import (
	"assetfindr/internal/app/storage/constants"
	"assetfindr/internal/app/storage/dtos"
	"assetfindr/internal/app/storage/models"
	"assetfindr/internal/app/storage/repository"
	internalConstants "assetfindr/internal/constants"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"

	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers"
	"assetfindr/pkg/common/helpers/authhelpers"
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/spf13/viper"
)

type AttachmentUseCase struct {
	DB                   database.DBUsecase
	AttachmentRepository repository.AttachmentRepository
	StorageRepository    repository.StorageRepository
}

func NewAttachmentUseCase(DB database.DBUsecase, attachmentRepo repository.AttachmentRepository, storageRepo repository.StorageRepository) *AttachmentUseCase {
	return &AttachmentUseCase{
		DB:                   DB,
		AttachmentRepository: attachmentRepo,
		StorageRepository:    storageRepo,
	}
}

func generateDestinationPath(clientID, referenceCode, prevPath string) string {
	return clientID + "/" +
		referenceCode + "/" +
		strings.Trim(prevPath, constants.TEMP_USER_UPLOAD_PREFIX)
}

func (uc *AttachmentUseCase) GetAttachments(ctx context.Context, req dtos.GetAttachmentsReq) ([]dtos.GetAttachmentsResp, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	attachmentsSources, err := uc.AttachmentRepository.GetAttachmentsSources(ctx, uc.DB.DB(), models.AttachmentCondition{
		Where: models.AttachmentWhere{
			ClientID:           claim.GetLoggedInClientID(),
			ReferenceCode:      req.ReferenceCode,
			SourceReferenceID:  req.SourceReferenceID,
			SourceReferenceIDs: req.SourceReferenceIDs,
			TargetReferenceID:  req.TargetReferenceID,
		},
	})
	if err != nil {
		return nil, err
	}

	resp := make([]dtos.GetAttachmentsResp, 0, len(attachmentsSources))
	for _, attachmentsSource := range attachmentsSources {
		url, _ := helpers.GenerateCloudStorageSignedURL(attachmentsSource.Attachment.Path, time.Duration(24))
		resp = append(resp, dtos.GetAttachmentsResp{
			Id:                attachmentsSource.Attachment.ID,
			Number:            attachmentsSource.Attachment.Number,
			CreatedAt:         attachmentsSource.Attachment.CreatedAt,
			Label:             attachmentsSource.Attachment.Label,
			ReferenceCode:     attachmentsSource.ReferenceCode,
			SourceReferenceID: attachmentsSource.SourceReferenceID,
			TargetReferenceID: attachmentsSource.TargetReferenceID,
			Path:              url,
			FileType:          attachmentsSource.Attachment.FileType,
		})
	}

	return resp, nil
}

func GeneratePublicGalleryJWTSignedToken(clientID string) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, authhelpers.PublicGaleryJwtTokenClaims{
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: time.Now().Add(60 * 24 * time.Hour).Unix(),
		},
		LoggedInClientID: clientID,
	})
	jwtKey := []byte(viper.GetString(internalConstants.PUBLIC_GALLERY_JWT_SECRET_KEY))
	signedToken, err := token.SignedString(jwtKey)
	if err != nil {
		return "", err
	}

	return signedToken, nil
}

func (uc *AttachmentUseCase) AttachmentPublicLinks(ctx context.Context, ids []string, clientID, clientAlias string) ([]string, error) {
	apiURL := internalConstants.STAGING_URL
	if appEnv := os.Getenv(internalConstants.ENV_APP_ENV); appEnv == "production" {
		apiURL = clientAlias + internalConstants.BASE_URL
	} else if appEnv == "sandbox" {
		apiURL = internalConstants.SANDBOX_URL
	}

	signedToken, err := GeneratePublicGalleryJWTSignedToken(clientID)
	if err != nil {
		return nil, err
	}

	res := make([]string, 0, len(ids))
	for _, id := range ids {
		res = append(res, fmt.Sprintf("https://%s/#/gallery?attachment_id=%s&token=%s", apiURL, id, signedToken))
	}

	return res, nil
}

func (uc *AttachmentUseCase) PhotoPublicLink(ctx context.Context, photo string, clientID, clientAlias string) (string, error) {
	apiURL := internalConstants.STAGING_URL
	if appEnv := os.Getenv(internalConstants.ENV_APP_ENV); appEnv == "production" {
		apiURL = clientAlias + internalConstants.BASE_URL
	} else if appEnv == "sandbox" {
		apiURL = internalConstants.SANDBOX_URL
	}

	signedToken, err := GeneratePublicGalleryJWTSignedToken(clientID)
	if err != nil {
		return "", err
	}

	res := fmt.Sprintf("https://%s/#/gallery?path=%s&token=%s", apiURL, photo, signedToken)
	return res, nil
}

func (uc *AttachmentUseCase) MoveUserPhotoStorage(ctx context.Context, clientID, prevPath string) (string, error) {
	destinationPath := generateDestinationPath(clientID, "USER_PROFILE", prevPath)
	_, err := uc.StorageRepository.MoveFile(ctx, prevPath, destinationPath)
	if err != nil {
		return "", err
	}

	return destinationPath, nil
}

func (uc *AttachmentUseCase) MoveProductPhotoStorage(ctx context.Context, clientID, prevPath string) (string, error) {
	destinationPath := generateDestinationPath(clientID, "PRODUCT_PHOTO", prevPath)
	_, err := uc.StorageRepository.MoveFile(ctx, prevPath, destinationPath)
	if err != nil {
		return "", err
	}

	return destinationPath, nil
}

func (uc *AttachmentUseCase) MoveGeneralPhotoStorage(ctx context.Context, refCode, clientID, prevPath string) (string, error) {
	destinationPath := generateDestinationPath(clientID, refCode, prevPath)
	_, err := uc.StorageRepository.MoveFile(ctx, prevPath, destinationPath)
	if err != nil {
		return "", err
	}

	return destinationPath, nil
}

func (uc *AttachmentUseCase) MoveClientPhotoStorage(ctx context.Context, clientID, prevPath string) (string, error) {
	destinationPath := generateDestinationPath(clientID, "CLIENT_PROFILE", prevPath)
	_, err := uc.StorageRepository.MoveFile(ctx, prevPath, destinationPath)
	if err != nil {
		return "", err
	}

	return destinationPath, nil
}

func (uc *AttachmentUseCase) MoveAssetPhotoStorage(ctx context.Context, clientID, prevPath string) (string, error) {
	destinationPath := generateDestinationPath(clientID, "ASSET_PHOTO", prevPath)
	_, err := uc.StorageRepository.MoveFile(ctx, prevPath, destinationPath)
	if err != nil {
		return "", err
	}

	return destinationPath, nil
}

func (uc *AttachmentUseCase) MovePhotoStorage(ctx context.Context, clientID, refCode, prevPath string) (string, error) {
	destinationPath := generateDestinationPath(clientID, refCode, prevPath)
	_, err := uc.StorageRepository.MoveFile(ctx, prevPath, destinationPath)
	if err != nil {
		return "", err
	}

	return destinationPath, nil
}

func (uc *AttachmentUseCase) GetFileSignedURL(ctx context.Context, filePath string) (string, error) {
	signedURL, err := uc.StorageRepository.GetFileSignedURL(ctx, filePath, time.Now().Add(24*time.Hour))
	if err != nil {
		return "", err
	}

	return signedURL, nil
}

func (uc *AttachmentUseCase) CreateAttachmentsPhotosV2(ctx context.Context, param dtos.UpsertAttachmentReq) ([]models.Attachment, error) {
	// Build a clean list: drop deletes, turn existing IDs into copy requests.
	filtered := make([]commonmodel.PhotoReq, 0, len(param.Photos))
	for _, p := range param.Photos {
		if p.IsDelete {
			continue // ignore deletes in create-only flow
		}
		if p.ID != "" {
			p.CopyFromID = p.ID
			p.ID = ""
		}
		filtered = append(filtered, p)
	}
	param.Photos = filtered

	return uc.UpdateAttachmentPhotos(ctx, param)
}

func (uc *AttachmentUseCase) UpdateAttachmentPhotos(ctx context.Context, param dtos.UpsertAttachmentReq) ([]models.Attachment, error) {
	newAttachments := make([]models.Attachment, 0, len(param.Photos))
	toDeleteIDs := make([]string, 0, len(param.Photos))

	// Allow multiple copy requests per source attachment ID.
	toCopy := make(map[string][]models.Attachment, len(param.Photos)) // key: source/original attachment ID

	// Build base sources once, then clone per record to avoid aliasing.
	buildBaseSources := func() []models.AttachmentSource {
		sources := make([]models.AttachmentSource, 0, len(param.AttachmentSources)+1)
		for i := range param.AttachmentSources {
			sources = append(sources, models.AttachmentSource{
				ReferenceCode:     param.AttachmentSources[i].ReferenceCode,
				SourceReferenceID: param.AttachmentSources[i].SourceReferenceID,
				TargetReferenceID: param.AttachmentSources[i].TargetReferenceID,
			})
		}
		if param.ReferenceCode != "" && param.SourceReferenceID != "" {
			sources = append(sources, models.AttachmentSource{
				ReferenceCode:     param.ReferenceCode,
				SourceReferenceID: param.SourceReferenceID,
				TargetReferenceID: param.TargetReferenceID,
			})
		}
		return sources
	}
	baseSources := buildBaseSources()

	cloneSources := func(src []models.AttachmentSource) []models.AttachmentSource {
		out := make([]models.AttachmentSource, len(src))
		copy(out, src)
		return out
	}

	for _, photo := range param.Photos {
		if photo.IsDelete {
			toDeleteIDs = append(toDeleteIDs, photo.ID)
			continue
		}

		// Copy from existing attachment flow (ID empty and CopyFromID not empty)
		if photo.IsCopyFromExisting() {
			if photo.CopyFromID != "" { // defensive
				toCopy[photo.CopyFromID] = append(toCopy[photo.CopyFromID], models.Attachment{
					Label:             photo.Label,
					AttachmentSources: cloneSources(baseSources),
				})
			}
		}

		// New upload flow (ID empty and CopyFromID empty)
		if photo.IsNew() {
			destPath := generateDestinationPath(
				param.ClientID,
				constants.ATTACHMENT_REFERENCE_CODE_ASSET,
				photo.Path,
			)

			contentType, err := uc.StorageRepository.MoveFile(ctx, photo.Path, destPath)
			if err != nil {
				return nil, err
			}

			newAttachments = append(newAttachments, models.Attachment{
				Label:             photo.Label,
				Path:              destPath,
				FileType:          contentType,
				AttachmentSources: cloneSources(baseSources),
			})
		}
	}

	// Nothing to do? Bail early.
	if len(newAttachments) == 0 && len(toDeleteIDs) == 0 && len(toCopy) == 0 {
		return newAttachments, nil
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}
	defer tx.Rollback()

	if len(newAttachments) > 0 {
		if err := uc.AttachmentRepository.CreateAttachments(ctx, tx.DB(), newAttachments); err != nil {
			return nil, fmt.Errorf("error creating attachments: %w", err)
		}
	}

	if len(toDeleteIDs) > 0 {
		if err := uc.AttachmentRepository.DeleteAttachments(ctx, tx.DB(), toDeleteIDs); err != nil {
			return nil, fmt.Errorf("error deleting attachments: %w", err)
		}
	}

	// Create copies: fetch sources, then fan out per request.
	if len(toCopy) > 0 {
		ids := make([]string, 0, len(toCopy))
		for id := range toCopy {
			ids = append(ids, id)
		}

		originals, err := uc.AttachmentRepository.GetAttachments(ctx, tx.DB(), models.AttachmentCondition{
			Where: models.AttachmentWhere{IDs: ids},
		})
		if err != nil {
			return nil, fmt.Errorf("error fetching original attachments: %w", err)
		}

		// Count how many copies we’ll create to pre-size the slice.
		totalCopies := 0
		for _, reqs := range toCopy {
			totalCopies += len(reqs)
		}

		finalCopies := make([]models.Attachment, 0, totalCopies)
		for _, orig := range originals {
			if reqs, ok := toCopy[orig.ID]; ok {
				for _, req := range reqs {
					finalCopies = append(finalCopies, models.Attachment{
						Label:             req.Label,
						Path:              orig.Path,
						FileType:          orig.FileType,
						Number:            orig.Number,
						AttachmentSources: req.AttachmentSources,
					})
				}
			}
		}

		if len(finalCopies) > 0 {
			if err := uc.AttachmentRepository.CreateAttachments(ctx, tx.DB(), finalCopies); err != nil {
				return nil, fmt.Errorf("error creating copied attachments: %w", err)
			}
		}
	}

	if err := tx.Commit(); err != nil {
		return nil, err
	}
	return newAttachments, nil
}

func (uc *AttachmentUseCase) DeleteAttachments(ctx context.Context, id string) (*commonmodel.DeleteResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	_, err = uc.AttachmentRepository.GetAttachment(ctx, uc.DB.DB(), models.AttachmentCondition{
		Where: models.AttachmentWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
		Columns: []string{},
	})
	if err != nil {
		return nil, err
	}

	err = uc.AttachmentRepository.DeleteAttachment(ctx, uc.DB.DB(), id)
	if err != nil {
		return nil, err
	}

	return &commonmodel.DeleteResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
	}, nil
}

func (uc *AttachmentUseCase) CreateAttachment(ctx context.Context, req dtos.CreateAttachmentReq) (*models.Attachment, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}
	clientId := claim.GetLoggedInClientID()

	destinationPath := generateDestinationPath(clientId, req.ReferenceCode, req.Path)
	contentType, err := uc.StorageRepository.MoveFile(ctx, req.Path, destinationPath)
	if err != nil {
		return nil, err
	}

	attachment := models.Attachment{
		Label:    req.Label,
		Path:     destinationPath,
		FileType: contentType,
		AttachmentSources: []models.AttachmentSource{
			{
				ReferenceCode:     req.ReferenceCode,
				SourceReferenceID: req.SourceReferenceID,
				TargetReferenceID: req.TargetReferenceID,
			},
		},
	}

	err = uc.AttachmentRepository.CreateAttachments(ctx, uc.DB.WithCtx(ctx).DB(), []models.Attachment{
		attachment,
	})
	if err != nil {
		return nil, fmt.Errorf("error when create attachments: %w", err)
	}

	return &attachment, nil

}

func (uc *AttachmentUseCase) UpdateAttachmentLabel(ctx context.Context, id string, req dtos.UpdateAttachmentReq) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	_, err = uc.AttachmentRepository.GetAttachment(ctx, uc.DB.DB(), models.AttachmentCondition{
		Where: models.AttachmentWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	err = uc.AttachmentRepository.UpdateAttachment(ctx, uc.DB.DB(), id, &models.Attachment{
		Label: req.Label,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}, nil
}

func (uc *AttachmentUseCase) GetPublicAttachment(ctx context.Context, id string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetPublicGalleryClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	attachment, err := uc.AttachmentRepository.GetAttachment(ctx, uc.DB.DB(), models.AttachmentCondition{
		Where: models.AttachmentWhere{
			ID:       id,
			ClientID: claim.LoggedInClientID,
		},
	})
	if err != nil {
		return nil, err
	}

	signedUrl, err := uc.StorageRepository.GetFileSignedURL(ctx, attachment.Path, time.Now().Add(7*24*time.Hour))
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        signedUrl,
	}, nil
}

func (uc *AttachmentUseCase) GetPublicPhoto(ctx context.Context, req dtos.GetPublicPhotoReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetPublicGalleryClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	if !strings.HasPrefix(req.Path, claim.LoggedInClientID) {
		return nil, errorhandler.ErrBadRequest("Invalid path")
	}

	signedUrl, err := uc.StorageRepository.GetFileSignedURL(ctx, req.Path, time.Now().Add(7*24*time.Hour))
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: req.Path,
		Data:        signedUrl,
	}, nil
}
