package persistence

import (
	"assetfindr/internal/app/user-identity/models"
	"assetfindr/internal/app/user-identity/repository"
	"assetfindr/internal/infrastructure/database"
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type AnalyticRepository struct{}

func NewAnalyticRepository() repository.AnalyticRepository {
	return &AnalyticRepository{}
}

func enrichAnalyticQueryWithWhere(query *gorm.DB, where models.AnalyticWhere) {
	if len(where.ExcludeAnalyticCode) > 0 {
		query.Where("code NOT IN ?", where.ExcludeAnalyticCode)
	} // ExcludeAnalyticCode
	if len(where.AnalyticTypeCodes) > 0 {
		query.Where("analytic_type_code IN ?", where.AnalyticTypeCodes)
	} // AnalyticTypeCodes

}

func enrichAnalyticDisplayConfigQueryWithPreload(query *gorm.DB, preload models.AnalyticDisplayConfigPreload) {
	if preload.Analytic {
		query.Preload("Analytic")
	} // Analytic
}

func enrichAnalyticDisplayConfigQueryWithWhere(query *gorm.DB, where models.AnalyticDisplayConfigWhere) {
	if len(where.AnalyticTypeCodes) > 0 {
		query.Where(`"uis_ANALYTICS".analytic_type_code IN ?`, where.AnalyticTypeCodes)
	} // AnalyticTypeCodes

}

func (r *AnalyticRepository) UpsertAnalyticDisplayConfig(ctx context.Context, dB database.DBI, analyticDisCon *[]models.AnalyticDisplayConfig) error {
	return dB.GetTx().Clauses(clause.OnConflict{
		Columns: []clause.Column{
			{Name: "analytic_code"},
			{Name: "user_id"},
			{Name: "client_id"},
		},
		DoUpdates: clause.AssignmentColumns([]string{
			"sequence",
			"is_active",
			"updated_at",
			"updated_by",
		}),
	}).Create(analyticDisCon).Error
}

func (r *AnalyticRepository) GetAnalyticDisplayConfigs(ctx context.Context, dB database.DBI, clientID string, userID string, where models.AnalyticDisplayConfigWhere, preload models.AnalyticDisplayConfigPreload) ([]models.AnalyticDisplayConfig, error) {
	analyticDisplayConfigs := []models.AnalyticDisplayConfig{}
	query := dB.GetOrm().Model(&analyticDisplayConfigs).
		Joins(`JOIN "uis_ANALYTICS" ON uis_analytic_display_configs.analytic_code = "uis_ANALYTICS".code`).
		Where("uis_analytic_display_configs.client_id = ?", clientID).
		Where("uis_analytic_display_configs.user_id = ?", userID)

	enrichAnalyticDisplayConfigQueryWithWhere(query, where)
	enrichAnalyticDisplayConfigQueryWithPreload(query, preload)

	query.Order("uis_analytic_display_configs.sequence ASC")
	err := query.Find(&analyticDisplayConfigs).Error
	if err != nil {
		return nil, err
	}

	return analyticDisplayConfigs, nil
}

func (r *AnalyticRepository) GetAnalytics(ctx context.Context, dB database.DBI, where models.AnalyticWhere) ([]models.Analytic, error) {
	analytics := []models.Analytic{}
	query := dB.GetOrm().Model(&analytics)

	enrichAnalyticQueryWithWhere(query, where)

	query.Order("sequence ASC")
	err := query.Find(&analytics).Error
	if err != nil {
		return nil, err
	}

	return analytics, nil
}
