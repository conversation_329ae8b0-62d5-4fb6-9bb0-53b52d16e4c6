package models

import "github.com/lib/pq"

type Analytic struct {
	Code                      string         `gorm:"type:varchar(40);primary_key" json:"code"`
	Label                     string         `gorm:"type:varchar(40);not null" json:"label"`
	Description               string         `gorm:"type:varchar(50);not null" json:"description"`
	Sequence                  int            `json:"sequence"`
	AnalyticTypeCode          string         `gorm:"type:varchar(40);not null" json:"analytic_type_code"`
	ShowOnClientPackageCodes  pq.StringArray `json:"show_on_client_package_codes" gorm:"column:show_on_client_package_codes;type:varchar(255)[]"`
	ShowOnUserPermissionCodes pq.StringArray `json:"show_on_user_permission_codes" gorm:"column:show_on_user_permission_codes;type:varchar(255)[]"`
}

func (Analytic) TableName() string {
	return "uis_ANALYTICS"
}

type AnalyticWhere struct {
	ExcludeAnalyticCode []string
	AnalyticTypeCodes   []string
}
