package usecase

import (
	assetConstants "assetfindr/internal/app/asset/constants"
	assetModels "assetfindr/internal/app/asset/models"
	notifConstants "assetfindr/internal/app/notification/constants"
	notificationDtos "assetfindr/internal/app/notification/dtos"
	"assetfindr/internal/app/task/constants"
	"assetfindr/internal/app/task/dtos"
	"assetfindr/internal/app/task/models"
	userIdentityModel "assetfindr/internal/app/user-identity/models"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/contexthelpers"
	"assetfindr/pkg/common/helpers/timehelpers"
	"assetfindr/pkg/common/helpers/tmplhelpers"
	"context"
	"fmt"
	"html/template"
	"time"

	"github.com/jackc/pgtype"
	"gopkg.in/guregu/null.v4"
)

func (uc *TicketUseCase) JobRemindTicketDueDate(ctx context.Context, req dtos.JobRemindTicketDueDateReq) (*commonmodel.CreateResponse, error) {
	dueDateInNext := time.Now().Add(7 * 24 * time.Hour)
	if req.TypeCode == "TOMORROW" {
		dueDateInNext = time.Now().Add(24 * time.Hour)
	} else if req.TypeCode == "TODAY" {
		dueDateInNext = time.Now()
	}
	tickets, err := uc.TicketRepository.GetTicketsV2(ctx, uc.DB.DB(), models.TicketCondition{
		Where: models.TicketWhere{
			DueDatetime: dueDateInNext,
			ExcludeStatusCodes: []string{
				constants.TICKET_STATUS_CODE_CLOSED,
				constants.TICKET_STATUS_CODE_CANCELED,
				constants.TICKET_STATUS_CODE_COMPLETED,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	if len(tickets) == 0 {
		return &commonmodel.CreateResponse{
			Success:     true,
			Message:     "Success",
			ReferenceID: "",
			Data:        nil,
		}, nil
	}

	assetIDs := make([]string, 0, len(tickets))
	clientIDs := make([]string, 0)
	userIDs := make([]string, 0)
	for i := range tickets {
		assetIDs = append(assetIDs, tickets[i].ReferenceID)
		clientIDs = append(clientIDs, tickets[i].ClientID)
		if tickets[i].AssignedToUserID.String != "" {
			userIDs = append(userIDs, tickets[i].AssignedToUserID.String)
		}
	}

	assets, err := uc.AssetRepository.GetAssets(ctx, uc.DB.DB(), assetModels.AssetCondition{
		Where: assetModels.AssetWhere{
			IDs: assetIDs,
		},
	})
	if err != nil {
		return nil, err
	}

	mapAssets := map[string]assetModels.Asset{}
	for i := range assets {
		mapAssets[assets[i].ID] = assets[i]
	}

	// get client
	mapClientAlias := map[string]string{}
	if len(clientIDs) > 0 {
		clients, err := uc.UserRepository.GetClients(ctx, uc.DB.DB(), userIdentityModel.ClientCondition{
			Where: userIdentityModel.ClientWhere{
				IDs: clientIDs,
			},
		})
		if err != nil {
			return nil, err
		}

		for i := range clients {
			mapClientAlias[clients[i].ID] = clients[i].ClientAlias
		}
	}

	//get user
	mapUserName := map[string]string{}
	if len(userIDs) > 0 {
		users, err := uc.UserRepository.GetUsersV2(ctx, uc.DB.DB(), userIdentityModel.UserCondition{
			Where: userIdentityModel.UserWhere{
				IDs: userIDs,
			},
		})
		if err != nil {
			return nil, err
		}

		for i := range users {
			mapUserName[users[i].ID] = users[i].GetName()
		}
	}

	loc, _ := time.LoadLocation("Asia/Jakarta")

	notifItems := make([]notificationDtos.CreateNotificationItem, 0, len(tickets))
	for i := range tickets {
		if tickets[i].AssignedToUserID.String == "" && tickets[i].RequesterUserID == "" {
			continue
		}

		asset, ok := mapAssets[tickets[i].ReferenceID]
		if !ok {
			continue
		}
		targetURL := uc.notifUseCase.GenerateTargetURL(ctx, mapClientAlias[tickets[i].ClientID], notifConstants.DESTINATION_TYPE_WORK_ORDER, tickets[i].ID)
		templ := tmplhelpers.TicketReminderDuedate{
			AssetIdent:           asset.GetAssetIdentForNotif(),
			AssetReferenceNumber: asset.ReferenceNumber,
			CustomerName:         asset.PartnerOwnerName,
			TicketCategoryCode:   tickets[i].TicketCategoryCode,
			TicketDescription:    tickets[i].Description,
			PriorityLevel:        tickets[i].SeverityLevelCode,
			Assignee:             mapUserName[tickets[i].AssignedToUserID.String],
			DueDateTime:          tickets[i].DueDatetime.In(loc).Format(timehelpers.RFC1123Notif),
			DueLabel:             "in a week",
			DueLabel2:            "in a week",
			RedirectLink:         template.URL(targetURL),
		}

		templ.Normalize()

		if req.TypeCode == "TOMORROW" {
			templ.DueLabel = "Tomorrow by " + tickets[i].DueDatetime.In(loc).Format("15:04")
			templ.DueLabel2 = "Tomorrow"
		} else if req.TypeCode == "TODAY" {
			templ.DueLabel = "Today by " + tickets[i].DueDatetime.In(loc).Format("15:04")
			templ.DueLabel2 = "Today"
		}

		item := notificationDtos.CreateNotificationItem{
			UserID:            tickets[i].RequesterUserID,
			SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_TICKET_UPDATE,
			SourceReferenceID: tickets[i].ID,
			TargetReferenceID: tickets[i].ReferenceID,
			TargetURL:         targetURL,
			MessageHeader:     templ.GenerateEmailSubject(),
			MessageBody:       templ.GenerateEmailBody(),
			ClientID:          tickets[i].ClientID,
			TypeCode:          notifConstants.NOTIFICATION_TYPE_REMINDER_CODE,
			MessageFirebase: notificationDtos.MessageFirebase{
				Title: templ.GeneratePushNotifSubject(),
				Body:  templ.GeneratePushNotifBody(),
			},
			ContentTypeCode: "",
			ReferenceCode:   notifConstants.NOTIF_REF_WORK_ORDER,
			ReferenceValue:  tickets[i].ID,
		}

		notifItems = append(notifItems, item)
		if tickets[i].AssignedToUserID.String != "" && tickets[i].RequesterUserID != tickets[i].AssignedToUserID.String {
			item.UserID = tickets[i].AssignedToUserID.String
			notifItems = append(notifItems, item)
		}
	}

	go func(notifItems []notificationDtos.CreateNotificationItem) {
		_ = uc.notifUseCase.CreateNotification(contexthelpers.WithoutCancel(ctx),
			notificationDtos.CreateNotificationReq{
				Items:           notifItems,
				SendToEmail:     true,
				SendToPushNotif: true,
			})
	}(notifItems)

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        nil,
	}, nil
}

func (uc *TicketUseCase) AutoCreateTicketOnReminderExpiry(ctx context.Context) error {
	assetComponents, err := uc.assetComponentRepo.GetAssetWithComponentExpiryReminderToday(ctx, uc.DB.DB())
	if err != nil {
		return err
	}

	if len(assetComponents) == 0 {
		return nil
	}

	clientIDs := []string{}
	assetIDs := []string{}
	mapAssetIDToAssetName := map[string]string{}
	for i := range assetComponents {
		assetIDs = append(assetIDs, assetComponents[i].AssetID)
		clientIDs = append(clientIDs, assetComponents[i].ClientID)
		mapAssetIDToAssetName[assetComponents[i].AssetID] = assetComponents[i].Asset.Name
	}

	assetAssignments, err := uc.assetAssignmentRepo.GetAssetAssignments(ctx, uc.DB.DB(), assetModels.AssetAssignmentCondition{
		Where: assetModels.AssetAssignmentWhere{
			AssetIDs: assetIDs,
			Assigned: true,
		},
	})
	if err != nil {
		return err
	}

	mapAssetIDToUserID := map[string]string{}
	userIDs := []string{}
	for _, assetAssignment := range assetAssignments {
		userIDs = append(userIDs, assetAssignment.UserID)
		mapAssetIDToUserID[assetAssignment.AssetID] = assetAssignment.UserID
	}

	mapUserNames := map[string]string{}
	mapUserClient := map[string]userIdentityModel.UserClient{}
	if len(userIDs) > 0 {
		users, err := uc.UserRepository.GetUsersV2(ctx, uc.DB.DB(), userIdentityModel.UserCondition{
			Where: userIdentityModel.UserWhere{
				IDs: userIDs,
			},
		})
		if err != nil {
			return nil
		}

		for _, user := range users {
			mapUserNames[user.ID] = user.GetName()
		}

		userClients, err := uc.UserRepository.GetUserClients(ctx, uc.DB.DB(), userIdentityModel.UserClientCondition{
			Where: userIdentityModel.UserClientWhere{
				UserIDs: userIDs,
			},
			Preload: userIdentityModel.UserClientPreload{Department: true},
		})
		if err != nil {
			return nil
		}

		for _, userClient := range userClients {
			mapUserClient[userClient.UserID] = userClient
		}
	}

	clients, err := uc.UserRepository.GetClients(ctx, uc.DB.DB(), userIdentityModel.ClientCondition{
		Where: userIdentityModel.ClientWhere{
			IDs: clientIDs,
		},
	})
	if err != nil {
		return nil
	}

	mapClientAlias := map[string]string{}
	for _, client := range clients {
		mapClientAlias[client.ID] = client.ClientAlias
	}

	for _, assetComponent := range assetComponents {
		// If not create Task
		if assetComponent.IsCreateTaskOnExpiry != nil && !assetComponent.IsCreateTaskOnExpiry.Bool {
			go uc.notifyAfterAutoCreateTicketOnAssetComponentAlmostExpiryDataNoTicket(
				contexthelpers.WithoutCancel(ctx),
				assetComponent, mapUserNames, mapClientAlias, mapAssetIDToUserID)
		}

		// Create Work Order
		if assetComponent.IsCreateWorkOrderOnExpiry != nil && assetComponent.IsCreateWorkOrderOnExpiry.Bool {
			dataInformation, err := uc.AssetRepository.GetAssetDataInformation(ctx, uc.DB.DB(), assetComponent.AssetID)
			if err != nil {
				return nil
			}

			ticket := models.Ticket{
				Subject: "Extend " + assetComponent.ComponentName + " Component",
				Description: fmt.Sprintf(`Please extend the component before the expiry date. 
Component: %s - %s
Expiry Date: %s
Component Assigned To: %s`,
					assetComponent.ComponentName,
					assetComponent.SerialNumber,
					assetComponent.ExpiryDate.Time.Format(time.DateOnly),
					mapUserNames[assetComponent.AssignedTo]),
				TicketCategoryCode:  constants.TICKET_CATEGORY_CODE_OTHERS,
				TicketReferenceCode: constants.TICKET_ASSET_VEHICLE_REF,
				ReferenceID:         assetComponent.AssetID,
				SeverityLevelCode:   constants.TICKET_SEVERITY_NOT_SET,
				RequesterUserID:     assetComponent.AssignedTo,
				AssignedToUserID:    null.StringFrom(assetComponent.AssignedTo),
				DepartmentID:        mapUserClient[assetComponent.AssignedTo].DepartmentID.String,
				StatusCode:          constants.TICKET_STATUS_CODE_ASSIGNED,
				Resolution:          "",
				ModelV2: commonmodel.ModelV2{
					CreatedBy: "system",
					UpdatedBy: "system",
					ClientID:  assetComponent.ClientID,
				},
				AssetDataInformation: pgtype.JSONB{Bytes: dataInformation, Status: pgtype.Present},
			}
			err = uc.TicketRepository.CreateTicket(ctx, uc.DB.DB(), &ticket)
			if err != nil {
				return err
			}

			go uc.notifyAfterAutoCreateTicketOnAssetComponentAlmostExpiryDataWithTicket(
				contexthelpers.WithoutCancel(ctx),
				assetComponent, ticket, mapUserNames, mapClientAlias, mapAssetIDToUserID)
		}

		// Create Task
		if assetComponent.IsCreateTaskOnExpiry != nil && assetComponent.IsCreateTaskOnExpiry.Bool {
			task := &models.Task{
				ModelV2: commonmodel.ModelV2{
					CreatedBy: assetComponent.AssignedTo,
					UpdatedBy: assetComponent.AssignedTo,
					ClientID:  assetComponent.ClientID,
				},
				Subject: "Extend " + assetComponent.ComponentName + " Component",
				Description: null.StringFrom(fmt.Sprintf(`Please extend the component before the expiry date. 
Component: %s - %s
Expiry Date: %s`, assetComponent.ComponentName, assetComponent.SerialNumber, assetComponent.ExpiryDate.Time.Format(timehelpers.DDMMYYYY))),
				ScheduleDatetime: null.NewTime(time.Time{}, false),
				StatusCode:       constants.TASK_STATUS_OPEN,
				AssetID:          assetComponent.AssetID,
			}
			err = uc.TicketRepository.CreateTask(ctx, uc.DB.DB(), task)
			if err != nil {
				return err
			}
			go uc.notifyAfterAutoCreateTicketOnAssetComponentAlmostExpiryDataWithTask(
				contexthelpers.WithoutCancel(ctx),
				assetComponent, task, mapUserNames, mapClientAlias)
		}

	}

	return nil

}

func (uc *TicketUseCase) AutoCreateTicketOnAssetTransactionReminderExpiry(ctx context.Context, trxType string) error {
	assetTransactions, err := uc.assetTransactionRepo.GetAssetWithTransactionExpiryReminderToday(ctx, uc.DB.DB(), assetModels.AssetTransactionWhere{
		Type: trxType,
	})
	if err != nil {
		return err
	}

	if len(assetTransactions) == 0 {
		return nil
	}

	clientIDs := []string{}
	assetIDs := []string{}
	mapAssetIDToAssetName := map[string]string{}
	for i := range assetTransactions {
		assetIDs = append(assetIDs, assetTransactions[i].AssetID)
		clientIDs = append(clientIDs, assetTransactions[i].ClientID)
		mapAssetIDToAssetName[assetTransactions[i].AssetID] = assetTransactions[i].Asset.Name
	}

	assetAssignments, err := uc.assetAssignmentRepo.GetAssetAssignments(ctx, uc.DB.DB(), assetModels.AssetAssignmentCondition{
		Where: assetModels.AssetAssignmentWhere{
			AssetIDs: assetIDs,
			Assigned: true,
		},
	})
	if err != nil {
		return err
	}

	mapAssetIDToUserID := map[string]string{}
	userIDs := []string{}
	for _, assetAssignment := range assetAssignments {
		userIDs = append(userIDs, assetAssignment.UserID)
		mapAssetIDToUserID[assetAssignment.AssetID] = assetAssignment.UserID
	}

	partnerIDs := []string{}
	for _, assetTransaction := range assetTransactions {
		userIDs = append(userIDs, assetTransaction.AssignedToUserID)
		partnerIDs = append(partnerIDs, assetTransaction.PartnerID)
	}

	mapUserNames := map[string]string{}
	mapUserClient := map[string]userIdentityModel.UserClient{}
	if len(userIDs) > 0 {
		users, err := uc.UserRepository.GetUsersV2(ctx, uc.DB.DB(), userIdentityModel.UserCondition{
			Where: userIdentityModel.UserWhere{
				IDs: userIDs,
			},
		})
		if err != nil {
			return nil
		}

		for _, user := range users {
			mapUserNames[user.ID] = user.GetName()
		}

		userClients, err := uc.UserRepository.GetUserClients(ctx, uc.DB.DB(), userIdentityModel.UserClientCondition{
			Where: userIdentityModel.UserClientWhere{
				UserIDs: userIDs,
			},
			Preload: userIdentityModel.UserClientPreload{Department: true},
		})
		if err != nil {
			return nil
		}

		for _, userClient := range userClients {
			mapUserClient[userClient.UserID] = userClient
		}
	}

	clients, err := uc.UserRepository.GetClients(ctx, uc.DB.DB(), userIdentityModel.ClientCondition{
		Where: userIdentityModel.ClientWhere{
			IDs: clientIDs,
		},
	})
	if err != nil {
		return nil
	}

	mapClientAlias := map[string]string{}
	for _, client := range clients {
		mapClientAlias[client.ID] = client.ClientAlias
	}

	mapPartnerName := map[string]string{}
	partners, err := uc.partnerRepo.GetPartners(ctx, uc.DB.DB(), userIdentityModel.PartnerCondition{
		Where: userIdentityModel.PartnerWhere{
			IDs: partnerIDs,
		},
	})
	if err != nil {
		return nil
	}

	for _, partner := range partners {
		mapPartnerName[partner.ID] = partner.Name
	}

	for _, assetTransaction := range assetTransactions {
		go uc.notifyAfterAutoCreateTicketOnAssetTransactionAlmostExpiryDataNoTicket(
			contexthelpers.WithoutCancel(ctx),
			assetTransaction, mapUserNames, mapClientAlias, mapAssetIDToUserID, mapPartnerName)

		// Create Work Order
		if assetTransaction.IsCreateWorkOrderOnExpiry != nil && assetTransaction.IsCreateWorkOrderOnExpiry.Bool {
			dataInformation, err := uc.AssetRepository.GetAssetDataInformation(ctx, uc.DB.DB(), assetTransaction.AssetID)
			if err != nil {
				return nil
			}

			subject := ""
			description := ""
			switch assetTransaction.TypeCode {
			case assetConstants.ASSET_TRANSACTION_TYPE_INSURANCE:
				subject = "Extend " + assetTransaction.ReferenceNumber + " Insurance"
				description = fmt.Sprintf(`Please extend the Insurance before the expiry date. 
					Insurance: %s - %s
					Expiry Date: %s
					Insurance Assigned To: %s`,
					assetTransaction.ReferenceNumber,
					mapPartnerName[assetTransaction.PartnerID],
					assetTransaction.ServiceEndDate.Format(time.DateOnly),
					mapUserNames[assetTransaction.AssignedToUserID])
			case assetConstants.ASSET_TRANSACTION_TYPE_WARRANTY:
				subject = "Extend " + assetTransaction.ReferenceNumber + " Warranty"
				description = fmt.Sprintf(`Please extend the warranty before the expiry date. 
					Warranty: %s - %s
					Expiry Date: %s
					Warranty Assigned To: %s`,
					assetTransaction.ReferenceNumber,
					mapPartnerName[assetTransaction.PartnerID],
					assetTransaction.ServiceEndDate.Format(time.DateOnly),
					mapUserNames[assetTransaction.AssignedToUserID])
			}

			ticket := models.Ticket{
				Subject:             subject,
				Description:         description,
				TicketCategoryCode:  constants.TICKET_CATEGORY_CODE_OTHERS,
				TicketReferenceCode: constants.TICKET_ASSET_VEHICLE_REF, // NEED TO CONFIRM
				ReferenceID:         assetTransaction.AssetID,
				SeverityLevelCode:   constants.TICKET_SEVERITY_NOT_SET,
				RequesterUserID:     assetTransaction.AssignedToUserID,
				AssignedToUserID:    null.StringFrom(assetTransaction.AssignedToUserID),
				DepartmentID:        mapUserClient[assetTransaction.AssignedToUserID].DepartmentID.String,
				StatusCode:          constants.TICKET_STATUS_CODE_ASSIGNED,
				Resolution:          "",
				ModelV2: commonmodel.ModelV2{
					CreatedBy: "system",
					UpdatedBy: "system",
					ClientID:  assetTransaction.ClientID,
				},
				AssetDataInformation: pgtype.JSONB{Bytes: dataInformation, Status: pgtype.Present},
			}
			err = uc.TicketRepository.CreateTicket(ctx, uc.DB.DB(), &ticket)
			if err != nil {
				return err
			}

			go uc.notifyAfterAutoCreateTicketOnAssetTransactionAlmostExpiryDataWithTicket(
				contexthelpers.WithoutCancel(ctx),
				assetTransaction, ticket, mapUserNames, mapClientAlias, mapAssetIDToUserID)
		}
	}

	return nil

}

func (uc *TicketUseCase) notifyAfterAutoCreateTicketOnAssetComponentAlmostExpiryDataNoTicket(
	ctx context.Context, assetComponent assetModels.AssetComponent, mapUserName map[string]string, mapClientAlias map[string]string, mapAssetIDToUserID map[string]string,
) {
	assetLink := template.URL(
		uc.notifUseCase.GenerateTargetURL(ctx, mapClientAlias[assetComponent.ClientID], notifConstants.DESTINATION_TYPE_ASSET, assetComponent.AssetID),
	)
	tmplAssetReminderComponentNoTicket := tmplhelpers.AssetComponentReminderNoAction{
		AssetIdent:            assetComponent.Asset.GetAssetIdentForNotif(),
		ComponentName:         assetComponent.ComponentName,
		ComponentSerialNumber: assetComponent.SerialNumber,
		ComponentExpiryDate:   assetComponent.ExpiryDate.Time.Format(timehelpers.DDMMYYYY),
		ComponentAssignedTo:   mapUserName[assetComponent.AssignedTo],
		RedirectLink:          assetLink,
		AssetAssignee:         mapUserName[mapAssetIDToUserID[assetComponent.AssetID]],
	}

	notifItem := notificationDtos.CreateNotificationItem{
		UserID:            assetComponent.AssignedTo,
		SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_ASSET_COMPONENT,
		SourceReferenceID: assetComponent.AssetID,
		TargetReferenceID: "",
		TargetURL:         string(assetLink),
		MessageHeader:     tmplAssetReminderComponentNoTicket.GenerateEmailSubject(),
		MessageBody:       tmplAssetReminderComponentNoTicket.GenerateEmailBody(),
		MessageFirebase: notificationDtos.MessageFirebase{
			Title: tmplAssetReminderComponentNoTicket.GeneratePushNotifSubject(),
			Body:  tmplAssetReminderComponentNoTicket.GeneratePushNotifBody(),
		},
		ClientID:        assetComponent.ClientID,
		TypeCode:        notifConstants.NOTIFICATION_TYPE_REMINDER_CODE,
		ContentTypeCode: "",
		ReferenceCode:   notifConstants.NOTIF_REF_ASSET,
		ReferenceValue:  assetComponent.AssetID,
	}

	notifItems := []notificationDtos.CreateNotificationItem{notifItem}

	if assetAssignee, ok := mapAssetIDToUserID[assetComponent.AssetID]; ok {
		if assetAssignee != assetComponent.AssignedTo {
			notifItem.UserID = assetAssignee
			notifItems = append(notifItems, notifItem)
		}
	}

	_ = uc.notifUseCase.CreateNotification(ctx, notificationDtos.CreateNotificationReq{
		Items:           notifItems,
		SendToEmail:     true,
		SendToPushNotif: true,
	})
}

func (uc *TicketUseCase) notifyAfterAutoCreateTicketOnAssetTransactionAlmostExpiryDataNoTicket(
	ctx context.Context, assetTransaction assetModels.AssetTransaction, mapUserName map[string]string, mapClientAlias map[string]string, mapAssetIDToUserID map[string]string, mapPartnerName map[string]string,
) {
	assetLink := template.URL(
		uc.notifUseCase.GenerateTargetURL(ctx, mapClientAlias[assetTransaction.ClientID], notifConstants.DESTINATION_TYPE_ASSET, assetTransaction.AssetID),
	)

	typeLabel := ""
	switch assetTransaction.TypeCode {
	case assetConstants.ASSET_TRANSACTION_TYPE_INSURANCE:
		typeLabel = "Insurance"
	case assetConstants.ASSET_TRANSACTION_TYPE_WARRANTY:
		typeLabel = "Warranty"
	}

	tmplAssetReminderComponentNoTicket := tmplhelpers.AssetTransactionReminderNoAction{
		AssetIdent:                 assetTransaction.Asset.GetAssetIdentForNotif(),
		AssetTransactionExpiryDate: assetTransaction.ServiceEndDate.Format(timehelpers.DDMMYYYY),
		AssetTransactionAssignedTo: mapUserName[assetTransaction.AssignedToUserID],
		RedirectLink:               assetLink,
		AssetAssignee:              mapUserName[mapAssetIDToUserID[assetTransaction.AssetID]],
		ReferenceNumber:            assetTransaction.ReferenceNumber,
		PartnerName:                mapPartnerName[assetTransaction.PartnerID],
		TypeLabel:                  typeLabel,
		PurchaseOrderNumber:        assetTransaction.PurchaseOrderNumber.String,
	}

	notifItem := notificationDtos.CreateNotificationItem{
		UserID:            assetTransaction.AssignedToUserID,
		SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_ASSET_TRANSACTION,
		SourceReferenceID: assetTransaction.AssetID,
		TargetReferenceID: "",
		TargetURL:         string(assetLink),
		MessageHeader:     tmplAssetReminderComponentNoTicket.GenerateEmailSubject(),
		MessageBody:       tmplAssetReminderComponentNoTicket.GenerateEmailBody(),
		MessageFirebase: notificationDtos.MessageFirebase{
			Title: tmplAssetReminderComponentNoTicket.GeneratePushNotifSubject(),
			Body:  tmplAssetReminderComponentNoTicket.GeneratePushNotifBody(),
		},
		ClientID:        assetTransaction.ClientID,
		TypeCode:        notifConstants.NOTIFICATION_TYPE_REMINDER_CODE,
		ContentTypeCode: "",
		ReferenceCode:   notifConstants.NOTIF_REF_ASSET,
		ReferenceValue:  assetTransaction.AssetID,
	}

	notifItems := []notificationDtos.CreateNotificationItem{notifItem}

	if assetAssignee, ok := mapAssetIDToUserID[assetTransaction.AssetID]; ok {
		if assetAssignee != assetTransaction.AssignedToUserID {
			notifItem.UserID = assetAssignee
			notifItems = append(notifItems, notifItem)
		}
	}

	_ = uc.notifUseCase.CreateNotification(ctx, notificationDtos.CreateNotificationReq{
		Items:           notifItems,
		SendToEmail:     true,
		SendToPushNotif: true,
	})
}

func (uc *TicketUseCase) notifyAfterAutoCreateTicketOnAssetComponentAlmostExpiryDataWithTicket(
	ctx context.Context, assetComponent assetModels.AssetComponent, ticket models.Ticket, mapUserName map[string]string, mapClientAlias map[string]string, mapAssetIDToUserID map[string]string,
) {
	ticketLink := template.URL(
		uc.notifUseCase.GenerateTargetURL(ctx, mapClientAlias[assetComponent.ClientID], notifConstants.DESTINATION_TYPE_WORK_ORDER, ticket.ID),
	)

	tmpl := tmplhelpers.CreateTicketBody{
		Subject:        ticket.Subject,
		AssetName:      assetComponent.Asset.GetAssetIdentForNotif(),
		UserAssigned:   mapUserName[ticket.AssignedToUserID.String],
		RedirectWOLink: ticketLink,
		SeverityLevel:  constants.MapTicketSeverityLabel[ticket.SeverityLevelCode],
		TicketDesc:     ticket.Description,
	}

	notifItem := notificationDtos.CreateNotificationItem{
		SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_TICKET_ASSIGNMENT,
		SourceReferenceID: ticket.ID,
		TargetReferenceID: ticket.ReferenceID,
		TargetURL:         string(ticketLink),
		MessageHeader:     tmpl.ConstructTitleEmail(),
		MessageBody:       tmpl.ConstructBodyEmail(),
		MessageFirebase: notificationDtos.MessageFirebase{
			Title: tmpl.ConstructTitlePushNotif(),
			Body:  tmpl.ConstructBodyPushNotif(),
		},
		ClientID:        ticket.ClientID,
		TypeCode:        "",
		ContentTypeCode: "",
		ReferenceCode:   notifConstants.NOTIF_REF_WORK_ORDER,
		ReferenceValue:  ticket.ID,
		UserID:          assetComponent.AssignedTo,
	}

	if assetAssignee, ok := mapAssetIDToUserID[assetComponent.AssetID]; ok {
		if assetAssignee != assetComponent.AssignedTo {
			notifItem.UserID = assetAssignee
		}
	}

	notifItems := []notificationDtos.CreateNotificationItem{notifItem}

	if assetAssignee, ok := mapAssetIDToUserID[assetComponent.AssetID]; ok {
		if assetAssignee != assetComponent.AssignedTo {
			notifItem.UserID = assetAssignee
			notifItems = append(notifItems, notifItem)
		}
	}

	_ = uc.notifUseCase.CreateNotification(ctx, notificationDtos.CreateNotificationReq{
		Items:           notifItems,
		SendToEmail:     true,
		SendToPushNotif: true,
	})
}

func (uc *TicketUseCase) notifyAfterAutoCreateTicketOnAssetTransactionAlmostExpiryDataWithTicket(
	ctx context.Context, assetTransaction assetModels.AssetTransaction, ticket models.Ticket, mapUserName map[string]string, mapClientAlias map[string]string, mapAssetIDToUserID map[string]string,
) {
	ticketLink := template.URL(
		uc.notifUseCase.GenerateTargetURL(ctx, mapClientAlias[assetTransaction.AssetID], notifConstants.DESTINATION_TYPE_WORK_ORDER, ticket.ID),
	)

	tmpl := tmplhelpers.CreateTicketBody{
		Subject:        ticket.Subject,
		AssetName:      assetTransaction.Asset.GetAssetIdentForNotif(),
		UserAssigned:   mapUserName[ticket.AssignedToUserID.String],
		RedirectWOLink: ticketLink,
		SeverityLevel:  constants.MapTicketSeverityLabel[ticket.SeverityLevelCode],
		TicketDesc:     ticket.Description,
	}

	notifItem := notificationDtos.CreateNotificationItem{
		SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_TICKET_ASSIGNMENT,
		SourceReferenceID: ticket.ID,
		TargetReferenceID: ticket.ReferenceID,
		TargetURL:         string(ticketLink),
		MessageHeader:     tmpl.ConstructTitleEmail(),
		MessageBody:       tmpl.ConstructBodyEmail(),
		MessageFirebase: notificationDtos.MessageFirebase{
			Title: tmpl.ConstructTitlePushNotif(),
			Body:  tmpl.ConstructBodyPushNotif(),
		},
		ClientID:        ticket.ClientID,
		TypeCode:        "",
		ContentTypeCode: "",
		ReferenceCode:   notifConstants.NOTIF_REF_WORK_ORDER,
		ReferenceValue:  ticket.ID,
		UserID:          assetTransaction.AssignedToUserID,
	}

	notifItems := []notificationDtos.CreateNotificationItem{notifItem}

	if assetAssignee, ok := mapAssetIDToUserID[assetTransaction.AssetID]; ok {
		if assetAssignee != assetTransaction.AssignedToUserID {
			notifItem.UserID = assetAssignee
			notifItems = append(notifItems, notifItem)
		}
	}

	_ = uc.notifUseCase.CreateNotification(ctx, notificationDtos.CreateNotificationReq{
		Items:           notifItems,
		SendToEmail:     true,
		SendToPushNotif: true,
	})
}

func (uc *TicketUseCase) notifyAfterAutoCreateTicketOnAssetComponentAlmostExpiryDataWithTask(
	ctx context.Context, assetComponent assetModels.AssetComponent, task *models.Task, mapUserName map[string]string, mapClientAlias map[string]string,
) {
	assetLink := template.URL(
		uc.notifUseCase.GenerateTargetURL(ctx, mapClientAlias[assetComponent.ClientID], notifConstants.DESTINATION_TYPE_ASSET, assetComponent.AssetID),
	)

	ticketLink := template.URL(
		uc.notifUseCase.GenerateTargetURL(ctx, mapClientAlias[assetComponent.ClientID], notifConstants.DESTINATION_TYPE_WORK_ORDER, task.ID),
	)

	assetNo := assetComponent.Asset.SerialNumber
	customerName := ""
	if assetComponent.Asset.ReferenceNumber != "" {
		assetNo = assetComponent.Asset.ReferenceNumber
	}
	if assetComponent.Asset.PartnerOwnerName != "" {
		customerName = assetComponent.Asset.PartnerOwnerName
	}
	tmpl := tmplhelpers.AssetComponentReminderWithTask{
		AssetComponentReminderNoAction: tmplhelpers.AssetComponentReminderNoAction{
			AssetIdent:            assetComponent.Asset.GetAssetIdentForNotif(),
			ComponentName:         assetComponent.ComponentName,
			ComponentSerialNumber: assetComponent.SerialNumber,
			ComponentExpiryDate:   assetComponent.ExpiryDate.Time.Format(timehelpers.DDMMYYYY),
			ComponentAssignedTo:   mapUserName[assetComponent.AssignedTo],
			RedirectLink:          assetLink,
		},
		RedirectLinkTicket: ticketLink,
		TaskSubject:        task.Subject,
		TaskDescription:    task.Description.String,
		AssetNo:            assetNo,
		CustomerName:       customerName,
	}

	_ = uc.notifUseCase.CreateNotification(ctx, notificationDtos.CreateNotificationReq{
		Items: []notificationDtos.CreateNotificationItem{
			{
				UserID:            assetComponent.AssignedTo,
				SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_WORK_ORDER,
				SourceReferenceID: assetComponent.AssetID,
				TargetReferenceID: "",
				TargetURL:         string(assetLink),
				MessageHeader:     tmpl.GenerateEmailSubject(),
				MessageBody:       tmpl.GenerateEmailBody(),
				MessageFirebase: notificationDtos.MessageFirebase{
					Title: tmpl.GeneratePushNotifSubject(),
					Body:  tmpl.GeneratePushNotifBody(),
				},
				ClientID:        assetComponent.ClientID,
				TypeCode:        notifConstants.NOTIFICATION_TYPE_REMINDER_CODE,
				ContentTypeCode: "",
				ReferenceCode:   notifConstants.NOTIF_REF_TASK,
				ReferenceValue:  task.ID,
			},
		},
		SendToEmail:     true,
		SendToPushNotif: true,
	})
}

func (uc *TicketUseCase) JobRemindTicketSchedule(ctx context.Context) (*commonmodel.CreateResponse, error) {
	inNext3Days := time.Now().Add(3 * 24 * time.Hour)
	tickets, err := uc.TicketRepository.GetTicketsV2(ctx, uc.DB.DB(), models.TicketCondition{
		Where: models.TicketWhere{
			ScheduleDatetime: inNext3Days,
			ExcludeStatusCodes: []string{
				constants.TICKET_STATUS_CODE_CLOSED,
				constants.TICKET_STATUS_CODE_CANCELED,
				constants.TICKET_STATUS_CODE_COMPLETED,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	if len(tickets) == 0 {
		return &commonmodel.CreateResponse{
			Success:     true,
			Message:     "Success",
			ReferenceID: "",
			Data:        nil,
		}, nil
	}

	assetIDs := make([]string, 0, len(tickets))
	clientIDs := make([]string, 0)
	userIDs := make([]string, 0)
	for i := range tickets {
		assetIDs = append(assetIDs, tickets[i].ReferenceID)
		clientIDs = append(clientIDs, tickets[i].ClientID)
		if tickets[i].AssignedToUserID.String != "" {
			userIDs = append(userIDs, tickets[i].AssignedToUserID.String)
		}
	}

	assets, err := uc.AssetRepository.GetAssets(ctx, uc.DB.DB(), assetModels.AssetCondition{
		Where: assetModels.AssetWhere{
			IDs: assetIDs,
		},
	})
	if err != nil {
		return nil, err
	}

	mapAssets := map[string]assetModels.Asset{}
	for i := range assets {
		mapAssets[assets[i].ID] = assets[i]
	}

	// get client
	mapClientAlias := map[string]string{}
	if len(clientIDs) > 0 {
		clients, err := uc.UserRepository.GetClients(ctx, uc.DB.DB(), userIdentityModel.ClientCondition{
			Where: userIdentityModel.ClientWhere{
				IDs: clientIDs,
			},
		})
		if err != nil {
			return nil, err
		}

		for i := range clients {
			mapClientAlias[clients[i].ID] = clients[i].ClientAlias
		}
	}

	//get user
	mapUserName := map[string]string{}
	if len(userIDs) > 0 {
		users, err := uc.UserRepository.GetUsersV2(ctx, uc.DB.DB(), userIdentityModel.UserCondition{
			Where: userIdentityModel.UserWhere{
				IDs: userIDs,
			},
		})
		if err != nil {
			return nil, err
		}

		for i := range users {
			mapUserName[users[i].ID] = users[i].GetName()
		}
	}

	loc, _ := time.LoadLocation("Asia/Jakarta")

	notifItems := make([]notificationDtos.CreateNotificationItem, 0, len(tickets))
	for i := range tickets {
		if tickets[i].AssignedToUserID.String == "" && tickets[i].RequesterUserID == "" {
			continue
		}

		asset, ok := mapAssets[tickets[i].ReferenceID]
		if !ok {
			continue
		}
		users, err := uc.UserRepository.GetUser(ctx, uc.DB.DB(), userIdentityModel.UserCondition{
			Where: userIdentityModel.UserWhere{
				ID: tickets[i].RequesterUserID,
			},
		})
		if err != nil {
			return nil, err
		}

		targetURL := uc.notifUseCase.GenerateTargetURL(ctx, mapClientAlias[tickets[i].ClientID], notifConstants.DESTINATION_TYPE_WORK_ORDER, tickets[i].ID)
		templ := tmplhelpers.TicketReminderSchedule{
			AssetIdent:           asset.GetAssetIdentForNotif(),
			AssetReferenceNumber: asset.ReferenceNumber,
			CustomerName:         asset.PartnerOwnerName,
			RequestedBy:          users.GetName(),
			TicketCategoryCode:   tickets[i].TicketCategoryCode,
			TicketDescription:    tickets[i].Description,
			PriorityLevel:        tickets[i].SeverityLevelCode,
			Assignee:             mapUserName[tickets[i].AssignedToUserID.String],
			ScheduledDateTime:    tickets[i].ScheduleDatetime.In(loc).Format(timehelpers.RFC1123Notif),
			CreatedDateTime:      tickets[i].CreatedAt.In(loc).Format(timehelpers.RFC1123Notif),
			RedirectLink:         template.URL(targetURL),
			IsFromWorkshop:       tickets[i].TicketCategoryCode == constants.TICKET_CATEGORY_CODE_WORKSHOP_SERVICE,
		}
		templ.Normalize()

		item := notificationDtos.CreateNotificationItem{
			UserID:            tickets[i].RequesterUserID,
			SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_TICKET_UPDATE,
			SourceReferenceID: tickets[i].ID,
			TargetReferenceID: tickets[i].ReferenceID,
			TargetURL:         targetURL,
			MessageHeader:     templ.GenerateEmailSubject(),
			MessageBody:       templ.GenerateEmailBody(),
			ClientID:          tickets[i].ClientID,
			TypeCode:          notifConstants.NOTIFICATION_TYPE_REMINDER_CODE,
			MessageFirebase: notificationDtos.MessageFirebase{
				Title: templ.GeneratePushNotifSubject(),
				Body:  templ.GeneratePushNotifBody(),
			},
			ContentTypeCode: "",
			ReferenceCode:   notifConstants.NOTIF_REF_WORK_ORDER,
			ReferenceValue:  tickets[i].ID,
		}

		notifItems = append(notifItems, item)
		// if tickets[i].AssignedToUserID != "" && tickets[i].RequesterUserID != tickets[i].AssignedToUserID {
		// 	item.UserID = tickets[i].AssignedToUserID
		// 	notifItems = append(notifItems, item)
		// }
	}

	go func(notifItems []notificationDtos.CreateNotificationItem) {
		_ = uc.notifUseCase.CreateNotification(contexthelpers.WithoutCancel(ctx),
			notificationDtos.CreateNotificationReq{
				Items:           notifItems,
				SendToEmail:     true,
				SendToPushNotif: true,
			})
	}(notifItems)

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        nil,
	}, nil
}
