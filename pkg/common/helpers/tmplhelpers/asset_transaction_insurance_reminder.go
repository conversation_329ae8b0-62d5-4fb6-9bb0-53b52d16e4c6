package tmplhelpers

import (
	"html/template"
)

type AssetTransactionReminderNoAction struct {
	AssetIdent, AssetTransactionExpiryDate, AssetTransactionAssignedTo, AssetAssignee, ReferenceNumber, PartnerName, TypeLabel, PurchaseOrderNumber string
	RedirectLink                                                                                                                                    template.URL
}

func (a AssetTransactionReminderNoAction) GenerateEmailSubject() string {
	title, _ := ParseStringTemplate(
		"[Asset] Reminder: Upcoming Expiry for {{.PurchaseOrderNumber}} of {{.AssetIdent}}", a)

	return title
}

func (a AssetTransactionReminderNoAction) GenerateEmailBody() string {
	body, _ := ParseStringTemplate(
		`Dear {{ .AssetAssignee }},
		<br>
		<br>
		This is a friendly reminder that a {{.TypeLabel}} of your asset is expiring soon. Here are the details:
		<br>
		<br>
		<table>
			<tr><td>Asset</td>: <td> {{.AssetIdent}}</td></tr>
			<tr><td>{{.TypeLabel}}</td>: <td> {{.PurchaseOrderNumber}} - {{.PartnerName}}</td></tr>
			<tr><td>Expiry Date</td>: <td> {{.AssetTransactionExpiryDate}}</td></tr>
			<tr><td>{{.TypeLabel}} Assigned To</td>: <td> {{.AssetTransactionAssignedTo}}</td></tr>
			<tr><td>Asset Assigned To</td>: <td> {{.AssetAssignee}}</td></tr>
		</table><br>

		For more details about the asset, please click below:
		<br><br>
		<a href = "{{.RedirectLink}}">
		<button style="background-color: #165FFF; color: white; border: none; border-radius: 5px; padding: 10px 20px; cursor: pointer;">
		View Asset
		</button>
		</a>
		<br>`, a)

	return body
}

func (a AssetTransactionReminderNoAction) GeneratePushNotifSubject() string {
	title, _ := ParseStringTemplate(
		"{{.TypeLabel}} Expiring Soon", a)

	return title
}

func (a AssetTransactionReminderNoAction) GeneratePushNotifBody() string {
	body, _ := ParseStringTemplate(
		`The {{.TypeLabel}} {{.PurchaseOrderNumber}} on {{.AssetIdent}} is expiring on {{.AssetTransactionExpiryDate}}.`, a)

	return body
}
