package helpers

import "fmt"

func AddSuffixInvalid(s string) string {
	return s + " (invalid)"
}

func AddSuffixDuplicate(s string) string {
	return s + " (duplicate or already exist)"
}

func TruncateMiddle(text string, maxLength int) string {
	if len(text) <= maxLength {
		return text
	}

	half := maxLength / 2
	start := text[:half]
	end := text[len(text)-half:]

	return fmt.Sprintf("%s...%s", start, end)
}

func TruncateEnd(text string, maxLength int) string {
	if len(text) <= maxLength {
		return text
	}

	truncatedText := text[:maxLength-1]

	return fmt.Sprintf("%s...", truncatedText)
}

func StringOrDefaultIfEmpty(text string, defaultVal string) string {
	if text == "" {
		return defaultVal
	}

	return text
}

func FormatDashedStringDisplay(firstPart, secondPart, thirdPart string) string {
	var parts []string

	// Add available data to the 'parts' slice in order of priority.
	if firstPart != "" {
		parts = append(parts, firstPart)
	}
	if secondPart != "" {
		parts = append(parts, secondPart)
	}
	if thirdPart != "" {
		parts = append(parts, thirdPart)
	}

	// Construct the final string based on how many parts were found.
	switch len(parts) {
	case 0:
		return "" // No data available
	case 1:
		return parts[0] // Only one piece of data was found
	default: // 2 or more pieces of data were found
		return fmt.Sprintf("%s - %s", parts[0], parts[1])
	}
}
