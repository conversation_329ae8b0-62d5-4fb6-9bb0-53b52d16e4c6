package httphelpers_test

import (
	"assetfindr/pkg/common/helpers/httphelpers"
	"net/http"
	"testing"
)

func TestNewClientAllowRedirectAuth(t *testing.T) {
	httpCLient := httphelpers.NewClientAllowRedirectAuth()
	req, err := http.NewRequest(http.MethodPost, "http://localhost:8000/test-redirect", nil)
	if err != nil {
		t.Fatalf("failed to create request: %v", err)
	}

	req.Header.Add("Authorization", "Bearer ABCDEFGHIJKLMNOPQRSTUVWXYZ")

	resp, err := httpCLient.Do(req)
	if err != nil {
		t.Fatalf("failed to do request: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		t.Errorf("expected status code %d, got %d", http.StatusOK, resp.StatusCode)
	}
}
