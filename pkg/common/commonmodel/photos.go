package commonmodel

import (
	"assetfindr/internal/app/storage/constants"
	"strings"

	"gopkg.in/guregu/null.v4"
)

type PhotoReq struct {
	ID         string      `json:"id"`
	Label      null.String `json:"label"`
	Path       string      `json:"path"`
	IsDelete   bool        `json:"is_delete"`
	CopyFromID string      `json:"copy_from_id"`
}

func (d PhotoReq) IsNew() bool {
	if d.ID == "" && d.CopyFromID == "" {
		return true
	}

	return false
}

func (d PhotoReq) IsCopyFromExisting() bool {
	if d.ID == "" && d.CopyFromID != "" {
		return true
	}

	return false
}

func IsInTempFileLoc(name string) bool {
	return strings.HasPrefix(name, constants.TEMP_USER_UPLOAD_PREFIX)
}
